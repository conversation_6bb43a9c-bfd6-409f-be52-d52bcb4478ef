#!/usr/bin/env python3
"""
Setup admin user for the version management system.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_path = Path(__file__).parent / "report-card-app" / "backend"
sys.path.insert(0, str(backend_path))

async def setup_admin():
    """Setup admin user and test login."""
    try:
        from app.database.connection import init_database, get_database
        from app.database.models import User
        from app.utils.security import hash_password, verify_password
        from sqlalchemy import select
        
        await init_database()
        print('✅ Database initialized')
        
        async for session in get_database():
            try:
                # Check if admin exists
                result = await session.execute(select(User).where(User.username == 'admin'))
                admin = result.scalar_one_or_none()
                
                if not admin:
                    # Create admin user
                    admin = User(
                        username='admin',
                        email='<EMAIL>',
                        hashed_password=hash_password('admin123'),
                        full_name='System Administrator',
                        role='admin',
                        is_active=True
                    )
                    session.add(admin)
                    await session.commit()
                    await session.refresh(admin)
                    print('✅ Created admin user: admin / admin123')
                else:
                    print(f'✅ Admin user exists: {admin.username}')
                
                print(f'   Email: {admin.email}')
                print(f'   Active: {admin.is_active}')
                print(f'   Role: {admin.role}')
                
                # Test password verification
                if verify_password('admin123', admin.hashed_password):
                    print('✅ Password verification works')
                else:
                    print('❌ Password verification failed')
                    
            except Exception as e:
                print(f'❌ Database error: {e}')
                await session.rollback()
            finally:
                break
                
    except ImportError as e:
        print(f'❌ Import error: {e}')
    except Exception as e:
        print(f'❌ Setup error: {e}')

if __name__ == "__main__":
    asyncio.run(setup_admin())
