#!/bin/bash

# Report Card Frontend Setup Script
# Sets up the React + Vite frontend environment

set -e  # Exit on any error

echo "⚛️  Report Card Frontend Setup"
echo "============================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Check if we're in the frontend directory
if [ ! -f "package.json" ] || [ ! -f "vite.config.js" ]; then
    print_error "Please run this script from the frontend directory (report-card-app)"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_step "Checking prerequisites..."

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js found: $NODE_VERSION ✓"
else
    print_error "Node.js is required but not found. Please install Node.js 18 or higher."
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "npm found: $NPM_VERSION ✓"
else
    print_error "npm is required but not found. Please install npm."
    exit 1
fi

# Check for yarn (optional)
if command_exists yarn; then
    YARN_VERSION=$(yarn --version)
    print_success "Yarn found: $YARN_VERSION ✓ (optional)"
    PACKAGE_MANAGER="yarn"
else
    print_status "Yarn not found - using npm"
    PACKAGE_MANAGER="npm"
fi

print_success "All prerequisites satisfied!"
echo

# Install dependencies
print_step "Installing Node.js dependencies..."

if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    print_status "Installing dependencies with Yarn..."
    yarn install
    print_success "Dependencies installed with Yarn ✓"
else
    print_status "Installing dependencies with npm..."
    npm install
    print_success "Dependencies installed with npm ✓"
fi

# Create .env file if it doesn't exist
print_step "Setting up environment configuration..."

if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    
    cat > .env << EOF
# ===================================
# FRONTEND ENVIRONMENT CONFIGURATION
# ===================================

# Backend API URL
VITE_API_URL=http://localhost:8000

# Application Configuration
VITE_APP_TITLE=Report Card System
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Modern Report Card Generation System

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK_DATA=false

# UI Configuration
VITE_THEME=default
VITE_LANGUAGE=en
VITE_TIMEZONE=America/New_York

# Development Settings
VITE_DEV_TOOLS=true
VITE_HOT_RELOAD=true

# API Configuration
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# File Upload Settings
VITE_MAX_FILE_SIZE=10485760  # 10MB
VITE_ALLOWED_FILE_TYPES=.docx,.pdf,.jpg,.png

# Performance Settings
VITE_ENABLE_SERVICE_WORKER=false
VITE_ENABLE_OFFLINE_MODE=false
EOF
    print_success ".env file created ✓"
else
    print_warning ".env file already exists - skipping creation"
fi

# Build the application
print_step "Building the application..."

if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    print_status "Building with Yarn..."
    yarn build
    print_success "Application built with Yarn ✓"
else
    print_status "Building with npm..."
    npm run build
    print_success "Application built with npm ✓"
fi

# Run linting if available
print_step "Running code quality checks..."

if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    if yarn lint 2>/dev/null; then
        print_success "Linting passed ✓"
    else
        print_warning "Linting issues found - run 'yarn lint' to see details"
    fi
else
    if npm run lint 2>/dev/null; then
        print_success "Linting passed ✓"
    else
        print_warning "Linting issues found - run 'npm run lint' to see details"
    fi
fi

# Create convenience scripts
print_step "Creating convenience scripts..."

# Create start script
cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Report Card Frontend"
echo "================================"

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run from the frontend directory."
    exit 1
fi

# Start development server
echo "Starting Vite development server..."
if command -v yarn &> /dev/null; then
    echo "Using Yarn..."
    yarn dev
else
    echo "Using npm..."
    npm run dev
fi
EOF

chmod +x start.sh
print_success "Start script created ✓"

# Create build script
cat > build.sh << 'EOF'
#!/bin/bash
echo "🏗️  Building Report Card Frontend"
echo "================================="

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run from the frontend directory."
    exit 1
fi

# Build for production
echo "Building for production..."
if command -v yarn &> /dev/null; then
    echo "Using Yarn..."
    yarn build
else
    echo "Using npm..."
    npm run build
fi

echo ""
echo "✅ Build completed!"
echo "📁 Output directory: dist/"
echo "🌐 Ready for deployment"
EOF

chmod +x build.sh
print_success "Build script created ✓"

# Create preview script
cat > preview.sh << 'EOF'
#!/bin/bash
echo "👀 Previewing Report Card Frontend"
echo "=================================="

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "Error: dist/ directory not found. Please run build first."
    echo "Run: ./build.sh or npm run build"
    exit 1
fi

# Preview production build
echo "Starting preview server..."
if command -v yarn &> /dev/null; then
    echo "Using Yarn..."
    yarn preview
else
    echo "Using npm..."
    npm run preview
fi
EOF

chmod +x preview.sh
print_success "Preview script created ✓"

# Create test script (if tests are configured)
cat > test.sh << 'EOF'
#!/bin/bash
echo "🧪 Running Frontend Tests"
echo "========================="

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run from the frontend directory."
    exit 1
fi

# Check if test script exists in package.json
if grep -q '"test"' package.json; then
    echo "Running test suite..."
    if command -v yarn &> /dev/null; then
        echo "Using Yarn..."
        yarn test
    else
        echo "Using npm..."
        npm test
    fi
else
    echo "No test script found in package.json"
    echo "Tests may not be configured for this project"
fi
EOF

chmod +x test.sh
print_success "Test script created ✓"

print_success "Convenience scripts created! ✓"
echo

# Final summary
echo "🎉 Frontend setup completed successfully!"
echo "========================================"
echo
print_status "Quick Start Commands:"
echo "  Development: ./start.sh"
echo "  Build:       ./build.sh"
echo "  Preview:     ./preview.sh"
echo "  Test:        ./test.sh"
echo
print_status "Manual Commands (with $PACKAGE_MANAGER):"
if [ "$PACKAGE_MANAGER" = "yarn" ]; then
    echo "  Start dev:   yarn dev"
    echo "  Build:       yarn build"
    echo "  Preview:     yarn preview"
    echo "  Lint:        yarn lint"
    echo "  Test:        yarn test"
else
    echo "  Start dev:   npm run dev"
    echo "  Build:       npm run build"
    echo "  Preview:     npm run preview"
    echo "  Lint:        npm run lint"
    echo "  Test:        npm test"
fi
echo
print_status "Access Points:"
echo "  🌐 Development: http://localhost:5173"
echo "  👀 Preview:     http://localhost:4173"
echo "  📱 Network:     Available on local network"
echo
print_status "Project Structure:"
echo "  📄 Config:      vite.config.js"
echo "  🌍 Environment: .env"
echo "  📦 Dependencies: package.json"
echo "  🎨 Source:      src/"
echo "  🏗️  Build:       dist/"
echo "  📚 Public:      public/"
echo
print_status "Configuration:"
echo "  📄 Environment: .env"
echo "  🔧 Backend URL: http://localhost:8000"
echo "  🎨 Theme:       default"
echo "  🌍 Language:    en"
echo
print_status "Next Steps:"
echo "  1. Review and customize .env file if needed"
echo "  2. Start development server: ./start.sh"
echo "  3. Open browser: http://localhost:5173"
echo "  4. Start coding in src/ directory"
echo "  5. Check the main README.md for full documentation"
echo
print_success "Happy coding! 🚀"
