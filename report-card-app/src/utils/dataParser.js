// Utility functions for the report card application using structured data
import {
  ATTITUDE_CATEGORIES,
  CLASSROOM_CATEGORIES,
  SOCIAL_EMOTIONAL_CATEGORIES,
  SUBJECT_GRADE_MAPPING,
  SUBJECT_FEEDBACK,
  CONCEPT_POINTS,
  WRITING_POINTS,
  SELF_AWARENESS_POINTS,
  formatText,
  highlightImportantWords,
  formatTextForFinalReport
} from '../data/reportCardData.js';

/**
 * Get structured behavior data organized by categories
 * @returns {Object} Organized behavior data
 */
export const getBehaviorData = () => {
  return {
    'ATTITUDE AND RESPONSIBILITY': ATTITUDE_CATEGORIES,
    'CLA<PERSON><PERSON>OM BEHAVIOR': CLASSROOM_CATEGORIES,
    'SOCIAL EMOTIONAL BEHAVIOR': SOCIAL_EMOTIONAL_CATEGORIES,
    'WRITING': WRITING_POINTS,
    'SELF AWARENESS': SELF_AWARENESS_POINTS
  };
};

/**
 * Get subjects available for a specific grade following ICSE syllabus
 * @param {number} grade - Grade number (1-10)
 * @returns {Object} Object with compulsory and optional subjects
 */
export const getSubjectsForGrade = (grade) => {
  const compulsorySubjects = [];
  const optionalSubjects = [];
  const subjectGroups = {};

  Object.entries(SUBJECT_GRADE_MAPPING).forEach(([subject, config]) => {
    if (grade >= config.from && grade <= config.to) {
      if (config.compulsory) {
        compulsorySubjects.push(subject);
      } else {
        optionalSubjects.push(subject);
      }

      // Group subjects for mutual exclusivity
      if (config.group) {
        if (!subjectGroups[config.group]) {
          subjectGroups[config.group] = [];
        }
        subjectGroups[config.group].push(subject);
      }
    }
  });

  // For grades 6+, handle science vs economics choice
  if (grade >= 6) {
    // Remove individual social subjects for lower grades
    const filteredCompulsory = compulsorySubjects.filter(s => s !== 'SOCIAL' && s !== 'SCIENCE');

    return {
      compulsory: filteredCompulsory,
      optional: optionalSubjects,
      groups: subjectGroups,
      all: [...filteredCompulsory, ...optionalSubjects]
    };
  }

  // For grades 1-5, use combined subjects
  const filteredCompulsory = compulsorySubjects.filter(s =>
    !['HISTORY', 'GEOGRAPHY', 'PHYSICS', 'CHEMISTRY', 'BIOLOGY', 'ECONOMICS'].includes(s)
  );
  const filteredOptional = optionalSubjects.filter(s =>
    !['HISTORY', 'GEOGRAPHY', 'PHYSICS', 'CHEMISTRY', 'BIOLOGY', 'ECONOMICS'].includes(s)
  );

  return {
    compulsory: filteredCompulsory,
    optional: filteredOptional,
    groups: subjectGroups,
    all: [...filteredCompulsory, ...filteredOptional]
  };
};

/**
 * Get subject feedback points for a specific subject
 * @param {string} subject - Subject name
 * @returns {Array} Array of feedback points for the subject
 */
export const getSubjectFeedback = (subject) => {
  // Handle subjects with options (like "2nd Language: Hindi")
  const baseSubject = subject.split(':')[0].trim();
  return SUBJECT_FEEDBACK[baseSubject] || [];
};

/**
 * Get concept evaluation points
 * @returns {Array} Array of concept points
 */
export const getConceptPoints = () => {
  return CONCEPT_POINTS;
};

/**
 * Get subject options that have multiple choices (like 2nd Language)
 * @param {Array} subjects - Array of subjects
 * @returns {Object} Object with subjects that have multiple options
 */
export const getSubjectOptions = (subjects) => {
  const options = {};

  subjects.forEach(subject => {
    const config = SUBJECT_GRADE_MAPPING[subject];
    if (config && config.options) {
      options[subject] = config.options;
    }
  });

  return options;
};

/**
 * Load all structured data (no longer needs CSV parsing)
 * @returns {Promise<Object>} Promise resolving to all structured data
 */
export const loadAllData = async () => {
  try {
    return {
      behavior: {
        'CLASSROOM BEHAVIOR': CLASSROOM_CATEGORIES,
        'SOCIAL EMOTIONAL BEHAVIOR': SOCIAL_EMOTIONAL_CATEGORIES
      },
      subjects: SUBJECT_GRADE_MAPPING,
      concepts: { CONCEPT: CONCEPT_POINTS }
    };
  } catch (error) {
    console.error('Error loading data:', error);
    throw error;
  }
};

/**
 * Format text with proper pronouns and highlighting
 * @param {string} text - Text to format
 * @param {string} studentName - Student name
 * @param {string} gender - Student gender ('male', 'female', or 'neutral')
 * @returns {string} Formatted text
 */
export const formatTextForDisplay = (text, studentName, gender = 'neutral') => {
  const formatted = formatText(text, studentName, gender);
  return highlightImportantWords(formatted);
};

/**
 * Generate report JSON from selected data
 * @param {Object} formData - Form data with selections
 * @param {Object} selections - Selected points for each section
 * @returns {Object} Generated report JSON
 */
export const generateReportJSON = (formData, selections) => {
  const report = {
    assessment: formData.assessment || "1st Term Assessment Profile 2023 - 2024",
    student: formData.studentName,
    sections: {}
  };

  // Format all text with proper pronouns and clean formatting for final report
  const gender = formData.gender || 'neutral';

  // Add behavioral sections
  if (selections.attitudeResponsibility && selections.attitudeResponsibility.length > 0) {
    const formatted = selections.attitudeResponsibility.map(text =>
      formatTextForFinalReport(text, formData.studentName, gender)
    );
    report.sections.Attitude_and_Responsibility = formatted.join(' ');
  }

  if (selections.classroomBehavior && selections.classroomBehavior.length > 0) {
    const formatted = selections.classroomBehavior.map(text =>
      formatTextForFinalReport(text, formData.studentName, gender)
    );
    report.sections.Class_Room_Behaviour = formatted.join(' ');
  }

  if (selections.socialEmotional && selections.socialEmotional.length > 0) {
    const formatted = selections.socialEmotional.map(text =>
      formatTextForFinalReport(text, formData.studentName, gender)
    );
    report.sections.Social_and_Emotional_Behaviour = formatted.join(' ');
  }

  // Add concept section
  if (selections.concept && selections.concept.length > 0) {
    const formatted = selections.concept.map(text =>
      formatTextForFinalReport(text, formData.studentName, gender)
    );
    report.sections.Concept = formatted.join(' ');
  }

  // Add subject-wise feedback
  if (selections.subjects && Object.keys(selections.subjects).length > 0) {
    report.sections.Subject_wise_Feedback = {};
    Object.entries(selections.subjects).forEach(([subject, points]) => {
      if (points && points.length > 0) {
        const formatted = points.map(text =>
          formatTextForFinalReport(text, formData.studentName, gender)
        );
        // Convert subject names to match the expected format
        const formattedSubject = subject.replace(/\s+/g, '_').replace(/\//g, '_');
        report.sections.Subject_wise_Feedback[formattedSubject] = formatted.join(' ');
      }
    });
  }

  return report;
};
