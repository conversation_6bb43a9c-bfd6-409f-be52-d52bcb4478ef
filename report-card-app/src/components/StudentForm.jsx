import { useState, useEffect } from 'react';
import { getSubjectsForGrade, getSubjectOptions } from '../utils/dataParser';
import CustomDropdown from './CustomDropdown';

const StudentForm = ({ data, onComplete, onBack, initialData }) => {
  const [studentName, setStudentName] = useState(initialData?.studentName || '');
  const [grade, setGrade] = useState(initialData?.grade || '');
  const [gender, setGender] = useState(initialData?.gender || '');
  const [selectedSubjects, setSelectedSubjects] = useState(initialData?.subjects || []);
  const [subjectOptions, setSubjectOptions] = useState({});
  const [subjectData, setSubjectData] = useState({ compulsory: [], optional: [], groups: {}, all: [] });
  const [selectedGroup, setSelectedGroup] = useState('science'); // Default to science track
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (grade) {
      const gradeNum = parseInt(grade);
      const subjects = getSubjectsForGrade(gradeNum);
      setSubjectData(subjects);

      const options = getSubjectOptions(subjects.all);
      setSubjectOptions(options);

      // Auto-select subjects based on ICSE requirements when grade changes
      if (grade !== initialData?.grade) {
        const defaultSelections = [];

        // Add all compulsory subjects
        subjects.compulsory.forEach(subject => {
          if (options[subject] && options[subject].length > 0) {
            defaultSelections.push(`${subject}: ${options[subject][0]}`);
          } else {
            defaultSelections.push(subject);
          }
        });

        // For grades 6+, add science subjects by default (or economics if commerce track)
        if (gradeNum >= 6) {
          if (selectedGroup === 'science' && subjects.groups.science) {
            subjects.groups.science.forEach(subject => {
              defaultSelections.push(subject);
            });
          } else if (selectedGroup === 'commerce' && subjects.groups.commerce) {
            subjects.groups.commerce.forEach(subject => {
              defaultSelections.push(subject);
            });
          }
        }

        // Handle Math vs EVS choice (default to Math)
        if (subjects.all.includes('MATHS') && !defaultSelections.includes('EVS')) {
          if (!defaultSelections.includes('MATHS')) {
            defaultSelections.push('MATHS');
          }
        }

        setSelectedSubjects(defaultSelections);

        // Clear subjects error when auto-selecting subjects
        if (defaultSelections.length > 0) {
          setErrors(prev => ({ ...prev, subjects: '' }));
        }
      }
    }
  }, [grade, selectedGroup, initialData?.grade]);

  const handleSubjectToggle = (subject) => {
    setSelectedSubjects(prev => {
      const newSubjects = prev.includes(subject)
        ? prev.filter(s => s !== subject)
        : [...prev, subject];

      // Clear subjects error when user selects at least one subject
      if (errors.subjects && newSubjects.length > 0) {
        setErrors(prevErrors => ({ ...prevErrors, subjects: '' }));
      }

      return newSubjects;
    });
  };

  const handleSubjectOptionChange = (subject, option) => {
    setSelectedSubjects(prev => {
      const filtered = prev.filter(s => !s.startsWith(subject));
      const newSubjects = [...filtered, `${subject}: ${option}`];

      // Clear subjects error when user selects subject options
      if (errors.subjects && newSubjects.length > 0) {
        setErrors(prevErrors => ({ ...prevErrors, subjects: '' }));
      }

      // Clear second language error when user selects second language
      if (subject === '2nd Language' && option && errors.secondLanguage) {
        setErrors(prevErrors => ({ ...prevErrors, secondLanguage: '' }));
      }

      return newSubjects;
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!studentName.trim()) {
      newErrors.studentName = 'Student name is required';
    }

    if (!grade) {
      newErrors.grade = 'Grade is required';
    }

    if (!gender || (gender !== 'male' && gender !== 'female')) {
      newErrors.gender = 'Please select gender (He or She)';
    }

    if (selectedSubjects.length === 0) {
      newErrors.subjects = 'Please select at least one subject';
    }

    // Check if second language is selected (if it's available for this grade)
    const hasSecondLanguage = subjectData.all && subjectData.all.includes('2nd Language');
    if (hasSecondLanguage) {
      const secondLanguageSelected = selectedSubjects.find(s => s.startsWith('2nd Language:'));
      if (!secondLanguageSelected) {
        newErrors.secondLanguage = 'Please select a second language';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getErrorMessages = () => {
    const messages = [];
    if (errors.studentName) messages.push('Please enter the student name');
    if (errors.grade) messages.push('Please select a grade');
    if (errors.gender) messages.push('Please select gender (He or She)');
    if (errors.subjects) messages.push('Please select at least one subject');
    if (errors.secondLanguage) messages.push('Please select a second language');
    return messages;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onComplete({
      studentName: studentName.trim(),
      grade: parseInt(grade),
      gender: gender,
      subjects: selectedSubjects
    });
  };

  const grades = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  return (
    <div className="form-section">
      <div className="form-header">
        <h2>Student Information</h2>
        <p>Enter the student details and select the relevant subjects for assessment.</p>
      </div>

      <form onSubmit={handleSubmit} className="form">
        <div className="form-group">
          <label htmlFor="studentName" className="form-label">
            Student Name
          </label>
          <input
            type="text"
            id="studentName"
            value={studentName}
            onChange={(e) => {
              setStudentName(e.target.value);
              // Clear error when user starts typing
              if (errors.studentName && e.target.value.trim()) {
                setErrors(prev => ({ ...prev, studentName: '' }));
              }
            }}
            className={`form-input ${errors.studentName ? 'error' : ''}`}
            placeholder="Enter student's full name"
            autoFocus
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label className="form-label">
              Grade
            </label>
            <div className="grade-selector">
              {grades.map(g => (
                <div key={g} className={`grade-option ${errors.grade ? 'error' : ''}`}>
                  <input
                    type="radio"
                    id={`grade-${g}`}
                    name="grade"
                    value={g}
                    checked={grade === g.toString()}
                    onChange={(e) => {
                      setGrade(e.target.value);
                      // Clear error when user selects grade
                      if (errors.grade) {
                        setErrors(prev => ({ ...prev, grade: '' }));
                      }
                    }}
                  />
                  <label htmlFor={`grade-${g}`} className="grade-option-label">
                    Grade {g}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">
              Gender (for pronouns)
            </label>
            <CustomDropdown
              value={gender}
              onChange={(val) => {
                setGender(val);
                if (errors.gender && (val === 'male' || val === 'female')) {
                  setErrors(prev => ({ ...prev, gender: '' }));
                }
              }}
              options={[
                { value: 'male', label: 'Male (He/Him)' },
                { value: 'female', label: 'Female (She/Her)' }
              ]}
              placeholder="Select gender"
              error={errors.gender}
            />
          </div>
        </div>

        {grade && subjectData.all.length > 0 && (
          <div className="form-group">
            <label className="form-label">
              Subjects
            </label>

            {/* Grade 6+ Track Selection */}
            {parseInt(grade) >= 6 && subjectData.groups.science && subjectData.groups.commerce && (
              <div className="form-group">
                <label className="form-label">Academic Track</label>
                <div className="form-row">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="track"
                      value="science"
                      checked={selectedGroup === 'science'}
                      onChange={(e) => {
                        setSelectedGroup(e.target.value);
                        // Clear subjects error; new subjects will be auto-selected by grade/group
                        if (errors.subjects) {
                          setErrors(prev => ({ ...prev, subjects: '' }));
                        }
                      }}
                    />
                    <span className="radio-label"><strong>Science Track</strong> (Physics, Chemistry, Biology)</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="track"
                      value="commerce"
                      checked={selectedGroup === 'commerce'}
                      onChange={(e) => {
                        setSelectedGroup(e.target.value);
                        if (errors.subjects) {
                          setErrors(prev => ({ ...prev, subjects: '' }));
                        }
                      }}
                    />
                    <span className="radio-label"><strong>Commerce Track</strong> (Economics)</span>
                  </label>
                </div>
              </div>
            )}

            {/* Math vs EVS Choice */}
            {subjectData.all.includes('MATHS') && subjectData.all.includes('EVS') && (
              <div className="form-group">
                <label className="form-label">Mathematics or Environmental Studies</label>
                <div className="form-row">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="mathEvs"
                      value="MATHS"
                      checked={selectedSubjects.includes('MATHS')}
                      onChange={() => {
                        const filtered = selectedSubjects.filter(s => s !== 'MATHS' && s !== 'EVS');
                        setSelectedSubjects([...filtered, 'MATHS']);
                        // Clear subjects error when selecting Math/EVS
                        if (errors.subjects) {
                          setErrors(prev => ({ ...prev, subjects: '' }));
                        }
                      }}
                    />
                    <span className="radio-label"><strong>Mathematics</strong></span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="mathEvs"
                      value="EVS"
                      checked={selectedSubjects.includes('EVS')}
                      onChange={() => {
                        const filtered = selectedSubjects.filter(s => s !== 'MATHS' && s !== 'EVS');
                        setSelectedSubjects([...filtered, 'EVS']);
                        // Clear subjects error when selecting Math/EVS
                        if (errors.subjects) {
                          setErrors(prev => ({ ...prev, subjects: '' }));
                        }
                      }}
                    />
                    <span className="radio-label"><strong>Environmental Studies</strong></span>
                  </label>
                </div>
              </div>
            )}

            {/* Compulsory Subjects */}
            {subjectData.compulsory.length > 0 && (
              <div className="form-group">
                <label className="form-label">Compulsory Subjects</label>
                <div className={`subjects-grid ${errors.subjects ? 'error' : ''}`}>
                  {subjectData.compulsory.filter(subject => subject !== 'MATHS' && subject !== 'EVS').map(subject => (
                    <div key={subject} className="subject-item">
                      {subjectOptions[subject] ? (
                        <div className="subject-with-options">
                          <span className="subject-name"><strong>{subject}</strong></span>
                          <CustomDropdown
                            value={selectedSubjects.find(s => s.startsWith(subject))?.split(': ')[1] || ''}
                            onChange={(value) => handleSubjectOptionChange(subject, value)}
                            options={[
                              { value: '', label: 'Select Option' },
                              ...subjectOptions[subject].map(option => ({ value: option, label: option }))
                            ]}
                            placeholder="Select Option"
                            className="subject-option-select"
                          />
                        </div>
                      ) : (
                        <div className="subject-checkbox">
                          <input
                            type="checkbox"
                            checked={true}
                            disabled={true}
                          />
                          <span className="subject-name"><strong>{subject}</strong> (Required)</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Optional Subjects */}
            {subjectData.optional.length > 0 && (
              <div className="form-group">
                <label className="form-label">Optional Subjects</label>
                <div className={`subjects-grid ${errors.subjects ? 'error' : ''}`}>
                  {subjectData.optional.filter(subject => {
                    // Filter based on selected track for grades 6+
                    if (parseInt(grade) >= 6) {
                      if (selectedGroup === 'science' && subjectData.groups.commerce?.includes(subject)) {
                        return false;
                      }
                      if (selectedGroup === 'commerce' && subjectData.groups.science?.includes(subject)) {
                        return false;
                      }
                    }
                    return subject !== 'MATHS' && subject !== 'EVS';
                  }).map(subject => (
                    <div key={subject} className="subject-item">
                      {subjectOptions[subject] ? (
                        <div className="subject-with-options">
                          <span className="subject-name">{subject}</span>
                          <CustomDropdown
                            value={selectedSubjects.find(s => s.startsWith(subject))?.split(': ')[1] || ''}
                            onChange={(value) => handleSubjectOptionChange(subject, value)}
                            options={[
                              { value: '', label: 'Select Option' },
                              ...subjectOptions[subject].map(option => ({ value: option, label: option }))
                            ]}
                            placeholder="Select Option"
                            className="subject-option-select"
                          />
                        </div>
                      ) : (
                        <label className="subject-checkbox">
                          <input
                            type="checkbox"
                            checked={selectedSubjects.includes(subject)}
                            onChange={() => handleSubjectToggle(subject)}
                          />
                          <span className="subject-name">{subject}</span>
                        </label>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

          </div>
        )}

        <div className="form-actions">
          {Object.keys(errors).filter(key => errors[key]).length > 0 && (
            <div className="error-summary">
              <h4>Please fix the following issues:</h4>
              <ul>
                {getErrorMessages().filter(msg => msg).map((message, index) => (
                  <li key={index}>{message}</li>
                ))}
              </ul>
            </div>
          )}
          <button type="button" onClick={onBack} className="btn btn-secondary">
            Back
          </button>
          <button type="submit" className="btn btn-primary">
            Continue
          </button>
        </div>
      </form>
    </div>
  );
};

export default StudentForm;
