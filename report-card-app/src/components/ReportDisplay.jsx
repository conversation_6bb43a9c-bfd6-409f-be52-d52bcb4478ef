import { useState } from 'react';
import { generateReportJSON, formatTextForDisplay } from '../utils/dataParser';
import { formatTextForFinalReport } from '../data/reportCardData';

const ReportDisplay = ({ formData, selections, onBack, onReset }) => {
  const [showJSON, setShowJSON] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationError, setGenerationError] = useState(null);

  const reportData = generateReportJSON(formData, selections);

  const formatSectionText = (points) => {
    if (Array.isArray(points)) {
      return points.map(point =>
        formatTextForFinalReport(point, formData.studentName, formData.gender)
      ).join(' ');
    }
    return formatTextForFinalReport(points, formData.studentName, formData.gender);
  };

  const handleCopyJSON = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(reportData, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy JSON:', err);
    }
  };

  const handleDownloadJSON = () => {
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `report_${formData.studentName.replace(/\s+/g, '_')}_${Date.now()}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleGenerateDocument = async () => {
    setIsGenerating(true);
    setGenerationError(null);

    try {
      // Backend API endpoint
      const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8000';

      const response = await fetch(`${backendUrl}/api/generate-doc`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Get filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `report_${formData.studentName.replace(/\s+/g, '_')}_${Date.now()}.docx`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const linkElement = document.createElement('a');
      linkElement.href = url;
      linkElement.download = filename;
      document.body.appendChild(linkElement);
      linkElement.click();
      document.body.removeChild(linkElement);

      // Clean up
      window.URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error generating document:', error);
      setGenerationError(error.message);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="form-section">
      <div className="form-header">
        <h2>Generated Report</h2>
        <p>Report card for <strong>{formData.studentName}</strong> has been generated successfully.</p>
      </div>

      <div className="report-container">
        <div className="report-summary">
          <div className="summary-item">
            <label>Student:</label>
            <span>{formData.studentName}</span>
          </div>
          <div className="summary-item">
            <label>Grade:</label>
            <span>{formData.grade}</span>
          </div>
          <div className="summary-item">
            <label>Subjects:</label>
            <span>{formData.subjects.join(', ')}</span>
          </div>
          <div className="summary-item">
            <label>Assessment:</label>
            <span>{formData.assessment}</span>
          </div>
        </div>

        <div className="report-sections">
          <div className="report-section">
            <h3>Attitude & Responsibility</h3>
            <div dangerouslySetInnerHTML={{
              __html: formatSectionText(selections.attitudeResponsibility)
            }} />
          </div>

          <div className="report-section">
            <h3>Classroom Behaviour</h3>
            <div dangerouslySetInnerHTML={{
              __html: formatSectionText(selections.classroomBehavior)
            }} />
          </div>

          <div className="report-section">
            <h3>Social & Emotional Behaviour</h3>
            <div dangerouslySetInnerHTML={{
              __html: formatSectionText(selections.socialEmotional)
            }} />
          </div>

          <div className="report-section">
            <h3>Concept</h3>
            <div dangerouslySetInnerHTML={{
              __html: formatSectionText(selections.concept)
            }} />
          </div>

          <div className="report-section">
            <h3>Subject-wise Feedback</h3>
            {Object.entries(selections.subjects).map(([subject, points]) => (
              points && points.length > 0 && (
                <div key={subject} className="subject-feedback">
                  <h4>{subject}</h4>
                  <div dangerouslySetInnerHTML={{
                    __html: formatSectionText(points)
                  }} />
                </div>
              )
            ))}
          </div>
        </div>

        <div className="json-section">
          <div className="json-header">
            <h3>JSON Output</h3>
            <div className="json-actions">
              <button 
                onClick={() => setShowJSON(!showJSON)} 
                className="btn btn-outline"
              >
                {showJSON ? 'Hide JSON' : 'Show JSON'}
              </button>
              <button 
                onClick={handleCopyJSON} 
                className="btn btn-outline"
                disabled={copied}
              >
                {copied ? 'Copied!' : 'Copy JSON'}
              </button>
              <button
                onClick={handleDownloadJSON}
                className="btn btn-outline"
              >
                Download JSON
              </button>
              <button
                onClick={handleGenerateDocument}
                className="btn btn-primary"
                disabled={isGenerating}
              >
                {isGenerating ? 'Generating...' : 'Generate Word Document'}
              </button>
            </div>
          </div>

          {generationError && (
            <div className="error-message" style={{
              marginTop: '1rem',
              padding: '1rem',
              backgroundColor: '#fee',
              border: '1px solid #fcc',
              borderRadius: '4px',
              color: '#c33'
            }}>
              <strong>Error generating document:</strong> {generationError}
            </div>
          )}

          {showJSON && (
            <div className="json-display">
              <pre>{JSON.stringify(reportData, null, 2)}</pre>
            </div>
          )}
        </div>
      </div>

      <div className="form-actions">
        <button type="button" onClick={onBack} className="btn btn-secondary">
          Back to Edit
        </button>
        <button type="button" onClick={onReset} className="btn btn-primary">
          Create New Report
        </button>
      </div>
    </div>
  );
};

export default ReportDisplay;
