import { useState } from 'react';
import { getConceptPoints, formatTextForDisplay } from '../utils/dataParser';

const ConceptStep = ({ 
  formData, 
  selections, 
  onSelectionChange, 
  onNext, 
  onBack 
}) => {
  const [errors, setErrors] = useState({});
  
  const conceptPoints = getConceptPoints();

  const handlePointToggle = (point) => {
    const currentSelections = selections || [];
    const newSelections = currentSelections.includes(point)
      ? currentSelections.filter(p => p !== point)
      : [...currentSelections, point];
    
    onSelectionChange(newSelections);
    
    // Clear errors when user makes a selection
    if (newSelections.length > 0) {
      setErrors({});
    }
  };

  const handleNext = (e) => {
    e.preventDefault();
    
    if (!selections || selections.length === 0) {
      setErrors({ selection: 'Please select at least one concept point' });
      return;
    }
    
    onNext();
  };

  return (
    <div className="form-section">
      <div className="form-header">
        <h2>Concept Understanding</h2>
        <h3 className="category-main-title">Academic Concepts</h3>
        <p>Select the most appropriate points that describe the student's understanding of academic concepts.</p>
      </div>

      <form onSubmit={handleNext} className="form">
        <div className="attitude-step-container">
          <div className={`points-grid ${errors.selection ? 'error' : ''}`}>
            {conceptPoints.map((point, index) => (
              <div key={index} className="point-item">
                <label className="point-checkbox">
                  <input
                    type="checkbox"
                    checked={selections?.includes(point) || false}
                    onChange={() => handlePointToggle(point)}
                  />
                  <span
                    className="point-text"
                    dangerouslySetInnerHTML={{
                      __html: formatTextForDisplay(point, formData.studentName, formData.gender)
                    }}
                  />
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="form-actions">
          {errors.selection && (
            <div className="error-summary">
              <h4>Please fix the following issue:</h4>
              <ul>
                <li>{errors.selection}</li>
              </ul>
            </div>
          )}
          <button type="button" onClick={onBack} className="btn btn-secondary">
            Previous
          </button>
          <button type="submit" className="btn btn-primary">
            Continue to Subject Feedback
          </button>
        </div>
      </form>
    </div>
  );
};

export default ConceptStep;
