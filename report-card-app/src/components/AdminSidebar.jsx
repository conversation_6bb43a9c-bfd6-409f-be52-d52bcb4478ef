import { useState } from 'react';

const AdminSidebar = ({ currentPage, collapsed, open, onToggle, user, onLogout }) => {
  const [expandedSections, setExpandedSections] = useState({
    setup: true,
    content: true,
    version: false,
    data: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      page: 'dashboard'
    },
    {
      id: 'setup',
      label: 'School Configuration',
      icon: '🏫',
      expandable: true,
      children: [
        { id: 'school-settings', label: 'School Information', page: 'school-settings', icon: '🏫', badge: 'Essential' },
        { id: 'grades', label: 'Grade Levels', page: 'grades', icon: '🎓' },
        { id: 'subjects', label: 'Subject Areas', page: 'subjects', icon: '📚' },
        { id: 'categories', label: 'Assessment Categories', page: 'categories', icon: '📋' }
      ]
    },
    {
      id: 'content',
      label: 'Teaching Tools',
      icon: '📝',
      expandable: true,
      children: [
        { id: 'assessment-points', label: 'Assessment Criteria', page: 'assessment-points', icon: '✓', badge: 'Daily Use' },
        { id: 'templates', label: 'Report Templates', page: 'templates', icon: '📄' },
        { id: 'bulk-operations', label: 'Batch Processing', page: 'bulk-operations', icon: '⚡', badge: 'Time Saver' }
      ]
    },
    {
      id: 'version',
      label: 'Publishing & Versions',
      icon: '🚀',
      expandable: true,
      children: [
        { id: 'versions', label: 'Draft Management', page: 'versions', icon: '📦', badge: 'Safe Edits' },
        { id: 'publish', label: 'Go Live', page: 'publish', icon: '🚀' }
      ]
    },
    {
      id: 'data',
      label: 'Data & Reports',
      icon: '📊',
      expandable: true,
      children: [
        { id: 'migration', label: 'Import Data', page: 'migration', icon: '📥', badge: 'Setup' },
        { id: 'export', label: 'Export & Backup', page: 'export', icon: '📤' },
        { id: 'analytics', label: 'Usage Insights', page: 'analytics', icon: '📈', badge: 'New' }
      ]
    }
  ];

  const handleNavigation = (page) => {
    // This will be handled by the parent component
    window.dispatchEvent(new CustomEvent('admin-navigate', { detail: { page } }));

    // Close sidebar on mobile after navigation
    if (window.innerWidth <= 768) {
      onToggle();
    }
  };

  const renderNavItem = (item, level = 0) => {
    const isActive = currentPage === item.page;
    const isExpanded = expandedSections[item.id];

    return (
      <div key={item.id} className={`nav-item level-${level}`}>
        <div 
          className={`nav-link ${isActive ? 'active' : ''} ${item.expandable ? 'expandable' : ''}`}
          onClick={() => {
            if (item.expandable) {
              toggleSection(item.id);
            } else if (item.page) {
              handleNavigation(item.page);
            }
          }}
        >
          <span className="nav-icon">{item.icon}</span>
          {!collapsed && (
            <>
              <span className="nav-label">{item.label}</span>
              {item.badge && (
                <span className="nav-badge">{item.badge}</span>
              )}
              {item.expandable && (
                <span className={`nav-arrow ${isExpanded ? 'expanded' : ''}`}>
                  ▼
                </span>
              )}
            </>
          )}
        </div>
        
        {item.children && isExpanded && !collapsed && (
          <div className="nav-children">
            {item.children.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <aside className={`admin-sidebar ${collapsed ? 'collapsed' : ''} ${open ? 'open' : ''}`}>
      <div className="sidebar-header">
        <div className="sidebar-logo">
          <span className="logo-icon">🎓</span>
          {!collapsed && <span className="logo-text">Report Card Admin</span>}
        </div>
      </div>
      
      <nav className="sidebar-nav">
        {navigationItems.map(item => renderNavItem(item))}
      </nav>
      
      <div className="sidebar-footer">
        {/* User Info and Logout */}
        {!collapsed && user && (
          <div className="sidebar-user-info">
            <div className="user-details">
              <div className="user-name">{user?.full_name || user?.username}</div>
              <div className="user-role">Administrator</div>
            </div>
            <button
              className="logout-btn"
              onClick={onLogout}
              title="Logout"
            >
              🚪
            </button>
          </div>
        )}

        <button
          className="sidebar-toggle"
          onClick={onToggle}
          title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {collapsed ? '→' : '←'}
        </button>
      </div>
    </aside>
  );
};

export default AdminSidebar;
