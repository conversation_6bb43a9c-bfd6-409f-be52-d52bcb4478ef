import { useState } from 'react';
import { getSubjectFeedback, getConceptPoints, formatTextForDisplay } from '../utils/dataParser';

const SectionSelection = ({ data, formData, onComplete, onBack }) => {
  const [selections, setSelections] = useState({
    classroomBehavior: [],
    socialEmotional: [],
    concept: [],
    subjects: {}
  });
  const [errors, setErrors] = useState({});

  const behaviorData = data?.behavior || {};
  const conceptPoints = getConceptPoints();

  // Get categorized behavior data
  const classroomCategories = behaviorData['CLASSROOM BEHAVIOR'] || {};
  const socialEmotionalCategories = behaviorData['SOCIAL EMOTIONAL BEHAVIOR'] || {};

  const handlePointToggle = (section, point) => {
    setSelections(prev => ({
      ...prev,
      [section]: prev[section].includes(point)
        ? prev[section].filter(p => p !== point)
        : [...prev[section], point]
    }));
  };

  const handleSubjectPointToggle = (subject, point) => {
    setSelections(prev => ({
      ...prev,
      subjects: {
        ...prev.subjects,
        [subject]: prev.subjects[subject]?.includes(point)
          ? prev.subjects[subject].filter(p => p !== point)
          : [...(prev.subjects[subject] || []), point]
      }
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (selections.classroomBehavior.length === 0) {
      newErrors.classroomBehavior = 'Please select at least one classroom behavior point';
    }
    
    if (selections.socialEmotional.length === 0) {
      newErrors.socialEmotional = 'Please select at least one social-emotional point';
    }
    
    if (selections.concept.length === 0) {
      newErrors.concept = 'Please select at least one concept point';
    }

    // Check if at least one subject has points selected
    const hasSubjectPoints = Object.values(selections.subjects).some(points => points && points.length > 0);
    if (!hasSubjectPoints) {
      newErrors.subjects = 'Please select points for at least one subject';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onComplete(selections);
  };



  return (
    <div className="form-section">
      <div className="form-header">
        <h2>Section Selection</h2>
        <p>Select appropriate points for each section of the report card.</p>
      </div>

      <form onSubmit={handleSubmit} className="form">
        {/* Classroom Behavior Section */}
        <div className="section-group">
          <h3 className="section-title">Classroom Behaviour</h3>
          <div className="attitude-categories">
            {Object.entries(classroomCategories).map(([categoryName, points]) => (
              <div key={categoryName} className="category-section">
                <h4 className="category-title">{categoryName}</h4>
                <div className="points-grid">
                  {points.map((point, index) => (
                    <div key={`${categoryName}-${index}`} className="point-item">
                      <label className="point-checkbox">
                        <input
                          type="checkbox"
                          checked={selections.classroomBehavior.includes(point)}
                          onChange={() => handlePointToggle('classroomBehavior', point)}
                        />
                        <span
                          className="point-text"
                          dangerouslySetInnerHTML={{
                            __html: formatTextForDisplay(point, formData.studentName, formData.gender)
                          }}
                        />
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          {errors.classroomBehavior && <span className="form-error">{errors.classroomBehavior}</span>}
        </div>

        {/* Social Emotional Section */}
        <div className="section-group">
          <h3 className="section-title">Social & Emotional Behaviour</h3>
          <div className="attitude-categories">
            {Object.entries(socialEmotionalCategories).map(([categoryName, points]) => (
              <div key={categoryName} className="category-section">
                <h4 className="category-title">{categoryName}</h4>
                <div className="points-grid">
                  {points.map((point, index) => (
                    <div key={`${categoryName}-${index}`} className="point-item">
                      <label className="point-checkbox">
                        <input
                          type="checkbox"
                          checked={selections.socialEmotional.includes(point)}
                          onChange={() => handlePointToggle('socialEmotional', point)}
                        />
                        <span
                          className="point-text"
                          dangerouslySetInnerHTML={{
                            __html: formatTextForDisplay(point, formData.studentName, formData.gender)
                          }}
                        />
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          {errors.socialEmotional && <span className="form-error">{errors.socialEmotional}</span>}
        </div>

        {/* Concept Section */}
        <div className="section-group">
          <h3 className="section-title">Concept</h3>
          <div className="points-grid">
            {conceptPoints.map((point, index) => (
              <div key={index} className="point-item">
                <label className="point-checkbox">
                  <input
                    type="checkbox"
                    checked={selections.concept.includes(point)}
                    onChange={() => handlePointToggle('concept', point)}
                  />
                  <span
                    className="point-text"
                    dangerouslySetInnerHTML={{
                      __html: formatTextForDisplay(point, formData.studentName, formData.gender)
                    }}
                  />
                </label>
              </div>
            ))}
          </div>
          {errors.concept && <span className="form-error">{errors.concept}</span>}
        </div>

        {/* Subject-wise Feedback */}
        <div className="section-group">
          <h3 className="section-title">Subject-wise Feedback</h3>
          {formData.subjects.map(subject => {
            const subjectPoints = getSubjectFeedback(subject);

            return (
              <div key={subject} className="subject-section">
                <h4 className="subject-title">{subject}</h4>
                <div className="points-grid">
                  {subjectPoints.map((point, index) => (
                    <div key={index} className="point-item">
                      <label className="point-checkbox">
                        <input
                          type="checkbox"
                          checked={selections.subjects[subject]?.includes(point) || false}
                          onChange={() => handleSubjectPointToggle(subject, point)}
                        />
                        <span
                          className="point-text"
                          dangerouslySetInnerHTML={{
                            __html: formatTextForDisplay(point, formData.studentName, formData.gender)
                          }}
                        />
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
          {errors.subjects && <span className="form-error">{errors.subjects}</span>}
        </div>

        <div className="form-actions">
          <button type="button" onClick={onBack} className="btn btn-secondary">
            Back
          </button>
          <button type="submit" className="btn btn-primary">
            Generate Report
          </button>
        </div>
      </form>
    </div>
  );
};

export default SectionSelection;
