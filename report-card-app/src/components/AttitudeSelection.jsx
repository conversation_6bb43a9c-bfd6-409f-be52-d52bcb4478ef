import { useState } from 'react';
import { formatTextForDisplay } from '../utils/dataParser';

const AttitudeSelection = ({ data, onComplete, onBack, formData }) => {
  const [selectedPoints, setSelectedPoints] = useState([]);
  const [error, setError] = useState('');

  const attitudeCategories = data?.behavior?.['ATTITUDE AND RESPONSIBILITY'] || {};

  const handlePointToggle = (point) => {
    setSelectedPoints(prev => {
      if (prev.includes(point)) {
        return prev.filter(p => p !== point);
      } else {
        return [...prev, point];
      }
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (selectedPoints.length === 0) {
      setError('Please select at least one attitude point');
      return;
    }

    setError('');
    onComplete(selectedPoints);
  };

  return (
    <div className="form-section">
      <div className="form-header">
        <h2>Attitude & Responsibility</h2>
        <p>Select the most appropriate attitude and responsibility points for <strong>{formData.studentName}</strong>.</p>
      </div>

      <form onSubmit={handleSubmit} className="form">
        <div className="attitude-categories">
          {Object.entries(attitudeCategories).map(([categoryName, points]) => (
            <div key={categoryName} className="category-section">
              <h3 className="category-title">{categoryName}</h3>
              <div className="points-grid">
                {points.map((point, index) => (
                  <div key={`${categoryName}-${index}`} className="point-item">
                    <label className="point-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedPoints.includes(point)}
                        onChange={() => handlePointToggle(point)}
                      />
                      <span
                        className="point-text"
                        dangerouslySetInnerHTML={{
                          __html: formatTextForDisplay(point, formData.studentName, formData.gender)
                        }}
                      />
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        {error && <span className="form-error">{error}</span>}

        <div className="selected-preview">
          <h3>Selected Points Preview:</h3>
          <div className="preview-content">
            {selectedPoints.length > 0 ? (
              <div
                dangerouslySetInnerHTML={{
                  __html: selectedPoints.map(point =>
                    formatTextForDisplay(point, formData.studentName, formData.gender)
                  ).join(' ')
                }}
              />
            ) : (
              <p className="preview-empty">No points selected yet.</p>
            )}
          </div>
        </div>

        <div className="form-actions">
          <button type="button" onClick={onBack} className="btn btn-secondary">
            Back
          </button>
          <button type="submit" className="btn btn-primary">
            Continue
          </button>
        </div>
      </form>
    </div>
  );
};

export default AttitudeSelection;
