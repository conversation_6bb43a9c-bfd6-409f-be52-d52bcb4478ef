import { useState, useEffect } from 'react';
import AdminSidebar from './AdminSidebar';

const AdminLayout = ({ children, currentPage, user, onLogout }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  return (
    <div className="admin-layout">
      {/* Mobile Overlay */}
      {sidebarOpen && (
        <div
          className="sidebar-overlay"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <AdminSidebar
        currentPage={currentPage}
        collapsed={sidebarCollapsed}
        open={sidebarOpen}
        onToggle={toggleSidebar}
        user={user}
        onLogout={onLogout}
      />

      <div className={`admin-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        {/* Mobile menu toggle for when sidebar is hidden */}
        {isMobile && !sidebarOpen && (
          <button
            className="mobile-menu-toggle-floating"
            onClick={toggleSidebar}
          >
            ☰
          </button>
        )}

        <main className="admin-content">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
