import { useState } from 'react';

const LoginForm = ({ onComplete }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username.trim() || !password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    setError('');
    setIsLoading(true);

    try {
      // Call unified login API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: username.trim(),
          password: password.trim()
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Login failed';
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorMessage;
        } catch (jsonError) {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        throw new Error('Invalid response from server. Please try again.');
      }

      // Validate response data
      if (!data.access_token || !data.user) {
        throw new Error('Invalid login response. Please try again.');
      }

      // Store token and user info
      localStorage.setItem('access_token', data.access_token);
      localStorage.setItem('user_info', JSON.stringify(data.user));

      onComplete({
        user: data.user,
        token: data.access_token,
        isAdmin: data.user.role === 'admin',
        teacherName: data.user.full_name || data.user.username
      });
    } catch (err) {
      setError(err.message || 'Login failed. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="form-section">
      <div className="form-header">
        <h2>Login to Report Card System</h2>
        <p>Welcome to the Report Card Generator. Please enter your credentials to continue.</p>
      </div>

      <form onSubmit={handleSubmit} className="form">
        <div className="form-group">
          <label htmlFor="username" className="form-label">
            Username
          </label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => {
              setUsername(e.target.value);
              if (error && e.target.value.trim()) {
                setError('');
              }
            }}
            className={`form-input ${error ? 'error' : ''}`}
            placeholder="Enter your username"
            autoFocus
            disabled={isLoading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => {
              setPassword(e.target.value);
              if (error && e.target.value.trim()) {
                setError('');
              }
            }}
            className={`form-input ${error ? 'error' : ''}`}
            placeholder="Enter your password"
            disabled={isLoading}
          />
        </div>

        <div className="form-actions">
          {error && error.trim() && (
            <div className="error-summary">
              <h4>Please fix the following issue:</h4>
              <ul>
                <li>{error}</li>
              </ul>
            </div>
          )}
          <button type="submit" className="btn btn-primary" disabled={isLoading}>
            {isLoading ? 'Logging in...' : 'Login'}
          </button>
        </div>
      </form>

      <div className="form-info">
        <p className="info-text">
          <strong>For Teachers:</strong> Use any username and password to access report generation.<br/>
          <strong>For Administrators:</strong> Use username "admin" and password "admin" to access configuration settings.
        </p>
      </div>
    </div>
  );
};

export default LoginForm;
