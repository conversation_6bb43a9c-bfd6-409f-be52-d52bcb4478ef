import { useState, useEffect } from 'react';
import dataService from '../services/dataService';

const GradeSelector = ({ selectedGrade, onGradeChange, showAllOption = true, className = '' }) => {
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadGrades();
  }, []);

  const loadGrades = async () => {
    try {
      setLoading(true);
      const data = await dataService.getGrades(true); // Only active grades
      setGrades(data);
      setError('');
    } catch (err) {
      setError('Failed to load grades');
      console.error('Error loading grades:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const value = e.target.value;
    onGradeChange(value === '' ? null : parseInt(value));
  };

  if (loading) {
    return (
      <select className={`grade-selector loading ${className}`} disabled>
        <option>Loading grades...</option>
      </select>
    );
  }

  if (error) {
    return (
      <select className={`grade-selector error ${className}`} disabled>
        <option>Error loading grades</option>
      </select>
    );
  }

  return (
    <select 
      className={`grade-selector ${className}`}
      value={selectedGrade || ''}
      onChange={handleChange}
    >
      {showAllOption && <option value="">All Grades</option>}
      {grades.map(grade => (
        <option key={grade.id} value={grade.grade_level}>
          {grade.grade_name}
        </option>
      ))}
      
      <style jsx>{`
        .grade-selector {
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          background: white;
          cursor: pointer;
        }

        .grade-selector:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .grade-selector.loading,
        .grade-selector.error {
          color: #666;
          cursor: not-allowed;
        }

        .grade-selector.error {
          border-color: #dc3545;
          background-color: #f8d7da;
        }
      `}</style>
    </select>
  );
};

export default GradeSelector;
