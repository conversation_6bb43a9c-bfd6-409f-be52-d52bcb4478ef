import { useState, useEffect } from 'react';

const SortOrderManager = ({ items, onSave, onClose, title = "Sort Order" }) => {
  const [sortedItems, setSortedItems] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    // Sort items by current sort_order
    const sorted = [...items].sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
    setSortedItems(sorted);
  }, [items]);

  const handleDragStart = (e, item, index) => {
    setDraggedItem({ item, index });
    e.dataTransfer.effectAllowed = 'move';
    e.target.style.opacity = '0.5';
  };

  const handleDragEnd = (e) => {
    e.target.style.opacity = '1';
    setDraggedItem(null);
    setDragOverIndex(null);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e, index) => {
    e.preventDefault();
    setDragOverIndex(index);
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem.index === dropIndex) {
      return;
    }

    const newItems = [...sortedItems];
    const draggedItemData = newItems[draggedItem.index];
    
    // Remove dragged item
    newItems.splice(draggedItem.index, 1);
    
    // Insert at new position
    const insertIndex = draggedItem.index < dropIndex ? dropIndex - 1 : dropIndex;
    newItems.splice(insertIndex, 0, draggedItemData);
    
    setSortedItems(newItems);
    setDragOverIndex(null);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      // Create updates with new sort orders
      const updates = sortedItems.map((item, index) => ({
        ...item,
        sort_order: index + 1
      }));
      
      await onSave(updates);
      onClose();
    } catch (error) {
      console.error('Error saving sort order:', error);
      alert('Failed to save sort order. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const getItemDisplayName = (item) => {
    return item.display_name || item.grade_name || item.point_text || item.name || `Item ${item.id}`;
  };

  const getItemDescription = (item) => {
    if (item.description) return item.description;
    if (item.point_text && item.point_text.length > 100) {
      return `${item.point_text.substring(0, 100)}...`;
    }
    if (item.category_type) return `Type: ${item.category_type}`;
    if (item.subject_group) return `Group: ${item.subject_group}`;
    if (item.grade_code) return `Code: ${item.grade_code}`;
    return '';
  };

  return (
    <div className="sort-overlay">
      <div className="sort-modal">
        <div className="sort-header">
          <h2>{title}</h2>
          <button onClick={onClose} className="close-btn">&times;</button>
        </div>

        <div className="sort-instructions">
          <p>Drag and drop items to reorder them. Items at the top will have lower sort order numbers.</p>
        </div>

        <div className="sort-list">
          {sortedItems.map((item, index) => (
            <div
              key={item.id}
              className={`sort-item ${dragOverIndex === index ? 'drag-over' : ''}`}
              draggable
              onDragStart={(e) => handleDragStart(e, item, index)}
              onDragEnd={handleDragEnd}
              onDragOver={handleDragOver}
              onDragEnter={(e) => handleDragEnter(e, index)}
              onDrop={(e) => handleDrop(e, index)}
            >
              <div className="drag-handle">
                <span className="drag-icon">⋮⋮</span>
              </div>
              <div className="item-content">
                <div className="item-name">{getItemDisplayName(item)}</div>
                <div className="item-description">{getItemDescription(item)}</div>
              </div>
              <div className="sort-number">#{index + 1}</div>
            </div>
          ))}
        </div>

        <div className="sort-actions">
          <button 
            onClick={handleSave} 
            className="btn btn-primary"
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Order'}
          </button>
          <button 
            onClick={onClose} 
            className="btn btn-secondary"
            disabled={saving}
          >
            Cancel
          </button>
        </div>

        <style jsx>{`
          .sort-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
          }

          .sort-modal {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          }

          .sort-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
          }

          .sort-header h2 {
            margin: 0;
            color: #333;
          }

          .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .close-btn:hover {
            color: #333;
          }

          .sort-instructions {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
          }

          .sort-instructions p {
            margin: 0;
            color: #666;
            font-size: 14px;
          }

          .sort-list {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
          }

          .sort-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: move;
            transition: all 0.2s ease;
          }

          .sort-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
          }

          .sort-item.drag-over {
            border-color: #28a745;
            background: #f8fff9;
            transform: translateY(-2px);
          }

          .drag-handle {
            margin-right: 15px;
            color: #999;
            cursor: grab;
          }

          .drag-handle:active {
            cursor: grabbing;
          }

          .drag-icon {
            font-size: 18px;
            line-height: 1;
          }

          .item-content {
            flex: 1;
          }

          .item-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .item-description {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
          }

          .sort-number {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            min-width: 30px;
            text-align: center;
          }

          .sort-actions {
            display: flex;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #eee;
            justify-content: flex-end;
          }

          .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
          }

          .btn-primary {
            background: #007bff;
            color: white;
          }

          .btn-secondary {
            background: #6c757d;
            color: white;
          }

          .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          .btn:hover:not(:disabled) {
            opacity: 0.9;
          }

          /* Drag and drop visual feedback */
          .sort-item[draggable="true"]:active {
            transform: rotate(2deg);
          }
        `}</style>
      </div>
    </div>
  );
};

export default SortOrderManager;
