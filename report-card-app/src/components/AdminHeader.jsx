import { useState } from 'react';

const AdminHeader = ({ currentPage, user, onLogout, onToggleSidebar, sidebarCollapsed }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);

  const pageInfo = {
    dashboard: { title: 'Dashboard', breadcrumb: ['Admin', 'Dashboard'] },
    grades: { title: 'Grade Management', breadcrumb: ['Admin', 'System Setup', 'Grades'] },
    subjects: { title: 'Subject Management', breadcrumb: ['Admin', 'System Setup', 'Subjects'] },
    categories: { title: 'Category Management', breadcrumb: ['Admin', 'System Setup', 'Categories'] },
    'assessment-points': { title: 'Assessment Points', breadcrumb: ['Admin', 'Content Management', 'Assessment Points'] },
    'bulk-operations': { title: 'Bulk Operations', breadcrumb: ['Admin', 'Content Management', 'Bulk Operations'] },
    versions: { title: 'Version Management', breadcrumb: ['Admin', 'Version Control', 'Manage Versions'] },
    publish: { title: 'Publishing', breadcrumb: ['Admin', 'Version Control', 'Publishing'] },
    migration: { title: 'Data Migration', breadcrumb: ['Admin', 'Data Management', 'Import/Migration'] },
    export: { title: 'Data Export', breadcrumb: ['Admin', 'Data Management', 'Export Data'] }
  };

  const current = pageInfo[currentPage] || { title: 'Admin', breadcrumb: ['Admin'] };

  const quickActions = [
    { id: 'add-assessment', label: 'Add Assessment Point', icon: '➕', action: 'add-assessment' },
    { id: 'add-subject', label: 'Add Subject', icon: '📚', action: 'add-subject' },
    { id: 'publish-version', label: 'Publish Version', icon: '🚀', action: 'publish-version' },
    { id: 'import-data', label: 'Import Data', icon: '📥', action: 'import-data' }
  ];

  const handleQuickAction = (action) => {
    window.dispatchEvent(new CustomEvent('admin-quick-action', { detail: { action } }));
  };

  return (
    <header className="admin-header">
      <div className="header-left">
        <button 
          className="mobile-menu-toggle"
          onClick={onToggleSidebar}
        >
          ☰
        </button>
        
        <div className="breadcrumb">
          {current.breadcrumb.map((crumb, index) => (
            <span key={index} className="breadcrumb-item">
              {index > 0 && <span className="breadcrumb-separator">/</span>}
              {crumb}
            </span>
          ))}
        </div>
        
        <h1 className="page-title">{current.title}</h1>
      </div>
      
      <div className="header-center">
        <div className="search-container">
          <input 
            type="text" 
            placeholder="Search admin content..."
            className="global-search"
          />
          <span className="search-icon">🔍</span>
        </div>
      </div>
      
      <div className="header-right">
        <div className="quick-actions">
          {quickActions.map(action => (
            <button
              key={action.id}
              className="quick-action-btn"
              onClick={() => handleQuickAction(action.action)}
              title={action.label}
            >
              <span className="action-icon">{action.icon}</span>
              <span className="action-label">{action.label}</span>
            </button>
          ))}
        </div>
        
        <div className="user-menu">
          <button 
            className="user-menu-trigger"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <span className="user-avatar">👤</span>
            <span className="user-name">{user?.full_name || user?.username}</span>
            <span className="dropdown-arrow">▼</span>
          </button>
          
          {showUserMenu && (
            <div className="user-menu-dropdown">
              <div className="user-info">
                <div className="user-name">{user?.full_name || user?.username}</div>
                <div className="user-role">Administrator</div>
              </div>
              <hr />
              <button className="menu-item" onClick={() => window.location.reload()}>
                <span className="menu-icon">🏠</span>
                Go to Report Generation
              </button>
              <button className="menu-item" onClick={onLogout}>
                <span className="menu-icon">🚪</span>
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
