import { useState, useEffect } from 'react';
import dataService from '../services/dataService';
import AdminLayout from './AdminLayout';

// Import the full-page manager components
import GradeManagerPage from './admin/GradeManagerPage';
import SubjectManagerPage from './admin/SubjectManagerPage';
import CategoryManagerPage from './admin/CategoryManagerPage';
import AssessmentPointManagerPage from './admin/AssessmentPointManagerPage';
import VersionManagerPage from './admin/VersionManagerPage';
import DashboardOverview from './admin/DashboardOverview';
import SchoolSettingsManagerPage from './admin/SchoolSettingsManagerPage';
import TemplateManagerPage from './admin/TemplateManagerPage';
import BulkOperationsManagerPage from './admin/BulkOperationsManagerPage';
import DataExportManagerPage from './admin/DataExportManagerPage';
import MigrationManagerPage from './admin/MigrationManagerPage';
import AnalyticsManagerPage from './admin/AnalyticsManagerPage';

const AdminDashboard = ({ user, onLogout }) => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadStats();
    
    // Listen for navigation events from sidebar
    const handleNavigation = (event) => {
      setCurrentPage(event.detail.page);
    };
    
    // Listen for quick action events from header
    const handleQuickAction = (event) => {
      const { action } = event.detail;
      switch (action) {
        case 'add-assessment':
          setCurrentPage('assessment-points');
          break;
        case 'add-subject':
          setCurrentPage('subjects');
          break;
        case 'publish-version':
          setCurrentPage('versions');
          break;
        case 'import-data':
          setCurrentPage('migration');
          break;
        case 'bulk-operations':
          setCurrentPage('bulk-operations');
          break;
        case 'export-data':
          setCurrentPage('export');
          break;
        default:
          break;
      }
    };

    window.addEventListener('admin-navigate', handleNavigation);
    window.addEventListener('admin-quick-action', handleQuickAction);
    
    return () => {
      window.removeEventListener('admin-navigate', handleNavigation);
      window.removeEventListener('admin-quick-action', handleQuickAction);
    };
  }, []);

  const loadStats = async () => {
    try {
      const data = await dataService.getConfigStats();
      setStats(data);
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Error loading stats:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <DashboardOverview 
            stats={stats}
            loading={loading}
            error={error}
            onRefresh={loadStats}
            onNavigate={setCurrentPage}
          />
        );
      case 'grades':
        return <GradeManagerPage onNavigate={setCurrentPage} />;
      case 'subjects':
        return <SubjectManagerPage onNavigate={setCurrentPage} />;
      case 'categories':
        return <CategoryManagerPage onNavigate={setCurrentPage} />;
      case 'assessment-points':
        return <AssessmentPointManagerPage onNavigate={setCurrentPage} />;
      case 'versions':
        return <VersionManagerPage onNavigate={setCurrentPage} />;
      case 'school-settings':
        return <SchoolSettingsManagerPage onNavigate={setCurrentPage} />;
      case 'templates':
        return <TemplateManagerPage onNavigate={setCurrentPage} />;
      case 'bulk-operations':
        return <BulkOperationsManagerPage onNavigate={setCurrentPage} />;
      case 'export':
        return <DataExportManagerPage onNavigate={setCurrentPage} />;
      case 'migration':
        return <MigrationManagerPage onNavigate={setCurrentPage} />;
      case 'analytics':
        return <AnalyticsManagerPage onNavigate={setCurrentPage} />;
      default:
        return (
          <div className="admin-page">
            <div className="admin-page-header">
              <h1 className="admin-page-title">Page Not Found</h1>
              <p className="admin-page-description">
                The requested page could not be found.
              </p>
            </div>
            <div className="admin-page-content">
              <button 
                className="btn btn-primary"
                onClick={() => setCurrentPage('dashboard')}
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <AdminLayout 
      currentPage={currentPage}
      user={user}
      onLogout={onLogout}
    >
      {renderCurrentPage()}
    </AdminLayout>
  );
};

export default AdminDashboard;
