import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const DataExportManagerPage = ({ onNavigate }) => {
  const [exportHistory, setExportHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedExportType, setSelectedExportType] = useState('');
  const [exportFilters, setExportFilters] = useState({
    grade_level: '',
    subject: '',
    date_from: '',
    date_to: '',
    include_inactive: false
  });

  useEffect(() => {
    loadExportHistory();
  }, []);

  const loadExportHistory = async () => {
    try {
      // Mock export history
      const mockHistory = [
        {
          id: 1,
          export_type: 'assessment_points',
          file_name: 'assessment_points_2024-09-21.csv',
          file_size: '45.2 KB',
          records_count: 156,
          status: 'completed',
          created_at: '2024-09-21T10:30:00Z',
          download_url: '/exports/assessment_points_2024-09-21.csv'
        },
        {
          id: 2,
          export_type: 'subjects',
          file_name: 'subjects_grade_5_2024-09-20.csv',
          file_size: '12.8 KB',
          records_count: 24,
          status: 'completed',
          created_at: '2024-09-20T15:45:00Z',
          download_url: '/exports/subjects_grade_5_2024-09-20.csv'
        }
      ];
      setExportHistory(mockHistory);
    } catch (err) {
      console.error('Error loading export history:', err);
    }
  };

  const exportTypes = [
    {
      id: 'assessment_points',
      title: 'Assessment Points',
      description: 'Export all assessment criteria and points',
      icon: '✓',
      fields: ['ID', 'Point Text', 'Category', 'Type', 'Grade Range', 'Status', 'Created Date']
    },
    {
      id: 'subjects',
      title: 'Subjects',
      description: 'Export subject configuration and grade assignments',
      icon: '📚',
      fields: ['ID', 'Name', 'Display Name', 'Grade From', 'Grade To', 'Compulsory', 'Status']
    },
    {
      id: 'categories',
      title: 'Categories',
      description: 'Export assessment categories and organization',
      icon: '📋',
      fields: ['ID', 'Name', 'Display Name', 'Type', 'Description', 'Sort Order', 'Status']
    },
    {
      id: 'grades',
      title: 'Grade Levels',
      description: 'Export grade level definitions and settings',
      icon: '🎓',
      fields: ['ID', 'Level', 'Name', 'Code', 'Age Range', 'Description', 'Status']
    },
    {
      id: 'configuration',
      title: 'Full Configuration',
      description: 'Export complete system configuration as backup',
      icon: '⚙️',
      fields: ['All system settings', 'Assessment points', 'Subjects', 'Categories', 'Grades']
    },
    {
      id: 'audit_log',
      title: 'Audit Log',
      description: 'Export system activity and change history',
      icon: '📊',
      fields: ['Timestamp', 'User', 'Action', 'Entity', 'Changes', 'IP Address']
    }
  ];

  const handleExport = async () => {
    if (!selectedExportType) {
      setError('Please select an export type');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create mock export record
      const newExport = {
        id: exportHistory.length + 1,
        export_type: selectedExportType,
        file_name: `${selectedExportType}_${new Date().toISOString().split('T')[0]}.csv`,
        file_size: `${Math.floor(Math.random() * 100) + 10}.${Math.floor(Math.random() * 9)} KB`,
        records_count: Math.floor(Math.random() * 200) + 50,
        status: 'completed',
        created_at: new Date().toISOString(),
        download_url: `/exports/${selectedExportType}_${new Date().toISOString().split('T')[0]}.csv`
      };

      setExportHistory(prev => [newExport, ...prev]);
      
      // Simulate file download
      const exportType = exportTypes.find(type => type.id === selectedExportType);
      const csvContent = `${exportType.fields.join(',')}\n` + 
                        Array.from({length: newExport.records_count}, (_, i) => 
                          exportType.fields.map(() => `Sample Data ${i + 1}`).join(',')
                        ).join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = newExport.file_name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      alert('Export completed and download started!');
    } catch (err) {
      setError(err.message || 'Export failed');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    setExportFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const downloadExport = (exportItem) => {
    // Simulate download
    alert(`Downloading ${exportItem.file_name}...`);
  };

  const deleteExport = (exportId) => {
    if (confirm('Are you sure you want to delete this export?')) {
      setExportHistory(prev => prev.filter(item => item.id !== exportId));
    }
  };

  const formatFileSize = (sizeStr) => {
    return sizeStr; // Already formatted in mock data
  };

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Data Export</h1>
        <p className="admin-page-description">
          Export system data for backup, analysis, or migration purposes. 
          Choose from various export formats and apply filters to customize your data export.
        </p>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Export Configuration */}
        <div className="form-section">
          <h3>Create New Export</h3>
          
          <div className="export-types-grid">
            {exportTypes.map(type => (
              <div 
                key={type.id} 
                className={`export-type-card ${selectedExportType === type.id ? 'selected' : ''}`}
                onClick={() => setSelectedExportType(type.id)}
              >
                <div className="export-type-header">
                  <span className="export-icon">{type.icon}</span>
                  <h4>{type.title}</h4>
                </div>
                <p className="export-description">{type.description}</p>
                <div className="export-fields">
                  <strong>Includes:</strong>
                  <ul>
                    {type.fields.slice(0, 3).map((field, index) => (
                      <li key={index}>{field}</li>
                    ))}
                    {type.fields.length > 3 && <li>...and {type.fields.length - 3} more</li>}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          {/* Export Filters */}
          {selectedExportType && (
            <div className="export-filters">
              <h4>Export Filters</h4>
              <div className="filter-row">
                <div className="form-group">
                  <label htmlFor="grade_level">Grade Level</label>
                  <select
                    id="grade_level"
                    name="grade_level"
                    value={exportFilters.grade_level}
                    onChange={handleFilterChange}
                    className="form-control"
                  >
                    <option value="">All Grades</option>
                    <option value="K">Kindergarten</option>
                    <option value="1">Grade 1</option>
                    <option value="2">Grade 2</option>
                    <option value="3">Grade 3</option>
                    <option value="4">Grade 4</option>
                    <option value="5">Grade 5</option>
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="date_from">Date From</label>
                  <input
                    type="date"
                    id="date_from"
                    name="date_from"
                    value={exportFilters.date_from}
                    onChange={handleFilterChange}
                    className="form-control"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="date_to">Date To</label>
                  <input
                    type="date"
                    id="date_to"
                    name="date_to"
                    value={exportFilters.date_to}
                    onChange={handleFilterChange}
                    className="form-control"
                  />
                </div>
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="include_inactive"
                    checked={exportFilters.include_inactive}
                    onChange={handleFilterChange}
                  />
                  Include inactive records
                </label>
              </div>
            </div>
          )}

          <div className="form-actions">
            <button
              onClick={handleExport}
              disabled={loading || !selectedExportType}
              className="btn btn-primary"
            >
              {loading ? 'Exporting...' : 'Start Export'}
            </button>
          </div>
        </div>

        {/* Export History */}
        <div className="form-section">
          <h3>Export History</h3>
          
          {exportHistory.length === 0 ? (
            <p className="no-data">No exports created yet.</p>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Export Type</th>
                    <th>File Name</th>
                    <th>Records</th>
                    <th>File Size</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {exportHistory.map(exportItem => (
                    <tr key={exportItem.id}>
                      <td>{exportItem.export_type.replace(/_/g, ' ')}</td>
                      <td>{exportItem.file_name}</td>
                      <td>{exportItem.records_count.toLocaleString()}</td>
                      <td>{formatFileSize(exportItem.file_size)}</td>
                      <td>
                        <span className={`status-badge ${exportItem.status === 'completed' ? 'active' : 'draft'}`}>
                          {exportItem.status}
                        </span>
                      </td>
                      <td>{new Date(exportItem.created_at).toLocaleString()}</td>
                      <td>
                        <div className="action-buttons">
                          <button
                            onClick={() => downloadExport(exportItem)}
                            className="btn btn-small btn-primary"
                            title="Download file"
                          >
                            Download
                          </button>
                          <button
                            onClick={() => deleteExport(exportItem.id)}
                            className="btn btn-small btn-danger"
                            title="Delete export"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataExportManagerPage;
