import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';
import FormModal from '../common/FormModal';

const AssessmentPointManagerPage = ({ onNavigate }) => {
  const [assessmentPoints, setAssessmentPoints] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingPoint, setEditingPoint] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [filters, setFilters] = useState({
    category_id: '',
    subject_id: '',
    point_type: '',
    grade: ''
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [currentVersion, setCurrentVersion] = useState(null);
  const [hasUnpublishedChanges, setHasUnpublishedChanges] = useState(false);
  const [formData, setFormData] = useState({
    category_id: '',
    subject_id: '',
    point_text: '',
    point_type: 'behavior',
    grade_from: '',
    grade_to: '',
    sort_order: '',
    is_active: true
  });

  const pointTypes = [
    { value: 'behavior', label: 'Behavior', icon: '👤' },
    { value: 'academic', label: 'Academic', icon: '📚' },
    { value: 'skill', label: 'Skill', icon: '🛠️' },
    { value: 'attitude', label: 'Attitude', icon: '😊' },
    { value: 'social', label: 'Social', icon: '👥' },
    { value: 'emotional', label: 'Emotional', icon: '❤️' }
  ];

  const pronounPlaceholders = [
    { placeholder: '{he/she}', description: 'Student pronoun (he or she)' },
    { placeholder: '{his/her}', description: 'Possessive pronoun (his or her)' },
    { placeholder: '{him/her}', description: 'Object pronoun (him or her)' },
    { placeholder: '{He/She}', description: 'Capitalized student pronoun' },
    { placeholder: '{His/Her}', description: 'Capitalized possessive pronoun' },
    { placeholder: '{student}', description: 'Student\'s name' },
    { placeholder: '{Student}', description: 'Capitalized student\'s name' }
  ];

  const insertPronoun = (placeholder) => {
    const textarea = document.querySelector('textarea[name="point_text"]');
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = formData.point_text;
      const newText = text.substring(0, start) + placeholder + text.substring(end);
      setFormData(prev => ({ ...prev, point_text: newText }));

      // Set cursor position after the inserted placeholder
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
      }, 0);
    }
  };

  useEffect(() => {
    loadData();
    loadCurrentVersion();
  }, [filters]);

  const loadCurrentVersion = async () => {
    try {
      const version = await dataService.getCurrentVersion();
      setCurrentVersion(version);
    } catch (err) {
      console.error('Error loading current version:', err);
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const [pointsData, categoriesData, subjectsData, gradesData] = await Promise.all([
        dataService.getAssessmentPoints(
          filters.category_id && filters.category_id !== '' ? filters.category_id : null,
          filters.subject_id && filters.subject_id !== '' ? filters.subject_id : null,
          filters.point_type && filters.point_type !== '' ? filters.point_type : null,
          filters.grade && filters.grade !== '' ? filters.grade : null
        ),
        dataService.getCategories(null, true), // Only active categories
        dataService.getSubjects(null, true), // Only active subjects
        dataService.getGrades(true) // Only active grades
      ]);
      setAssessmentPoints(pointsData);
      setCategories(categoriesData);
      setSubjects(subjectsData);
      setGrades(gradesData);
      setError('');
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const pointData = {
        ...formData,
        category_id: formData.category_id ? parseInt(formData.category_id) : null,
        subject_id: formData.subject_id ? parseInt(formData.subject_id) : null,
        grade_from: formData.grade_from ? parseInt(formData.grade_from) : null,
        grade_to: formData.grade_to ? parseInt(formData.grade_to) : null,
        sort_order: parseInt(formData.sort_order) || 0
      };

      if (editingPoint) {
        await dataService.updateAssessmentPoint(editingPoint.id, pointData);
      } else {
        await dataService.createAssessmentPoint(pointData);
      }

      await loadData();
      resetForm();
      setError('');
      setHasUnpublishedChanges(true); // Mark that there are unpublished changes
    } catch (err) {
      setError(err.message || 'Failed to save assessment point');
    }
  };

  const handleEdit = (point) => {
    setEditingPoint(point);
    setFormData({
      category_id: point.category_id?.toString() || '',
      subject_id: point.subject_id?.toString() || '',
      point_text: point.point_text,
      point_type: point.point_type,
      grade_from: point.grade_from?.toString() || '',
      grade_to: point.grade_to?.toString() || '',
      sort_order: point.sort_order.toString(),
      is_active: point.is_active
    });
    setShowAddForm(true);
  };

  const handleDelete = async (pointId) => {
    if (!window.confirm('Are you sure you want to delete this assessment point? This action cannot be undone.')) {
      return;
    }

    try {
      await dataService.deleteAssessmentPoint(pointId);
      await loadData();
      setError('');
    } catch (err) {
      setError(err.message || 'Failed to delete assessment point');
    }
  };

  const resetForm = () => {
    setFormData({
      category_id: '',
      subject_id: '',
      point_text: '',
      point_type: 'behavior',
      grade_from: '',
      grade_to: '',
      sort_order: '',
      is_active: true
    });
    setEditingPoint(null);
    setShowAddForm(false);
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.display_name : 'N/A';
  };

  const getSubjectName = (subjectId) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.display_name : 'N/A';
  };

  const getGradeName = (gradeLevel) => {
    const grade = grades.find(g => g.grade_level === gradeLevel);
    return grade ? grade.grade_code : gradeLevel?.toString() || 'N/A';
  };

  const getGradeRange = (gradeFrom, gradeTo) => {
    if (!gradeFrom && !gradeTo) return 'All Grades';
    if (gradeFrom === gradeTo) return getGradeName(gradeFrom);
    return `${getGradeName(gradeFrom)} - ${getGradeName(gradeTo)}`;
  };

  // Filter assessment points based on search
  const filteredPoints = assessmentPoints.filter(point => {
    const matchesSearch = !searchTerm || 
      point.point_text.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Assessment Points...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Assessment Point Management</h1>
        <p className="admin-page-description">
          Create and manage individual assessment criteria for student evaluation.
          Assessment points are the building blocks of your report card system.
        </p>

        {currentVersion && (
          <div style={{
            marginTop: '12px',
            padding: '8px 12px',
            background: hasUnpublishedChanges ? '#fef3c7' : '#f0f9ff',
            border: `1px solid ${hasUnpublishedChanges ? '#f59e0b' : '#3b82f6'}`,
            borderRadius: '6px',
            fontSize: '0.875rem',
            color: hasUnpublishedChanges ? '#92400e' : '#1e40af'
          }}>
            📋 <strong>Current Version:</strong> {currentVersion.version_name}
            {hasUnpublishedChanges && (
              <span style={{ marginLeft: '8px', fontWeight: 'bold' }}>
                ⚠️ You have unpublished changes
              </span>
            )}
          </div>
        )}

        <div className="admin-page-actions">
          <div className="admin-page-filters">
            <div className="filter-group">
              <label>Search:</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search assessment points..."
                className="filter-input"
              />
            </div>
            
            <div className="filter-group">
              <label>Category:</label>
              <select 
                name="category_id"
                value={filters.category_id} 
                onChange={handleFilterChange}
                className="filter-select"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.display_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="filter-group">
              <label>Subject:</label>
              <select 
                name="subject_id"
                value={filters.subject_id} 
                onChange={handleFilterChange}
                className="filter-select"
              >
                <option value="">All Subjects</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.display_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="filter-group">
              <label>Type:</label>
              <select 
                name="point_type"
                value={filters.point_type} 
                onChange={handleFilterChange}
                className="filter-select"
              >
                <option value="">All Types</option>
                {pointTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="filter-group">
              <label>Grade:</label>
              <select 
                name="grade"
                value={filters.grade} 
                onChange={handleFilterChange}
                className="filter-select"
              >
                <option value="">All Grades</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.grade_level}>
                    {grade.grade_name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="btn btn-primary"
          >
            ➕ Add Assessment Point
          </button>

          {hasUnpublishedChanges && (
            <button
              onClick={() => onNavigate('version-management')}
              className="btn btn-warning"
              style={{
                background: '#f59e0b',
                color: 'white',
                animation: 'pulse 2s infinite'
              }}
            >
              📦 Publish Changes
            </button>
          )}

          <button
            onClick={loadData}
            className="btn btn-secondary"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Quick Stats */}
        <div className="admin-stats-grid" style={{ marginBottom: '24px' }}>
          <div className="admin-stat-card">
            <div className="stat-icon">✓</div>
            <div className="stat-value">{filteredPoints.length}</div>
            <div className="stat-label">Filtered Points</div>
          </div>
          <div className="admin-stat-card">
            <div className="stat-icon">📋</div>
            <div className="stat-value">{categories.length}</div>
            <div className="stat-label">Categories</div>
          </div>
          <div className="admin-stat-card">
            <div className="stat-icon">📚</div>
            <div className="stat-value">{subjects.length}</div>
            <div className="stat-label">Subjects</div>
          </div>
          <div className="admin-stat-card">
            <div className="stat-icon">🎓</div>
            <div className="stat-value">{grades.length}</div>
            <div className="stat-label">Grades</div>
          </div>
        </div>

        {/* Add/Edit Modal */}
        <FormModal
          isOpen={showAddForm}
          onClose={resetForm}
          title={editingPoint ? 'Edit Assessment Point' : 'Add Assessment Point'}
          onSubmit={handleSubmit}
          submitLabel={editingPoint ? 'Update' : 'Create'}
          size="large"
        >
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Point Type *</label>
              <select
                name="point_type"
                value={formData.point_type}
                onChange={handleInputChange}
                required
                className="form-select"
              >
                {pointTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Category</label>
              <select
                name="category_id"
                value={formData.category_id}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="">No category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.display_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Subject</label>
              <select
                name="subject_id"
                value={formData.subject_id}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="">No subject</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.display_name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label className="form-label">Grade From</label>
              <select
                name="grade_from"
                value={formData.grade_from}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="">Any grade</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.grade_level}>
                    {grade.grade_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Grade To</label>
              <select
                name="grade_to"
                value={formData.grade_to}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="">Any grade</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.grade_level}>
                    {grade.grade_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Sort Order</label>
              <input
                type="number"
                name="sort_order"
                value={formData.sort_order}
                onChange={handleInputChange}
                min="0"
                placeholder="Display order"
                className="form-input"
              />
            </div>
          </div>

          <div className="form-group">
            <div className="form-checkbox-group">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="form-checkbox"
                id="is_active"
              />
              <label htmlFor="is_active" className="form-label">Active</label>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Assessment Point Text *</label>
            <textarea
              name="point_text"
              value={formData.point_text}
              onChange={handleInputChange}
              required
              rows="4"
              placeholder="Enter the assessment point description..."
              className="form-textarea"
            />
          </div>

          {/* Pronoun Helper */}
          <div className="card">
            <div className="card-header">
              <h4 className="card-title">Quick Insert Pronouns</h4>
            </div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginBottom: '12px' }}>
              {pronounPlaceholders.map((item, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => insertPronoun(item.placeholder)}
                  className="btn btn-outline"
                  style={{ fontSize: '0.75rem', padding: '4px 8px' }}
                  title={item.description}
                >
                  {item.placeholder}
                </button>
              ))}
            </div>
            <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: 0 }}>
              Click buttons to insert pronouns at cursor position.
            </p>
          </div>
        </FormModal>

        {/* Assessment Points Table */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">
              Assessment Points ({filteredPoints.length} of {assessmentPoints.length})
            </h3>
          </div>

          <table className="data-table">
            <thead>
              <tr>
                <th>Assessment Point</th>
                <th>Type</th>
                <th>Category</th>
                <th>Subject</th>
                <th>Grade Range</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPoints.map(point => (
                <tr
                  key={point.id}
                  className="clickable-row"
                  onClick={() => handleEdit(point)}
                  style={{ cursor: 'pointer' }}
                >
                  <td style={{ maxWidth: '300px' }}>
                    <div style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      lineHeight: '1.4'
                    }}>
                      {point.point_text}
                    </div>
                  </td>
                  <td>
                    <span style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '4px',
                      padding: '4px 8px',
                      background: '#f3f4f6',
                      borderRadius: '4px',
                      fontSize: '0.875rem'
                    }}>
                      {pointTypes.find(t => t.value === point.point_type)?.icon}
                      {pointTypes.find(t => t.value === point.point_type)?.label || point.point_type}
                    </span>
                  </td>
                  <td>{getCategoryName(point.category_id)}</td>
                  <td>{getSubjectName(point.subject_id)}</td>
                  <td>{getGradeRange(point.grade_from, point.grade_to)}</td>
                  <td>
                    <span className={`status-badge ${point.is_active ? 'active' : 'inactive'}`}>
                      {point.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td onClick={(e) => e.stopPropagation()}>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <button
                        onClick={() => handleEdit(point)}
                        className="btn btn-outline"
                        style={{ padding: '4px 8px', fontSize: '0.75rem' }}
                        title="Edit assessment point"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(point.id)}
                        className="btn btn-danger"
                        style={{ padding: '4px 8px', fontSize: '0.75rem' }}
                        title="Delete assessment point"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {filteredPoints.length === 0 && (
            <div style={{ padding: '48px 24px', textAlign: 'center', color: '#6b7280' }}>
              <div style={{ fontSize: '3rem', marginBottom: '16px' }}>✓</div>
              <h3 style={{ margin: '0 0 8px 0', fontSize: '1.125rem' }}>
                {assessmentPoints.length === 0 ? 'No assessment points configured' : 'No assessment points match your filters'}
              </h3>
              <p style={{ margin: 0, fontSize: '0.875rem' }}>
                {assessmentPoints.length === 0 
                  ? 'Start by adding assessment points to evaluate student performance.'
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentPointManagerPage;
