import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const GradeManagerPage = ({ onNavigate }) => {
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingGrade, setEditingGrade] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    grade_level: '',
    grade_name: '',
    grade_code: '',
    description: '',
    age_range: '',
    sort_order: '',
    is_active: true
  });

  useEffect(() => {
    loadGrades();
  }, []);

  const loadGrades = async () => {
    try {
      setLoading(true);
      const data = await dataService.getGrades();
      setGrades(data);
      setError('');
    } catch (err) {
      setError('Failed to load grades');
      console.error('Error loading grades:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const gradeData = {
        ...formData,
        grade_level: parseInt(formData.grade_level),
        sort_order: parseInt(formData.sort_order) || 0
      };

      if (editingGrade) {
        await dataService.updateGrade(editingGrade.id, gradeData);
      } else {
        await dataService.createGrade(gradeData);
      }

      await loadGrades();
      resetForm();
      setError('');
    } catch (err) {
      setError(err.message || 'Failed to save grade');
    }
  };

  const handleEdit = (grade) => {
    setEditingGrade(grade);
    setFormData({
      grade_level: grade.grade_level.toString(),
      grade_name: grade.grade_name,
      grade_code: grade.grade_code,
      description: grade.description || '',
      age_range: grade.age_range || '',
      sort_order: grade.sort_order.toString(),
      is_active: grade.is_active
    });
    setShowAddForm(true);
  };

  const handleDelete = async (gradeId) => {
    if (!window.confirm('Are you sure you want to delete this grade? This action cannot be undone.')) {
      return;
    }

    try {
      await dataService.deleteGrade(gradeId);
      await loadGrades();
      setError('');
    } catch (err) {
      setError(err.message || 'Failed to delete grade');
    }
  };

  const resetForm = () => {
    setFormData({
      grade_level: '',
      grade_name: '',
      grade_code: '',
      description: '',
      age_range: '',
      sort_order: '',
      is_active: true
    });
    setEditingGrade(null);
    setShowAddForm(false);
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Grades...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Grade Management</h1>
        <p className="admin-page-description">
          Configure grade levels, names, and age ranges for your school system. 
          Grades form the foundation for subject and assessment organization.
        </p>
        
        <div className="admin-page-actions">
          <button 
            onClick={() => setShowAddForm(!showAddForm)}
            className="btn btn-primary"
          >
            {showAddForm ? '✕ Cancel' : '➕ Add New Grade'}
          </button>
          
          <button 
            onClick={loadGrades}
            className="btn btn-secondary"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Add/Edit Form */}
        {showAddForm && (
          <div style={{ 
            background: '#fff', 
            border: '1px solid #e5e7eb', 
            borderRadius: '8px', 
            padding: '24px', 
            marginBottom: '24px' 
          }}>
            <h3 style={{ margin: '0 0 16px 0', fontSize: '1.25rem', fontWeight: '600' }}>
              {editingGrade ? 'Edit Grade' : 'Add New Grade'}
            </h3>
            
            <form onSubmit={handleSubmit}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
                <div className="form-group">
                  <label>Grade Level (Number) *</label>
                  <input
                    type="number"
                    name="grade_level"
                    value={formData.grade_level}
                    onChange={handleInputChange}
                    required
                    min="0"
                    max="20"
                    placeholder="e.g., 0 for K, 1 for Grade 1"
                  />
                </div>

                <div className="form-group">
                  <label>Grade Name *</label>
                  <input
                    type="text"
                    name="grade_name"
                    value={formData.grade_name}
                    onChange={handleInputChange}
                    required
                    placeholder="e.g., Kindergarten, Grade 1"
                  />
                </div>

                <div className="form-group">
                  <label>Grade Code *</label>
                  <input
                    type="text"
                    name="grade_code"
                    value={formData.grade_code}
                    onChange={handleInputChange}
                    required
                    placeholder="e.g., K, 1, 2"
                  />
                </div>

                <div className="form-group">
                  <label>Age Range</label>
                  <input
                    type="text"
                    name="age_range"
                    value={formData.age_range}
                    onChange={handleInputChange}
                    placeholder="e.g., 5-6 years"
                  />
                </div>

                <div className="form-group">
                  <label>Sort Order</label>
                  <input
                    type="number"
                    name="sort_order"
                    value={formData.sort_order}
                    onChange={handleInputChange}
                    min="0"
                    placeholder="Display order"
                  />
                </div>

                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleInputChange}
                    />
                    Active
                  </label>
                </div>
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Optional description for this grade level"
                />
              </div>

              <div className="form-actions">
                <button type="submit" className="btn btn-primary">
                  {editingGrade ? 'Update Grade' : 'Create Grade'}
                </button>
                <button type="button" onClick={resetForm} className="btn btn-secondary">
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Grades Table */}
        <div className="admin-table-container">
          <div style={{ padding: '16px 24px', borderBottom: '1px solid #e5e7eb' }}>
            <h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: '600' }}>
              Existing Grades ({grades.length})
            </h3>
          </div>
          
          <table className="admin-table">
            <thead>
              <tr>
                <th>Level</th>
                <th>Name</th>
                <th>Code</th>
                <th>Age Range</th>
                <th>Sort Order</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {grades.map(grade => (
                <tr key={grade.id}>
                  <td style={{ fontWeight: '600' }}>{grade.grade_level}</td>
                  <td>{grade.grade_name}</td>
                  <td>
                    <span style={{ 
                      background: '#f3f4f6', 
                      padding: '4px 8px', 
                      borderRadius: '4px',
                      fontFamily: 'monospace',
                      fontSize: '0.875rem'
                    }}>
                      {grade.grade_code}
                    </span>
                  </td>
                  <td>{grade.age_range || 'N/A'}</td>
                  <td>{grade.sort_order}</td>
                  <td>
                    <span className={`status-badge ${grade.is_active ? 'active' : 'inactive'}`}>
                      {grade.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>
                    <div className="action-buttons">
                      <button 
                        onClick={() => handleEdit(grade)}
                        className="btn-icon edit"
                        title="Edit grade"
                      >
                        ✏️
                      </button>
                      <button 
                        onClick={() => handleDelete(grade.id)}
                        className="btn-icon delete"
                        title="Delete grade"
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {grades.length === 0 && (
            <div style={{ padding: '48px 24px', textAlign: 'center', color: '#6b7280' }}>
              <div style={{ fontSize: '3rem', marginBottom: '16px' }}>🎓</div>
              <h3 style={{ margin: '0 0 8px 0', fontSize: '1.125rem' }}>No grades configured</h3>
              <p style={{ margin: 0, fontSize: '0.875rem' }}>
                Start by adding your first grade level to organize your school system.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GradeManagerPage;
