import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const TemplateManagerPage = ({ onNavigate }) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [formData, setFormData] = useState({
    template_name: '',
    description: '',
    template_type: 'report_card',
    is_default: false
  });

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      // For now, we'll use mock data since the backend endpoint might not exist yet
      const mockTemplates = [
        {
          id: 1,
          template_name: 'Standard Report Card',
          description: 'Default template for elementary report cards',
          template_type: 'report_card',
          file_path: '/templates/standard_report.docx',
          is_default: true,
          is_active: true,
          uploaded_by: 'admin',
          created_at: '2024-01-15T10:00:00Z',
          file_size: '45.2 KB'
        },
        {
          id: 2,
          template_name: 'Detailed Assessment',
          description: 'Comprehensive template with detailed assessment criteria',
          template_type: 'detailed_report',
          file_path: '/templates/detailed_assessment.docx',
          is_default: false,
          is_active: true,
          uploaded_by: 'admin',
          created_at: '2024-02-01T14:30:00Z',
          file_size: '67.8 KB'
        }
      ];
      setTemplates(mockTemplates);
      setError('');
    } catch (err) {
      setError('Failed to load templates');
      console.error('Error loading templates:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a valid Word document (.docx or .doc)');
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setError('File size must be less than 10MB');
        return;
      }

      setSelectedFile(file);
      setError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('Please select a template file');
      return;
    }

    try {
      setUploading(true);
      
      // TODO: Implement actual file upload when backend endpoint is ready
      // const uploadData = new FormData();
      // uploadData.append('file', selectedFile);
      // uploadData.append('template_name', formData.template_name);
      // uploadData.append('description', formData.description);
      // uploadData.append('template_type', formData.template_type);
      // uploadData.append('is_default', formData.is_default);
      // await dataService.uploadTemplate(uploadData);
      
      // For now, just simulate upload
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Add mock template to list
      const newTemplate = {
        id: templates.length + 1,
        template_name: formData.template_name,
        description: formData.description,
        template_type: formData.template_type,
        file_path: `/templates/${selectedFile.name}`,
        is_default: formData.is_default,
        is_active: true,
        uploaded_by: 'admin',
        created_at: new Date().toISOString(),
        file_size: `${(selectedFile.size / 1024).toFixed(1)} KB`
      };
      
      setTemplates(prev => [...prev, newTemplate]);
      
      // Reset form
      setFormData({
        template_name: '',
        description: '',
        template_type: 'report_card',
        is_default: false
      });
      setSelectedFile(null);
      document.getElementById('template-file').value = '';
      setError('');
      
      alert('Template uploaded successfully!');
    } catch (err) {
      setError(err.message || 'Failed to upload template');
    } finally {
      setUploading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSetDefault = async (templateId) => {
    try {
      // TODO: Implement actual API call
      // await dataService.setDefaultTemplate(templateId);
      
      // For now, just update local state
      setTemplates(prev => prev.map(template => ({
        ...template,
        is_default: template.id === templateId
      })));
      
      alert('Default template updated successfully!');
    } catch (err) {
      setError(err.message || 'Failed to set default template');
    }
  };

  const handleDownload = async (template) => {
    try {
      // TODO: Implement actual file download when backend is ready
      // For now, simulate download

      // Create a mock Word document content
      const mockDocContent = `
        Report Card Template: ${template.template_name}

        Description: ${template.description}
        Type: ${template.template_type}

        This is a mock template file. In production, this would download the actual Word document.

        Template Features:
        - School logo placeholder
        - Student information section
        - Assessment criteria table
        - Teacher comments section
        - Principal signature area
      `;

      const blob = new Blob([mockDocContent], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.template_name.replace(/\s+/g, '_')}.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      alert('Template download started!');
    } catch (err) {
      setError(err.message || 'Failed to download template');
    }
  };

  const handleDelete = async (templateId) => {
    if (!confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      // TODO: Implement actual API call
      // await dataService.deleteTemplate(templateId);

      // For now, just update local state
      setTemplates(prev => prev.filter(template => template.id !== templateId));

      alert('Template deleted successfully!');
    } catch (err) {
      setError(err.message || 'Failed to delete template');
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-header">
          <h1 className="admin-page-title">Template Management</h1>
        </div>
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading templates...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Template Management</h1>
        <p className="admin-page-description">
          Manage Word document templates for report cards and other school documents. 
          Upload custom templates or modify existing ones to match your school's format and branding.
        </p>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Upload New Template Form */}
        <div className="form-section">
          <h3>Upload New Template</h3>
          
          <form onSubmit={handleSubmit} className="admin-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="template_name">Template Name *</label>
                <input
                  type="text"
                  id="template_name"
                  name="template_name"
                  value={formData.template_name}
                  onChange={handleInputChange}
                  required
                  className="form-control"
                  placeholder="Enter template name"
                />
              </div>

              <div className="form-group">
                <label htmlFor="template_type">Template Type</label>
                <select
                  id="template_type"
                  name="template_type"
                  value={formData.template_type}
                  onChange={handleInputChange}
                  className="form-control"
                >
                  <option value="report_card">Report Card</option>
                  <option value="detailed_report">Detailed Report</option>
                  <option value="summary_report">Summary Report</option>
                  <option value="progress_report">Progress Report</option>
                  <option value="certificate">Certificate</option>
                </select>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="form-control"
                rows="2"
                placeholder="Describe this template's purpose and features"
              />
            </div>

            <div className="form-group">
              <label htmlFor="template-file">Template File *</label>
              <input
                type="file"
                id="template-file"
                accept=".docx,.doc"
                onChange={handleFileSelect}
                className="form-control"
              />
              <small className="form-help">
                Upload a Word document (.docx or .doc). Maximum file size: 10MB.
              </small>
              {selectedFile && (
                <div className="file-info">
                  <strong>Selected:</strong> {selectedFile.name} ({formatFileSize(selectedFile.size)})
                </div>
              )}
            </div>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="is_default"
                  checked={formData.is_default}
                  onChange={handleInputChange}
                />
                Set as default template
              </label>
            </div>

            <div className="form-actions">
              <button
                type="submit"
                disabled={uploading}
                className="btn btn-primary"
              >
                {uploading ? 'Uploading...' : 'Upload Template'}
              </button>
            </div>
          </form>
        </div>

        {/* Existing Templates */}
        <div className="form-section">
          <h3>Existing Templates ({templates.length})</h3>
          
          {templates.length === 0 ? (
            <p className="no-data">No templates found. Upload your first template above.</p>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Description</th>
                    <th>File Size</th>
                    <th>Status</th>
                    <th>Uploaded</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {templates.map(template => (
                    <tr key={template.id}>
                      <td>
                        <strong>{template.template_name}</strong>
                        {template.is_default && (
                          <span className="status-badge active">Default</span>
                        )}
                      </td>
                      <td>{template.template_type.replace('_', ' ')}</td>
                      <td>{template.description || 'No description'}</td>
                      <td>{template.file_size}</td>
                      <td>
                        <span className={`status-badge ${template.is_active ? 'active' : 'inactive'}`}>
                          {template.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td>{new Date(template.created_at).toLocaleDateString()}</td>
                      <td>
                        <div className="action-buttons">
                          <button
                            onClick={() => handleDownload(template)}
                            className="btn btn-small btn-primary"
                            title="Download template"
                          >
                            Download
                          </button>
                          {!template.is_default && (
                            <button
                              onClick={() => handleSetDefault(template.id)}
                              className="btn btn-small btn-secondary"
                              title="Set as default"
                            >
                              Set Default
                            </button>
                          )}
                          <button
                            onClick={() => handleDelete(template.id)}
                            className="btn btn-small btn-danger"
                            title="Delete template"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TemplateManagerPage;
