import { useState, useEffect } from 'react';
import FormModal from '../common/FormModal';

const VersionManagerPage = ({ onNavigate }) => {
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [actionLoading, setActionLoading] = useState(null);
  const [showDuplicateForm, setShowDuplicateForm] = useState(null);
  const [duplicateName, setDuplicateName] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createFormData, setCreateFormData] = useState({
    version_name: '',
    description: ''
  });

  useEffect(() => {
    loadVersions();
  }, []);

  const loadVersions = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('access_token');
      console.log('Loading versions with token:', token ? 'Token present' : 'No token');

      const response = await fetch('/api/versions/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('Versions API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Versions API error:', response.status, errorText);
        throw new Error(`Failed to load versions: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Loaded versions:', data);
      setVersions(data);
      setError('');
    } catch (err) {
      setError('Failed to load versions');
      console.error('Error loading versions:', err);
    } finally {
      setLoading(false);
    }
  };

  const publishVersion = async (versionId) => {
    setActionLoading(`publish_${versionId}`);
    setError('');

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`/api/versions/${versionId}/publish`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to publish version');
      }

      await loadVersions();
    } catch (err) {
      setError(err.message || 'Failed to publish version');
    } finally {
      setActionLoading(null);
    }
  };

  const duplicateVersion = async (versionId, newName) => {
    setActionLoading(`duplicate_${versionId}`);
    setError('');

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch(`/api/versions/${versionId}/duplicate?new_version_name=${encodeURIComponent(newName)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to duplicate version');
      }

      await loadVersions();
      setShowDuplicateForm(null);
      setDuplicateName('');
    } catch (err) {
      setError(err.message || 'Failed to duplicate version');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDuplicate = (version) => {
    setShowDuplicateForm(version.id);
    setDuplicateName(`${version.version_name} - Copy`);
  };

  const submitDuplicate = (versionId) => {
    if (!duplicateName.trim()) {
      setError('Please enter a name for the duplicated version');
      return;
    }
    duplicateVersion(versionId, duplicateName.trim());
  };

  const createNewVersion = async () => {
    setActionLoading('create_new');
    setError('');

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/versions/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(createFormData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to create version');
      }

      await loadVersions();
      setShowCreateForm(false);
      setCreateFormData({ version_name: '', description: '' });
    } catch (err) {
      setError(err.message || 'Failed to create version');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCreateFormChange = (e) => {
    const { name, value } = e.target;
    setCreateFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetCreateForm = () => {
    setShowCreateForm(false);
    setCreateFormData({ version_name: '', description: '' });
    setError('');
  };

  const handleCreateSubmit = (e) => {
    e.preventDefault();
    if (!createFormData.version_name.trim()) {
      setError('Please enter a version name');
      return;
    }
    createNewVersion();
  };

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Versions...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Version Management</h1>
        <p className="admin-page-description">
          Manage configuration versions, publish changes, and maintain different drafts of your 
          report card system. Use versioning to safely test changes before making them live.
        </p>
        
        <div className="admin-page-actions">
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn btn-primary"
          >
            ➕ Create New Version
          </button>

          <button
            onClick={loadVersions}
            className="btn btn-secondary"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Version Statistics */}
        <div className="admin-stats-grid" style={{ marginBottom: '32px' }}>
          <div className="admin-stat-card">
            <div className="stat-icon">📦</div>
            <div className="stat-value">{versions.length}</div>
            <div className="stat-label">Total Versions</div>
          </div>
          <div className="admin-stat-card">
            <div className="stat-icon">🚀</div>
            <div className="stat-value">{versions.filter(v => v.is_published).length}</div>
            <div className="stat-label">Published</div>
          </div>
          <div className="admin-stat-card">
            <div className="stat-icon">📝</div>
            <div className="stat-value">{versions.filter(v => !v.is_published).length}</div>
            <div className="stat-label">Drafts</div>
          </div>
          <div className="admin-stat-card">
            <div className="stat-icon">⭐</div>
            <div className="stat-value">{versions.filter(v => v.is_current).length}</div>
            <div className="stat-label">Current</div>
          </div>
        </div>

        {/* Versions Timeline */}
        <div style={{ marginBottom: '32px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '1.25rem', fontWeight: '600' }}>
            Version Timeline
          </h2>
          
          {versions.length === 0 ? (
            <div style={{ padding: '48px 24px', textAlign: 'center', color: '#6b7280' }}>
              <div style={{ fontSize: '3rem', marginBottom: '16px' }}>📦</div>
              <h3 style={{ margin: '0 0 8px 0', fontSize: '1.125rem' }}>No versions found</h3>
              <p style={{ margin: 0, fontSize: '0.875rem' }}>
                Versions will appear here as you make changes to your configuration.
              </p>
            </div>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {versions.map((version, index) => (
                <div 
                  key={version.id} 
                  style={{
                    background: '#fff',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '24px',
                    position: 'relative',
                    borderLeft: version.is_current ? '4px solid #f59e0b' : '4px solid #e5e7eb'
                  }}
                >
                  {/* Version Header */}
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                        <h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: '600' }}>
                          {version.version_name}
                        </h3>
                        
                        <div style={{ display: 'flex', gap: '8px' }}>
                          {version.is_current && (
                            <span style={{
                              background: '#f59e0b',
                              color: '#fff',
                              padding: '2px 8px',
                              borderRadius: '12px',
                              fontSize: '0.75rem',
                              fontWeight: '600',
                              textTransform: 'uppercase'
                            }}>
                              CURRENT
                            </span>
                          )}
                          
                          {version.is_published ? (
                            <span className="status-badge published">PUBLISHED</span>
                          ) : (
                            <span className="status-badge draft">DRAFT</span>
                          )}
                        </div>
                      </div>
                      
                      <p style={{ margin: '0 0 12px 0', color: '#6b7280', fontSize: '0.875rem' }}>
                        {version.description || 'No description provided'}
                      </p>
                      
                      <div style={{ display: 'flex', gap: '16px', fontSize: '0.75rem', color: '#9ca3af' }}>
                        <span>Created: {new Date(version.created_at).toLocaleDateString()}</span>
                        {version.published_at && (
                          <span>Published: {new Date(version.published_at).toLocaleDateString()}</span>
                        )}
                        <span>By: {version.creator_name || 'Unknown'}</span>
                      </div>
                    </div>
                    
                    {/* Version Actions */}
                    <div style={{ display: 'flex', gap: '8px', flexShrink: 0 }}>
                      {!version.is_published && (
                        <button
                          onClick={() => publishVersion(version.id)}
                          disabled={actionLoading === `publish_${version.id}`}
                          className="btn btn-primary"
                          style={{ fontSize: '0.875rem', padding: '6px 12px' }}
                        >
                          {actionLoading === `publish_${version.id}` ? 'Publishing...' : '🚀 Publish'}
                        </button>
                      )}
                      
                      <button
                        onClick={() => handleDuplicate(version)}
                        disabled={actionLoading === `duplicate_${version.id}`}
                        className="btn btn-secondary"
                        style={{ fontSize: '0.875rem', padding: '6px 12px' }}
                      >
                        {actionLoading === `duplicate_${version.id}` ? 'Duplicating...' : '📋 Duplicate'}
                      </button>
                    </div>
                  </div>
                  
                  {/* Duplicate Form */}
                  {showDuplicateForm === version.id && (
                    <div style={{
                      background: '#f9fafb',
                      border: '1px solid #e5e7eb',
                      borderRadius: '6px',
                      padding: '16px',
                      marginTop: '16px'
                    }}>
                      <h4 style={{ margin: '0 0 12px 0', fontSize: '1rem', fontWeight: '600' }}>
                        Duplicate Version
                      </h4>
                      <div style={{ display: 'flex', gap: '12px', alignItems: 'flex-end' }}>
                        <div style={{ flex: 1 }}>
                          <label style={{ display: 'block', marginBottom: '4px', fontSize: '0.875rem', fontWeight: '500' }}>
                            New Version Name:
                          </label>
                          <input
                            type="text"
                            value={duplicateName}
                            onChange={(e) => setDuplicateName(e.target.value)}
                            placeholder="Enter version name"
                            style={{
                              width: '100%',
                              padding: '8px 12px',
                              border: '1px solid #d1d5db',
                              borderRadius: '4px',
                              fontSize: '0.875rem'
                            }}
                          />
                        </div>
                        <button
                          onClick={() => submitDuplicate(version.id)}
                          className="btn btn-primary"
                          style={{ fontSize: '0.875rem', padding: '8px 16px' }}
                        >
                          Create
                        </button>
                        <button
                          onClick={() => setShowDuplicateForm(null)}
                          className="btn btn-secondary"
                          style={{ fontSize: '0.875rem', padding: '8px 16px' }}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* Timeline Connector */}
                  {index < versions.length - 1 && (
                    <div style={{
                      position: 'absolute',
                      left: '2px',
                      bottom: '-16px',
                      width: '2px',
                      height: '16px',
                      background: '#e5e7eb'
                    }} />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Version Management Info */}
        <div style={{ 
          marginTop: '32px', 
          padding: '24px', 
          background: '#f9fafb', 
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '1.125rem', fontWeight: '600' }}>
            About Version Management
          </h3>
          <div style={{ fontSize: '0.875rem', color: '#6b7280', lineHeight: '1.6' }}>
            <p style={{ margin: '0 0 12px 0' }}>
              <strong>Drafts:</strong> Work-in-progress versions that can be edited and modified. 
              Use drafts to test configuration changes before publishing.
            </p>
            <p style={{ margin: '0 0 12px 0' }}>
              <strong>Published:</strong> Stable versions that are available for use in report generation. 
              Published versions cannot be modified.
            </p>
            <p style={{ margin: 0 }}>
              <strong>Current:</strong> The active version being used for new reports. 
              Only one version can be current at a time.
            </p>
          </div>
        </div>

        {/* Create New Version Modal */}
        <FormModal
          isOpen={showCreateForm}
          onClose={resetCreateForm}
          title="Create New Version"
          onSubmit={handleCreateSubmit}
          submitLabel="Create Version"
          isSubmitting={actionLoading === 'create_new'}
          size="medium"
        >
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Version Name *</label>
              <input
                type="text"
                name="version_name"
                value={createFormData.version_name}
                onChange={handleCreateFormChange}
                required
                placeholder="e.g., 2024-2025 Academic Year Updates"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Description</label>
              <textarea
                name="description"
                value={createFormData.description}
                onChange={handleCreateFormChange}
                placeholder="Describe what changes this version includes..."
                className="form-input"
                rows="3"
              />
            </div>
          </div>

          <div style={{
            marginTop: '16px',
            padding: '12px',
            background: '#f0f9ff',
            borderRadius: '6px',
            border: '1px solid #bae6fd',
            fontSize: '0.875rem',
            color: '#0369a1'
          }}>
            💡 <strong>Tip:</strong> New versions are created as drafts. You can make changes and then publish when ready.
          </div>
        </FormModal>
      </div>
    </div>
  );
};

export default VersionManagerPage;
