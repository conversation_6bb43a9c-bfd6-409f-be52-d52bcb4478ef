import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';
import FormModal from '../common/FormModal';

const CategoryManagerPage = ({ onNavigate }) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingCategory, setEditingCategory] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [filterType, setFilterType] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentVersion, setCurrentVersion] = useState(null);
  const [hasUnpublishedChanges, setHasUnpublishedChanges] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    category_type: 'behavior',
    subject_id: '',
    description: '',
    sort_order: '',
    is_active: true
  });

  const [subjects, setSubjects] = useState([]);

  const categoryTypes = [
    { value: 'behavior', label: 'Behavior', icon: '👤', description: 'General behavior assessments' },
    { value: 'attitude', label: 'Attitude', icon: '😊', description: 'Student attitude and mindset' },
    { value: 'classroom', label: 'Classroom Behavior', icon: '🏫', description: 'Classroom-specific behaviors' },
    { value: 'social_emotional', label: 'Social Emotional', icon: '❤️', description: 'Social and emotional development' },
    { value: 'academic', label: 'Academic', icon: '🎓', description: 'Academic subject-specific categories' },
    { value: 'subject', label: 'Subject', icon: '📚', description: 'Subject-specific categories' },
    { value: 'concept', label: 'Concept', icon: '💡', description: 'Learning concepts and skills' },
    { value: 'writing', label: 'Writing', icon: '✍️', description: 'Writing skills and abilities' },
    { value: 'self_awareness', label: 'Self Awareness', icon: '🧠', description: 'Self-reflection and awareness' }
  ];

  useEffect(() => {
    loadCategories();
    loadSubjects();
    loadCurrentVersion();
  }, [filterType]);

  const loadCurrentVersion = async () => {
    try {
      const version = await dataService.getCurrentVersion();
      setCurrentVersion(version);
    } catch (err) {
      console.error('Error loading current version:', err);
    }
  };

  const loadSubjects = async () => {
    try {
      const data = await dataService.getSubjects();
      setSubjects(data);
    } catch (err) {
      console.error('Error loading subjects:', err);
    }
  };

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await dataService.getCategories(filterType || null);
      setCategories(data);
      setError('');
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error loading categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const categoryData = {
        ...formData,
        sort_order: parseInt(formData.sort_order) || 0
      };

      if (editingCategory) {
        await dataService.updateCategory(editingCategory.id, categoryData);
      } else {
        await dataService.createCategory(categoryData);
      }

      await loadCategories();
      resetForm();
      setError('');
      setHasUnpublishedChanges(true); // Mark that there are unpublished changes
    } catch (err) {
      setError(err.message || 'Failed to save category');
    }
  };

  const handleEdit = (category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      display_name: category.display_name,
      category_type: category.category_type,
      description: category.description || '',
      sort_order: category.sort_order.toString(),
      is_active: category.is_active
    });
    setShowAddForm(true);
  };

  const handleDelete = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    try {
      await dataService.deleteCategory(categoryId);
      await loadCategories();
      setError('');
    } catch (err) {
      setError(err.message || 'Failed to delete category');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      display_name: '',
      category_type: 'behavior',
      description: '',
      sort_order: '',
      is_active: true
    });
    setEditingCategory(null);
    setShowAddForm(false);
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const getCategoryTypeInfo = (type) => {
    return categoryTypes.find(t => t.value === type) || { label: type, icon: '📋', description: '' };
  };

  // Filter categories based on search
  const filteredCategories = categories.filter(category => {
    const matchesSearch = !searchTerm || 
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.display_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  // Group categories by type for better organization
  const groupedCategories = filteredCategories.reduce((groups, category) => {
    const type = category.category_type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(category);
    return groups;
  }, {});

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Categories...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Category Management</h1>
        <p className="admin-page-description">
          Organize assessment types and categories for different aspects of student evaluation.
          Categories help structure assessment points and provide clear organization for reports.
        </p>

        {currentVersion && (
          <div style={{
            marginTop: '12px',
            padding: '8px 12px',
            background: hasUnpublishedChanges ? '#fef3c7' : '#f0f9ff',
            border: `1px solid ${hasUnpublishedChanges ? '#f59e0b' : '#3b82f6'}`,
            borderRadius: '6px',
            fontSize: '0.875rem',
            color: hasUnpublishedChanges ? '#92400e' : '#1e40af'
          }}>
            📋 <strong>Current Version:</strong> {currentVersion.version_name}
            {hasUnpublishedChanges && (
              <span style={{ marginLeft: '8px', fontWeight: 'bold' }}>
                ⚠️ You have unpublished changes
              </span>
            )}
          </div>
        )}

        <div className="admin-page-actions">
          <div className="admin-page-filters">
            <div className="filter-group">
              <label>Search:</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search categories..."
                className="filter-input"
              />
            </div>
            
            <div className="filter-group">
              <label>Type:</label>
              <select 
                value={filterType} 
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="">All Types</option>
                {categoryTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="btn btn-primary"
          >
            ➕ Add Category
          </button>

          {hasUnpublishedChanges && (
            <button
              onClick={() => onNavigate('version-management')}
              className="btn btn-warning"
              style={{
                background: '#f59e0b',
                color: 'white',
                animation: 'pulse 2s infinite'
              }}
            >
              📦 Publish Changes
            </button>
          )}

          <button
            onClick={loadCategories}
            className="btn btn-secondary"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Add/Edit Modal */}
        <FormModal
          isOpen={showAddForm}
          onClose={resetForm}
          title={editingCategory ? 'Edit Category' : 'Add Category'}
          onSubmit={handleSubmit}
          submitLabel={editingCategory ? 'Update' : 'Create'}
          size="medium"
        >
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Category Name *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="e.g., communication_skills"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Display Name *</label>
              <input
                type="text"
                name="display_name"
                value={formData.display_name}
                onChange={handleInputChange}
                required
                placeholder="e.g., Communication Skills"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Category Type *</label>
              <select
                name="category_type"
                value={formData.category_type}
                onChange={handleInputChange}
                required
                className="form-select"
              >
                <option value="">Select type</option>
                {categoryTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {formData.category_type === 'academic' && (
            <div className="form-group">
              <label className="form-label">Subject *</label>
              <select
                name="subject_id"
                value={formData.subject_id}
                onChange={handleInputChange}
                required
                className="form-select"
              >
                <option value="">Select subject</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.display_name || subject.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="form-row">
            <div className="form-group">
              <label className="form-label">Sort Order</label>
              <input
                type="number"
                name="sort_order"
                value={formData.sort_order}
                onChange={handleInputChange}
                min="0"
                placeholder="Display order"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <div className="form-checkbox-group">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="form-checkbox"
                  id="is_active_category"
                />
                <label htmlFor="is_active_category" className="form-label">Active</label>
              </div>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Description</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows="3"
              placeholder="Optional description for this category"
              className="form-textarea"
            />
          </div>
        </FormModal>

        {/* Categories Display */}
        {Object.keys(groupedCategories).length > 0 ? (
          Object.entries(groupedCategories).map(([type, typeCategories]) => {
            const typeInfo = getCategoryTypeInfo(type);
            return (
              <div key={type} style={{ marginBottom: '32px' }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '12px', 
                  marginBottom: '16px',
                  padding: '12px 16px',
                  background: '#f9fafb',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb'
                }}>
                  <span style={{ fontSize: '1.5rem' }}>{typeInfo.icon}</span>
                  <div>
                    <h3 style={{ margin: 0, fontSize: '1.125rem', fontWeight: '600' }}>
                      {typeInfo.label} ({typeCategories.length})
                    </h3>
                    <p style={{ margin: 0, fontSize: '0.875rem', color: '#6b7280' }}>
                      {typeInfo.description}
                    </p>
                  </div>
                </div>
                
                <div className="card">
                  <table className="data-table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Display Name</th>
                        <th>Sort Order</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {typeCategories.map(category => (
                        <tr
                          key={category.id}
                          className="clickable-row"
                          onClick={() => handleEdit(category)}
                          style={{ cursor: 'pointer' }}
                        >
                          <td style={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                            {category.name}
                          </td>
                          <td style={{ fontWeight: '500' }}>{category.display_name}</td>
                          <td>{category.sort_order}</td>
                          <td>
                            <span className={`status-badge ${category.is_active ? 'active' : 'inactive'}`}>
                              {category.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td onClick={(e) => e.stopPropagation()}>
                            <div style={{ display: 'flex', gap: '8px' }}>
                              <button
                                onClick={() => handleEdit(category)}
                                className="btn btn-outline"
                                style={{ padding: '4px 8px', fontSize: '0.75rem' }}
                                title="Edit category"
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => handleDelete(category.id)}
                                className="btn btn-danger"
                                style={{ padding: '4px 8px', fontSize: '0.75rem' }}
                                title="Delete category"
                              >
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            );
          })
        ) : (
          <div style={{ padding: '48px 24px', textAlign: 'center', color: '#6b7280' }}>
            <div style={{ fontSize: '3rem', marginBottom: '16px' }}>📋</div>
            <h3 style={{ margin: '0 0 8px 0', fontSize: '1.125rem' }}>
              {categories.length === 0 ? 'No categories configured' : 'No categories match your filters'}
            </h3>
            <p style={{ margin: 0, fontSize: '0.875rem' }}>
              {categories.length === 0 
                ? 'Start by adding categories to organize your assessment points.'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryManagerPage;
