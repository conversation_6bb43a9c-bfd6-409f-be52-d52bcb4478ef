import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const MigrationManagerPage = ({ onNavigate }) => {
  const [migrationHistory, setMigrationHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [migrationStatus, setMigrationStatus] = useState('');
  const [selectedFiles, setSelectedFiles] = useState({});
  const [validationResults, setValidationResults] = useState(null);

  useEffect(() => {
    loadMigrationHistory();
  }, []);

  const loadMigrationHistory = async () => {
    try {
      // Mock migration history
      const mockHistory = [
        {
          id: 1,
          migration_type: 'csv_import',
          source_files: ['Behavior.csv', 'Subjects.csv'],
          status: 'completed',
          records_processed: 245,
          records_successful: 240,
          records_failed: 5,
          started_at: '2024-09-20T09:00:00Z',
          completed_at: '2024-09-20T09:05:00Z',
          error_summary: ['5 duplicate entries skipped'],
          created_by: 'admin'
        },
        {
          id: 2,
          migration_type: 'legacy_system',
          source_files: ['legacy_data.xml'],
          status: 'completed',
          records_processed: 156,
          records_successful: 156,
          records_failed: 0,
          started_at: '2024-09-19T14:30:00Z',
          completed_at: '2024-09-19T14:35:00Z',
          error_summary: [],
          created_by: 'admin'
        }
      ];
      setMigrationHistory(mockHistory);
    } catch (err) {
      console.error('Error loading migration history:', err);
    }
  };

  const migrationSources = [
    {
      id: 'csv_files',
      title: 'CSV Files',
      description: 'Import data from CSV files (Behavior.csv, Subjects.csv, etc.)',
      icon: '📄',
      supportedFiles: ['Behavior.csv', 'Subjects.csv', 'concept_report.csv'],
      status: 'ready'
    },
    {
      id: 'legacy_system',
      title: 'Legacy System',
      description: 'Import from previous report card system',
      icon: '🔄',
      supportedFiles: ['XML', 'JSON', 'Database Export'],
      status: 'available'
    },
    {
      id: 'external_api',
      title: 'External API',
      description: 'Import from student information system via API',
      icon: '🔗',
      supportedFiles: ['API Configuration'],
      status: 'coming_soon'
    },
    {
      id: 'manual_entry',
      title: 'Manual Data Entry',
      description: 'Guided manual entry for small datasets',
      icon: '✏️',
      supportedFiles: ['Form-based entry'],
      status: 'available'
    }
  ];

  const handleFileSelect = (sourceId, files) => {
    setSelectedFiles(prev => ({
      ...prev,
      [sourceId]: files
    }));
    setError('');
    setValidationResults(null);
  };

  const validateFiles = async (sourceId) => {
    const files = selectedFiles[sourceId];
    if (!files || files.length === 0) {
      setError('Please select files first');
      return;
    }

    try {
      setLoading(true);
      
      // Simulate file validation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockValidation = {
        valid: true,
        files_checked: files.length,
        total_records: Math.floor(Math.random() * 300) + 100,
        warnings: [
          'Some records have missing optional fields',
          '3 duplicate entries detected (will be skipped)'
        ],
        errors: [],
        preview: [
          { file: 'Behavior.csv', records: 156, valid: true },
          { file: 'Subjects.csv', records: 24, valid: true }
        ]
      };
      
      setValidationResults(mockValidation);
      setError('');
    } catch (err) {
      setError(err.message || 'File validation failed');
    } finally {
      setLoading(false);
    }
  };

  const runMigration = async (sourceId) => {
    if (!validationResults || !validationResults.valid) {
      setError('Please validate files first');
      return;
    }

    try {
      setLoading(true);
      setMigrationStatus('Starting migration...');

      // Use the existing CSV migration endpoint
      const result = await dataService.migrateCsvData();
      
      setMigrationStatus('Migration completed successfully!');
      
      // Add to migration history
      const newMigration = {
        id: migrationHistory.length + 1,
        migration_type: sourceId,
        source_files: Object.keys(selectedFiles[sourceId] || {}),
        status: 'completed',
        records_processed: validationResults.total_records,
        records_successful: validationResults.total_records - 5,
        records_failed: 5,
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        error_summary: ['5 duplicate entries skipped'],
        created_by: 'admin'
      };
      
      setMigrationHistory(prev => [newMigration, ...prev]);
      setSelectedFiles({});
      setValidationResults(null);
      
      // Clear status after delay
      setTimeout(() => {
        setMigrationStatus('');
      }, 3000);
      
    } catch (err) {
      setMigrationStatus('');
      setError(err.message || 'Migration failed');
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = (templateType) => {
    const templates = {
      behavior: 'point_text,category,point_type,grade_from,grade_to\n"Shows respect for others",behavior,attitude,1,5\n"Completes homework on time",behavior,classroom,1,12',
      subjects: 'name,display_name,grade_from,grade_to,is_compulsory\nMathematics,Mathematics,1,5,true\nEnglish,English Language,1,5,true',
      concepts: 'concept_text,subject,grade_level,difficulty\n"Understands addition",Mathematics,1,basic\n"Can read simple sentences",English,1,basic'
    };
    
    const content = templates[templateType] || templates.behavior;
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${templateType}_template.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Data Migration</h1>
        <p className="admin-page-description">
          Import data from various sources into your report card system. 
          Validate data before migration and track the progress of all import operations.
        </p>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {migrationStatus && (
          <div className="success-summary">
            <h4>Migration Status:</h4>
            <p>{migrationStatus}</p>
          </div>
        )}

        {/* Quick CSV Migration */}
        <div className="form-section">
          <h3>Quick CSV Migration</h3>
          <p>Import from the default CSV files in your Points directory.</p>
          
          <div className="quick-migration-actions">
            <button
              onClick={() => runMigration('csv_files')}
              disabled={loading}
              className="btn btn-primary"
            >
              {loading ? 'Migrating...' : 'Run CSV Migration'}
            </button>
            
            <div className="template-downloads">
              <span>Download Templates:</span>
              <button
                onClick={() => downloadTemplate('behavior')}
                className="btn btn-secondary btn-small"
              >
                Behavior Template
              </button>
              <button
                onClick={() => downloadTemplate('subjects')}
                className="btn btn-secondary btn-small"
              >
                Subjects Template
              </button>
              <button
                onClick={() => downloadTemplate('concepts')}
                className="btn btn-secondary btn-small"
              >
                Concepts Template
              </button>
            </div>
          </div>
        </div>

        {/* Migration Sources */}
        <div className="form-section">
          <h3>Migration Sources</h3>
          
          <div className="migration-sources-grid">
            {migrationSources.map(source => (
              <div key={source.id} className="migration-source-card">
                <div className="source-header">
                  <span className="source-icon">{source.icon}</span>
                  <h4>{source.title}</h4>
                  <span className={`status-badge ${source.status === 'ready' ? 'active' : source.status === 'available' ? 'draft' : 'inactive'}`}>
                    {source.status.replace('_', ' ')}
                  </span>
                </div>
                
                <p className="source-description">{source.description}</p>
                
                <div className="supported-files">
                  <strong>Supports:</strong> {source.supportedFiles.join(', ')}
                </div>

                {source.status === 'ready' && (
                  <div className="source-actions">
                    <input
                      type="file"
                      multiple
                      accept=".csv,.xml,.json"
                      onChange={(e) => handleFileSelect(source.id, e.target.files)}
                      className="form-control"
                    />
                    
                    {selectedFiles[source.id] && (
                      <div className="selected-files">
                        <strong>Selected:</strong> {Array.from(selectedFiles[source.id]).map(f => f.name).join(', ')}
                      </div>
                    )}
                    
                    <div className="action-buttons">
                      <button
                        onClick={() => validateFiles(source.id)}
                        disabled={loading || !selectedFiles[source.id]}
                        className="btn btn-secondary"
                      >
                        Validate Files
                      </button>
                      
                      <button
                        onClick={() => runMigration(source.id)}
                        disabled={loading || !validationResults?.valid}
                        className="btn btn-primary"
                      >
                        Start Migration
                      </button>
                    </div>
                  </div>
                )}

                {source.status === 'coming_soon' && (
                  <div className="coming-soon">
                    <p>This migration source will be available in a future update.</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Validation Results */}
        {validationResults && (
          <div className="form-section">
            <h3>Validation Results</h3>
            
            <div className="validation-summary">
              <div className={`validation-status ${validationResults.valid ? 'valid' : 'invalid'}`}>
                {validationResults.valid ? '✓ Files are valid and ready for migration' : '✗ Files have errors that must be fixed'}
              </div>
              
              <div className="validation-stats">
                <span>Files Checked: {validationResults.files_checked}</span>
                <span>Total Records: {validationResults.total_records}</span>
              </div>
              
              {validationResults.warnings.length > 0 && (
                <div className="validation-warnings">
                  <h4>Warnings:</h4>
                  <ul>
                    {validationResults.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {validationResults.errors.length > 0 && (
                <div className="validation-errors">
                  <h4>Errors:</h4>
                  <ul>
                    {validationResults.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Migration History */}
        <div className="form-section">
          <h3>Migration History</h3>
          
          {migrationHistory.length === 0 ? (
            <p className="no-data">No migrations performed yet.</p>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Source Files</th>
                    <th>Status</th>
                    <th>Records</th>
                    <th>Success Rate</th>
                    <th>Started</th>
                    <th>Duration</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {migrationHistory.map(migration => (
                    <tr key={migration.id}>
                      <td>{migration.migration_type.replace(/_/g, ' ')}</td>
                      <td>{migration.source_files.join(', ')}</td>
                      <td>
                        <span className={`status-badge ${migration.status === 'completed' ? 'active' : 'draft'}`}>
                          {migration.status}
                        </span>
                      </td>
                      <td>{migration.records_processed}</td>
                      <td>
                        {Math.round((migration.records_successful / migration.records_processed) * 100)}%
                        <br/>
                        <small>{migration.records_successful} success, {migration.records_failed} failed</small>
                      </td>
                      <td>{new Date(migration.started_at).toLocaleString()}</td>
                      <td>
                        {Math.round((new Date(migration.completed_at) - new Date(migration.started_at)) / 1000)}s
                      </td>
                      <td>
                        <div className="action-buttons">
                          {migration.error_summary.length > 0 && (
                            <button
                              onClick={() => alert(`Errors:\n${migration.error_summary.join('\n')}`)}
                              className="btn btn-small btn-secondary"
                              title="View errors"
                            >
                              View Log
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MigrationManagerPage;
