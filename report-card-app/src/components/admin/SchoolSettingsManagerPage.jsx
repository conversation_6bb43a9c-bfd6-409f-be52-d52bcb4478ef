import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const SchoolSettingsManagerPage = ({ onNavigate }) => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    school_name: '',
    school_address: '',
    school_logo_url: '',
    academic_year: '',
    assessment_periods: ['Term 1', 'Term 2', 'Term 3'],
    grading_scale: 'A-F',
    report_card_format: 'standard'
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      // For now, we'll use mock data since the backend endpoint might not exist yet
      const mockSettings = {
        school_name: 'Sample Elementary School',
        school_address: '123 Education Street, Learning City, LC 12345',
        school_logo_url: '',
        academic_year: '2024-2025',
        assessment_periods: ['Term 1', 'Term 2', 'Term 3'],
        grading_scale: 'A-F',
        report_card_format: 'standard'
      };
      setSettings(mockSettings);
      setFormData(mockSettings);
      setError('');
    } catch (err) {
      setError('Failed to load school settings');
      console.error('Error loading settings:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      // TODO: Implement actual API call when backend endpoint is ready
      // await dataService.updateSchoolSettings(formData);
      
      // For now, just simulate saving
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSettings(formData);
      setError('');
      
      // Show success message
      alert('School settings saved successfully!');
    } catch (err) {
      setError(err.message || 'Failed to save school settings');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAssessmentPeriodsChange = (index, value) => {
    const newPeriods = [...formData.assessment_periods];
    newPeriods[index] = value;
    setFormData(prev => ({
      ...prev,
      assessment_periods: newPeriods
    }));
  };

  const addAssessmentPeriod = () => {
    setFormData(prev => ({
      ...prev,
      assessment_periods: [...prev.assessment_periods, `Term ${prev.assessment_periods.length + 1}`]
    }));
  };

  const removeAssessmentPeriod = (index) => {
    if (formData.assessment_periods.length > 1) {
      const newPeriods = formData.assessment_periods.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        assessment_periods: newPeriods
      }));
    }
  };

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-header">
          <h1 className="admin-page-title">School Settings</h1>
        </div>
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading school settings...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">School Settings</h1>
        <p className="admin-page-description">
          Configure your school's basic information, academic year settings, and report card preferences.
          These settings will be used across all report cards and system communications.
        </p>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        <form onSubmit={handleSubmit} className="admin-form">
          <div className="form-section">
            <h3>Basic Information</h3>
            
            <div className="form-group">
              <label htmlFor="school_name">School Name *</label>
              <input
                type="text"
                id="school_name"
                name="school_name"
                value={formData.school_name}
                onChange={handleInputChange}
                required
                className="form-control"
                placeholder="Enter your school's full name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="school_address">School Address</label>
              <textarea
                id="school_address"
                name="school_address"
                value={formData.school_address}
                onChange={handleInputChange}
                className="form-control"
                rows="3"
                placeholder="Enter your school's complete address"
              />
            </div>

            <div className="form-group">
              <label htmlFor="school_logo_url">School Logo URL</label>
              <input
                type="url"
                id="school_logo_url"
                name="school_logo_url"
                value={formData.school_logo_url}
                onChange={handleInputChange}
                className="form-control"
                placeholder="https://example.com/logo.png"
              />
              <small className="form-help">
                Provide a URL to your school logo. This will appear on report cards and official documents.
              </small>
            </div>
          </div>

          <div className="form-section">
            <h3>Academic Configuration</h3>
            
            <div className="form-group">
              <label htmlFor="academic_year">Academic Year *</label>
              <input
                type="text"
                id="academic_year"
                name="academic_year"
                value={formData.academic_year}
                onChange={handleInputChange}
                required
                className="form-control"
                placeholder="e.g., 2024-2025"
              />
            </div>

            <div className="form-group">
              <label>Assessment Periods</label>
              <div className="assessment-periods">
                {formData.assessment_periods.map((period, index) => (
                  <div key={index} className="period-input-group">
                    <input
                      type="text"
                      value={period}
                      onChange={(e) => handleAssessmentPeriodsChange(index, e.target.value)}
                      className="form-control"
                      placeholder={`Period ${index + 1}`}
                    />
                    {formData.assessment_periods.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeAssessmentPeriod(index)}
                        className="btn btn-danger btn-small"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addAssessmentPeriod}
                  className="btn btn-secondary btn-small"
                >
                  Add Assessment Period
                </button>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="grading_scale">Grading Scale</label>
              <select
                id="grading_scale"
                name="grading_scale"
                value={formData.grading_scale}
                onChange={handleInputChange}
                className="form-control"
              >
                <option value="A-F">A, B, C, D, F</option>
                <option value="1-4">1, 2, 3, 4</option>
                <option value="percentage">Percentage (0-100%)</option>
                <option value="custom">Custom Scale</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="report_card_format">Report Card Format</label>
              <select
                id="report_card_format"
                name="report_card_format"
                value={formData.report_card_format}
                onChange={handleInputChange}
                className="form-control"
              >
                <option value="standard">Standard Format</option>
                <option value="detailed">Detailed Format</option>
                <option value="summary">Summary Format</option>
                <option value="narrative">Narrative Format</option>
              </select>
            </div>
          </div>

          <div className="form-actions">
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
            
            <button
              type="button"
              onClick={loadSettings}
              className="btn btn-secondary"
            >
              Reset Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SchoolSettingsManagerPage;
