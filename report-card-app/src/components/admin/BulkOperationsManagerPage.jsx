import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const BulkOperationsManagerPage = ({ onNavigate }) => {
  const [activeOperation, setActiveOperation] = useState(null);
  const [operationHistory, setOperationHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [operationProgress, setOperationProgress] = useState(null);

  useEffect(() => {
    loadOperationHistory();
  }, []);

  const loadOperationHistory = async () => {
    try {
      // Mock operation history
      const mockHistory = [
        {
          id: 1,
          operation_type: 'bulk_import_students',
          status: 'completed',
          records_processed: 150,
          records_successful: 148,
          records_failed: 2,
          started_at: '2024-09-20T10:00:00Z',
          completed_at: '2024-09-20T10:05:00Z',
          error_details: ['Row 15: Invalid grade level', 'Row 87: Missing required field']
        },
        {
          id: 2,
          operation_type: 'bulk_update_assessments',
          status: 'completed',
          records_processed: 75,
          records_successful: 75,
          records_failed: 0,
          started_at: '2024-09-19T14:30:00Z',
          completed_at: '2024-09-19T14:32:00Z',
          error_details: []
        }
      ];
      setOperationHistory(mockHistory);
    } catch (err) {
      console.error('Error loading operation history:', err);
    }
  };

  const bulkOperations = [
    {
      id: 'import_students',
      title: 'Import Students',
      description: 'Bulk import student information from CSV file',
      icon: '👥',
      fileFormat: 'CSV',
      requiredColumns: ['student_id', 'first_name', 'last_name', 'grade_level', 'class'],
      sampleData: 'student_id,first_name,last_name,grade_level,class\n001,John,Doe,5,5A\n002,Jane,Smith,5,5B'
    },
    {
      id: 'import_assessments',
      title: 'Import Assessment Points',
      description: 'Bulk import assessment criteria from CSV file',
      icon: '✓',
      fileFormat: 'CSV',
      requiredColumns: ['point_text', 'category', 'point_type', 'grade_from', 'grade_to'],
      sampleData: 'point_text,category,point_type,grade_from,grade_to\n"Shows respect for others",behavior,attitude,1,5\n"Completes homework on time",behavior,classroom,1,12'
    },
    {
      id: 'update_grades',
      title: 'Update Grade Assignments',
      description: 'Bulk update student grade assignments',
      icon: '🎓',
      fileFormat: 'CSV',
      requiredColumns: ['student_id', 'subject', 'assessment_point', 'score'],
      sampleData: 'student_id,subject,assessment_point,score\n001,Mathematics,"Understands addition",4\n001,English,"Reads fluently",3'
    },
    {
      id: 'export_reports',
      title: 'Generate Bulk Reports',
      description: 'Generate report cards for multiple students',
      icon: '📄',
      fileFormat: 'Selection',
      requiredColumns: ['Select students or classes'],
      sampleData: 'Select students from the list below'
    }
  ];

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      setError('');
    }
  };

  const handleOperationStart = async (operationType) => {
    if (!selectedFile && operationType !== 'export_reports') {
      setError('Please select a file first');
      return;
    }

    try {
      setLoading(true);
      setActiveOperation(operationType);
      setOperationProgress({ current: 0, total: 100, status: 'Processing...' });

      // Simulate bulk operation progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setOperationProgress({ 
          current: i, 
          total: 100, 
          status: i === 100 ? 'Completed' : `Processing... ${i}%` 
        });
      }

      // Add to operation history
      const newOperation = {
        id: operationHistory.length + 1,
        operation_type: operationType,
        status: 'completed',
        records_processed: Math.floor(Math.random() * 200) + 50,
        records_successful: Math.floor(Math.random() * 190) + 45,
        records_failed: Math.floor(Math.random() * 5),
        started_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        error_details: []
      };

      setOperationHistory(prev => [newOperation, ...prev]);
      setSelectedFile(null);
      document.getElementById('bulk-file').value = '';
      setError('');
      
      alert('Bulk operation completed successfully!');
    } catch (err) {
      setError(err.message || 'Bulk operation failed');
    } finally {
      setLoading(false);
      setActiveOperation(null);
      setOperationProgress(null);
    }
  };

  const downloadSampleFile = (operation) => {
    const blob = new Blob([operation.sampleData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${operation.id}_sample.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'active';
      case 'failed': return 'inactive';
      case 'processing': return 'draft';
      default: return 'draft';
    }
  };

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Bulk Operations</h1>
        <p className="admin-page-description">
          Perform bulk operations to efficiently manage large amounts of data. 
          Import students, assessment points, or generate multiple reports at once.
        </p>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Active Operation Progress */}
        {operationProgress && (
          <div className="operation-progress">
            <h3>Operation in Progress</h3>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${operationProgress.current}%` }}
              ></div>
            </div>
            <p>{operationProgress.status}</p>
          </div>
        )}

        {/* Bulk Operations Grid */}
        <div className="bulk-operations-grid">
          {bulkOperations.map(operation => (
            <div key={operation.id} className="bulk-operation-card">
              <div className="operation-header">
                <span className="operation-icon">{operation.icon}</span>
                <h3>{operation.title}</h3>
              </div>
              
              <p className="operation-description">{operation.description}</p>
              
              <div className="operation-details">
                <strong>Format:</strong> {operation.fileFormat}<br/>
                <strong>Required Columns:</strong> {operation.requiredColumns.join(', ')}
              </div>

              <div className="operation-actions">
                <button
                  onClick={() => downloadSampleFile(operation)}
                  className="btn btn-secondary btn-small"
                >
                  Download Sample
                </button>
                
                {operation.id !== 'export_reports' && (
                  <div className="file-upload-section">
                    <input
                      type="file"
                      id="bulk-file"
                      accept=".csv"
                      onChange={handleFileSelect}
                      className="form-control"
                    />
                  </div>
                )}
                
                <button
                  onClick={() => handleOperationStart(operation.id)}
                  disabled={loading || (!selectedFile && operation.id !== 'export_reports')}
                  className="btn btn-primary"
                >
                  {loading && activeOperation === operation.id ? 'Processing...' : 'Start Operation'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Operation History */}
        <div className="form-section">
          <h3>Operation History</h3>
          
          {operationHistory.length === 0 ? (
            <p className="no-data">No operations performed yet.</p>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Operation</th>
                    <th>Status</th>
                    <th>Records</th>
                    <th>Success Rate</th>
                    <th>Started</th>
                    <th>Duration</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {operationHistory.map(operation => (
                    <tr key={operation.id}>
                      <td>{operation.operation_type.replace(/_/g, ' ')}</td>
                      <td>
                        <span className={`status-badge ${getStatusColor(operation.status)}`}>
                          {operation.status}
                        </span>
                      </td>
                      <td>{operation.records_processed}</td>
                      <td>
                        {operation.records_processed > 0 
                          ? `${Math.round((operation.records_successful / operation.records_processed) * 100)}%`
                          : 'N/A'
                        }
                        <br/>
                        <small>
                          {operation.records_successful} success, {operation.records_failed} failed
                        </small>
                      </td>
                      <td>{new Date(operation.started_at).toLocaleString()}</td>
                      <td>
                        {operation.completed_at 
                          ? `${Math.round((new Date(operation.completed_at) - new Date(operation.started_at)) / 1000)}s`
                          : 'In progress'
                        }
                      </td>
                      <td>
                        <div className="action-buttons">
                          {operation.error_details.length > 0 && (
                            <button
                              onClick={() => alert(`Errors:\n${operation.error_details.join('\n')}`)}
                              className="btn btn-small btn-secondary"
                              title="View errors"
                            >
                              View Errors
                            </button>
                          )}
                          <button
                            onClick={() => alert('Download detailed report functionality coming soon')}
                            className="btn btn-small btn-secondary"
                            title="Download report"
                          >
                            Download Report
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkOperationsManagerPage;
