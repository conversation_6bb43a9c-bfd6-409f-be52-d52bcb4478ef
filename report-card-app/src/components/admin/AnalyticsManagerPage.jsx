import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';

const AnalyticsManagerPage = ({ onNavigate }) => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedTimeRange, setSelectedTimeRange] = useState('30_days');
  const [selectedMetric, setSelectedMetric] = useState('usage');

  useEffect(() => {
    loadAnalytics();
  }, [selectedTimeRange, selectedMetric]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // Mock analytics data
      const mockAnalytics = {
        system_usage: {
          total_users: 45,
          active_users_today: 12,
          active_users_week: 28,
          total_report_cards: 1250,
          reports_generated_today: 15,
          reports_generated_week: 89
        },
        assessment_metrics: {
          total_assessment_points: 156,
          most_used_points: [
            { point_text: "Shows respect for others", usage_count: 245 },
            { point_text: "Completes homework on time", usage_count: 198 },
            { point_text: "Participates in class discussions", usage_count: 167 }
          ],
          category_distribution: [
            { category: "Behavior", count: 45, percentage: 28.8 },
            { category: "Academic Skills", count: 38, percentage: 24.4 },
            { category: "Social Skills", count: 32, percentage: 20.5 },
            { category: "Work Habits", count: 25, percentage: 16.0 },
            { category: "Other", count: 16, percentage: 10.3 }
          ]
        },
        grade_distribution: {
          grade_levels: [
            { grade: "Kindergarten", student_count: 45, report_count: 135 },
            { grade: "Grade 1", student_count: 52, report_count: 156 },
            { grade: "Grade 2", student_count: 48, report_count: 144 },
            { grade: "Grade 3", student_count: 51, report_count: 153 },
            { grade: "Grade 4", student_count: 49, report_count: 147 },
            { grade: "Grade 5", student_count: 46, report_count: 138 }
          ]
        },
        performance_metrics: {
          average_report_generation_time: "2.3 seconds",
          system_uptime: "99.8%",
          error_rate: "0.2%",
          peak_usage_hours: ["9:00 AM", "1:00 PM", "3:00 PM"]
        },
        recent_activity: [
          { timestamp: "2024-09-21T10:30:00Z", action: "Report Generated", user: "teacher1", details: "Grade 3 Progress Report" },
          { timestamp: "2024-09-21T10:25:00Z", action: "Assessment Point Added", user: "admin", details: "New behavior criteria" },
          { timestamp: "2024-09-21T10:20:00Z", action: "User Login", user: "teacher2", details: "Dashboard access" },
          { timestamp: "2024-09-21T10:15:00Z", action: "Data Export", user: "admin", details: "Assessment points CSV" },
          { timestamp: "2024-09-21T10:10:00Z", action: "Report Generated", user: "teacher3", details: "Grade 1 Term Report" }
        ]
      };
      
      setAnalytics(mockAnalytics);
      setError('');
    } catch (err) {
      setError('Failed to load analytics data');
      console.error('Error loading analytics:', err);
    } finally {
      setLoading(false);
    }
  };

  const timeRanges = [
    { value: '7_days', label: 'Last 7 Days' },
    { value: '30_days', label: 'Last 30 Days' },
    { value: '90_days', label: 'Last 3 Months' },
    { value: '1_year', label: 'Last Year' }
  ];

  const metricTypes = [
    { value: 'usage', label: 'System Usage' },
    { value: 'performance', label: 'Performance' },
    { value: 'content', label: 'Content Analytics' },
    { value: 'users', label: 'User Activity' }
  ];

  const exportAnalytics = () => {
    const csvData = [
      ['Metric', 'Value', 'Time Range'],
      ['Total Users', analytics.system_usage.total_users, selectedTimeRange],
      ['Active Users Today', analytics.system_usage.active_users_today, selectedTimeRange],
      ['Total Report Cards', analytics.system_usage.total_report_cards, selectedTimeRange],
      ['Reports Generated Today', analytics.system_usage.reports_generated_today, selectedTimeRange]
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics_${selectedTimeRange}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-header">
          <h1 className="admin-page-title">System Analytics</h1>
        </div>
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading analytics data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">System Analytics</h1>
        <p className="admin-page-description">
          Monitor system usage, performance metrics, and user activity. 
          Track how your report card system is being used and identify areas for improvement.
        </p>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Analytics Controls */}
        <div className="analytics-controls">
          <div className="control-group">
            <label htmlFor="time-range">Time Range:</label>
            <select
              id="time-range"
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="form-control"
            >
              {timeRanges.map(range => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
          </div>

          <div className="control-group">
            <label htmlFor="metric-type">Focus:</label>
            <select
              id="metric-type"
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="form-control"
            >
              {metricTypes.map(metric => (
                <option key={metric.value} value={metric.value}>
                  {metric.label}
                </option>
              ))}
            </select>
          </div>

          <button
            onClick={exportAnalytics}
            className="btn btn-secondary"
          >
            Export Data
          </button>
        </div>

        {/* Key Metrics Dashboard */}
        <div className="metrics-grid">
          <div className="metric-card">
            <div className="metric-icon">👥</div>
            <div className="metric-content">
              <h3>{analytics.system_usage.total_users}</h3>
              <p>Total Users</p>
              <small>{analytics.system_usage.active_users_today} active today</small>
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">📄</div>
            <div className="metric-content">
              <h3>{analytics.system_usage.total_report_cards}</h3>
              <p>Report Cards Generated</p>
              <small>{analytics.system_usage.reports_generated_today} today</small>
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">✓</div>
            <div className="metric-content">
              <h3>{analytics.assessment_metrics.total_assessment_points}</h3>
              <p>Assessment Points</p>
              <small>Available for use</small>
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-icon">⚡</div>
            <div className="metric-content">
              <h3>{analytics.performance_metrics.average_report_generation_time}</h3>
              <p>Avg. Generation Time</p>
              <small>{analytics.performance_metrics.system_uptime} uptime</small>
            </div>
          </div>
        </div>

        {/* Detailed Analytics Sections */}
        <div className="analytics-sections">
          {/* Most Used Assessment Points */}
          <div className="analytics-section">
            <h3>Most Used Assessment Points</h3>
            <div className="usage-list">
              {analytics.assessment_metrics.most_used_points.map((point, index) => (
                <div key={index} className="usage-item">
                  <span className="usage-rank">#{index + 1}</span>
                  <span className="usage-text">{point.point_text}</span>
                  <span className="usage-count">{point.usage_count} uses</span>
                </div>
              ))}
            </div>
          </div>

          {/* Category Distribution */}
          <div className="analytics-section">
            <h3>Assessment Category Distribution</h3>
            <div className="category-chart">
              {analytics.assessment_metrics.category_distribution.map((category, index) => (
                <div key={index} className="category-bar">
                  <div className="category-label">
                    <span>{category.category}</span>
                    <span>{category.count} points ({category.percentage}%)</span>
                  </div>
                  <div className="category-progress">
                    <div 
                      className="category-fill" 
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Grade Level Statistics */}
          <div className="analytics-section">
            <h3>Grade Level Statistics</h3>
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>Grade Level</th>
                    <th>Students</th>
                    <th>Reports Generated</th>
                    <th>Avg. Reports per Student</th>
                  </tr>
                </thead>
                <tbody>
                  {analytics.grade_distribution.grade_levels.map((grade, index) => (
                    <tr key={index}>
                      <td>{grade.grade}</td>
                      <td>{grade.student_count}</td>
                      <td>{grade.report_count}</td>
                      <td>{(grade.report_count / grade.student_count).toFixed(1)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="analytics-section">
            <h3>Recent System Activity</h3>
            <div className="activity-list">
              {analytics.recent_activity.map((activity, index) => (
                <div key={index} className="activity-item">
                  <div className="activity-time">
                    {new Date(activity.timestamp).toLocaleString()}
                  </div>
                  <div className="activity-content">
                    <strong>{activity.action}</strong> by {activity.user}
                    <br />
                    <small>{activity.details}</small>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsManagerPage;
