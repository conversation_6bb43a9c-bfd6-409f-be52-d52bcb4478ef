import { useState } from 'react';
import dataService from '../../services/dataService';

const DashboardOverview = ({ stats, loading, error, onRefresh, onNavigate }) => {
  const [migrationStatus, setMigrationStatus] = useState('');
  const [migrationLoading, setMigrationLoading] = useState(false);

  const runMigration = async () => {
    setMigrationLoading(true);
    setMigrationStatus('Running migration...');

    try {
      const result = await dataService.migrateCsvData();
      setMigrationStatus('Migration completed successfully!');

      // Reload stats after migration
      setTimeout(() => {
        onRefresh();
        setMigrationStatus('');
      }, 2000);

    } catch (err) {
      setMigrationStatus('');
      console.error('Migration failed:', err);
    } finally {
      setMigrationLoading(false);
    }
  };

  const quickActions = [
    {
      id: 'manage-assessment-points',
      title: 'Manage Assessment Points',
      description: 'Add, edit, and organize assessment criteria',
      icon: '✓',
      page: 'assessment-points',
      primary: true
    },
    {
      id: 'manage-subjects',
      title: 'Manage Subjects',
      description: 'Configure subjects for different grade levels',
      icon: '📚',
      page: 'subjects'
    },
    {
      id: 'manage-categories',
      title: 'Manage Categories',
      description: 'Organize assessment types and categories',
      icon: '📋',
      page: 'categories'
    },
    {
      id: 'manage-grades',
      title: 'Manage Grades',
      description: 'Setup grade levels and age ranges',
      icon: '🎓',
      page: 'grades'
    },
    {
      id: 'version-control',
      title: 'Version Control',
      description: 'Manage configuration versions and publishing',
      icon: '🔄',
      page: 'versions'
    },
    {
      id: 'data-migration',
      title: 'Data Migration',
      description: 'Import data from CSV files',
      icon: '📥',
      page: 'migration'
    }
  ];

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Dashboard...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Report Card Admin Dashboard</h1>
        <p className="admin-page-description">
          Manage your system configuration, assessment points, and data with powerful tools designed for efficiency.
        </p>
        
        <div className="admin-page-actions">
          <button 
            onClick={onRefresh}
            className="btn btn-secondary"
          >
            🔄 Refresh Data
          </button>
          
          <button 
            onClick={runMigration}
            className="btn btn-primary"
            disabled={migrationLoading}
          >
            {migrationLoading ? 'Migrating...' : '📥 Run Migration'}
          </button>
        </div>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {migrationStatus && (
          <div style={{ 
            padding: '15px', 
            backgroundColor: '#e8f5e8', 
            border: '1px solid #4caf50', 
            borderRadius: '8px', 
            marginBottom: '24px',
            color: '#2e7d32'
          }}>
            {migrationStatus}
          </div>
        )}

        {/* Statistics Overview */}
        {stats && (
          <div className="admin-stats-grid">
            <div className="admin-stat-card">
              <div className="stat-icon">📋</div>
              <div className="stat-value">{stats.active_categories}</div>
              <div className="stat-label">Active Categories</div>
              <div className="stat-change">
                {stats.total_categories} total
              </div>
            </div>
            
            <div className="admin-stat-card">
              <div className="stat-icon">📚</div>
              <div className="stat-value">{stats.active_subjects}</div>
              <div className="stat-label">Active Subjects</div>
              <div className="stat-change">
                {stats.total_subjects} total
              </div>
            </div>
            
            <div className="admin-stat-card">
              <div className="stat-icon">✓</div>
              <div className="stat-value">{stats.active_assessment_points}</div>
              <div className="stat-label">Assessment Points</div>
              <div className="stat-change">
                {stats.total_assessment_points} total
              </div>
            </div>
            
            <div className="admin-stat-card">
              <div className="stat-icon">🎓</div>
              <div className="stat-value">{stats.active_grades}</div>
              <div className="stat-label">Active Grades</div>
              <div className="stat-change">
                {stats.total_grades} total
              </div>
            </div>

            <div className="admin-stat-card">
              <div className="stat-icon">🔄</div>
              <div className="stat-value">{stats.current_version || 'None'}</div>
              <div className="stat-label">Current Version</div>
              <div className="stat-change">Configuration</div>
            </div>
          </div>
        )}

        {/* Quick Actions Grid */}
        <div style={{ marginTop: '24px' }}>
          <h2 style={{ marginBottom: '16px', fontSize: '1.25rem', fontWeight: '600' }}>
            Quick Actions
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '16px'
          }}>
            {quickActions.map(action => (
              <div 
                key={action.id}
                className={`quick-action-card ${action.primary ? 'primary' : ''}`}
                onClick={() => onNavigate(action.page)}
                style={{
                  background: action.primary ? '#000' : '#fff',
                  color: action.primary ? '#fff' : '#333',
                  border: action.primary ? 'none' : '1px solid #e5e7eb',
                  borderRadius: '6px',
                  padding: '16px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }}
                onMouseEnter={(e) => {
                  if (action.primary) {
                    e.target.style.background = '#374151';
                  } else {
                    e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                    e.target.style.transform = 'translateY(-2px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (action.primary) {
                    e.target.style.background = '#000';
                  } else {
                    e.target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    e.target.style.transform = 'translateY(0)';
                  }
                }}
              >
                <div style={{ fontSize: '1.5rem', marginBottom: '8px' }}>
                  {action.icon}
                </div>
                <h3 style={{
                  margin: '0 0 6px 0',
                  fontSize: '1rem',
                  fontWeight: '600'
                }}>
                  {action.title}
                </h3>
                <p style={{
                  margin: 0,
                  fontSize: '0.75rem',
                  opacity: 0.8,
                  lineHeight: '1.3'
                }}>
                  {action.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* System Information */}
        <div style={{
          marginTop: '32px',
          padding: '16px',
          background: '#f9fafb',
          borderRadius: '6px',
          border: '1px solid #e5e7eb'
        }}>
          <h3 style={{ margin: '0 0 12px 0', fontSize: '1rem', fontWeight: '600' }}>
            System Information
          </h3>
          <p style={{
            margin: 0,
            color: '#6b7280',
            lineHeight: '1.5',
            fontSize: '0.75rem'
          }}>
            <strong>Admin Features:</strong> As an administrator, you have full access to configure
            all aspects of the report card system. Use the tools above to manage grades, subjects,
            categories, and assessment points. The version control system allows you to maintain
            different configurations and publish changes when ready.
          </p>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
