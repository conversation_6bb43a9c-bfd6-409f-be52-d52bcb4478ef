import { useState, useEffect } from 'react';
import dataService from '../../services/dataService';
import FormModal from '../common/FormModal';

const SubjectManagerPage = ({ onNavigate }) => {
  const [subjects, setSubjects] = useState([]);
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editingSubject, setEditingSubject] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [filterGrade, setFilterGrade] = useState('');
  const [filterGroup, setFilterGroup] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentVersion, setCurrentVersion] = useState(null);
  const [hasUnpublishedChanges, setHasUnpublishedChanges] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    grade_from: '',
    grade_to: '',
    is_compulsory: true,
    subject_group: '',
    description: '',
    sort_order: '',
    is_active: true
  });

  const subjectGroups = [
    { value: 'core', label: 'Core Subjects' },
    { value: 'science', label: 'Science' },
    { value: 'arts', label: 'Arts & Humanities' },
    { value: 'languages', label: 'Languages' },
    { value: 'physical', label: 'Physical Education' },
    { value: 'elective', label: 'Electives' }
  ];

  useEffect(() => {
    loadData();
    loadCurrentVersion();
  }, [filterGrade]);

  const loadCurrentVersion = async () => {
    try {
      const version = await dataService.getCurrentVersion();
      setCurrentVersion(version);
    } catch (err) {
      console.error('Error loading current version:', err);
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const [subjectsData, gradesData] = await Promise.all([
        dataService.getSubjects(filterGrade || null),
        dataService.getGrades(true) // Only active grades
      ]);
      setSubjects(subjectsData);
      setGrades(gradesData);
      setError('');
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const subjectData = {
        ...formData,
        grade_from: parseInt(formData.grade_from),
        grade_to: parseInt(formData.grade_to),
        sort_order: parseInt(formData.sort_order) || 0
      };

      if (editingSubject) {
        await dataService.updateSubject(editingSubject.id, subjectData);
      } else {
        await dataService.createSubject(subjectData);
      }

      await loadData();
      resetForm();
      setError('');
      setHasUnpublishedChanges(true); // Mark that there are unpublished changes
    } catch (err) {
      setError(err.message || 'Failed to save subject');
    }
  };

  const handleEdit = (subject) => {
    setEditingSubject(subject);
    setFormData({
      name: subject.name,
      display_name: subject.display_name,
      grade_from: subject.grade_from.toString(),
      grade_to: subject.grade_to.toString(),
      is_compulsory: subject.is_compulsory,
      subject_group: subject.subject_group || '',
      description: subject.description || '',
      sort_order: subject.sort_order.toString(),
      is_active: subject.is_active
    });
    setShowAddForm(true);
  };

  const handleDelete = async (subjectId) => {
    if (!window.confirm('Are you sure you want to delete this subject? This action cannot be undone.')) {
      return;
    }

    try {
      await dataService.deleteSubject(subjectId);
      await loadData();
      setError('');
      setHasUnpublishedChanges(true); // Mark that there are unpublished changes
    } catch (err) {
      setError(err.message || 'Failed to delete subject');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      display_name: '',
      grade_from: '',
      grade_to: '',
      is_compulsory: true,
      subject_group: '',
      description: '',
      sort_order: '',
      is_active: true
    });
    setEditingSubject(null);
    setShowAddForm(false);
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const getGradeName = (gradeLevel) => {
    const grade = grades.find(g => g.grade_level === gradeLevel);
    return grade ? grade.grade_code : gradeLevel.toString();
  };

  const getGradeRange = (gradeFrom, gradeTo) => {
    if (gradeFrom === gradeTo) {
      return getGradeName(gradeFrom);
    }
    return `${getGradeName(gradeFrom)} - ${getGradeName(gradeTo)}`;
  };

  // Filter subjects based on search and filters
  const filteredSubjects = subjects.filter(subject => {
    const matchesSearch = !searchTerm || 
      subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subject.display_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesGroup = !filterGroup || subject.subject_group === filterGroup;
    
    return matchesSearch && matchesGroup;
  });

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-page-content">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Subjects...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-page">
      <div className="admin-page-header">
        <h1 className="admin-page-title">Subject Management</h1>
        <p className="admin-page-description">
          Configure subjects for different grade levels with groups, compulsory status, and grade ranges.
          Subjects are used to organize assessment points and curriculum structure.
        </p>

        {currentVersion && (
          <div style={{
            marginTop: '12px',
            padding: '8px 12px',
            background: hasUnpublishedChanges ? '#fef3c7' : '#f0f9ff',
            border: `1px solid ${hasUnpublishedChanges ? '#f59e0b' : '#3b82f6'}`,
            borderRadius: '6px',
            fontSize: '0.875rem',
            color: hasUnpublishedChanges ? '#92400e' : '#1e40af'
          }}>
            📋 <strong>Current Version:</strong> {currentVersion.version_name}
            {hasUnpublishedChanges && (
              <span style={{ marginLeft: '8px', fontWeight: 'bold' }}>
                ⚠️ You have unpublished changes
              </span>
            )}
          </div>
        )}

        <div className="admin-page-actions">
          <div className="admin-page-filters">
            <div className="filter-group">
              <label>Search:</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search subjects..."
                className="filter-input"
              />
            </div>
            
            <div className="filter-group">
              <label>Grade:</label>
              <select 
                value={filterGrade} 
                onChange={(e) => setFilterGrade(e.target.value)}
                className="filter-select"
              >
                <option value="">All Grades</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.grade_level}>
                    {grade.grade_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="filter-group">
              <label>Group:</label>
              <select 
                value={filterGroup} 
                onChange={(e) => setFilterGroup(e.target.value)}
                className="filter-select"
              >
                <option value="">All Groups</option>
                {subjectGroups.map(group => (
                  <option key={group.value} value={group.value}>
                    {group.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="btn btn-primary"
          >
            ➕ Add Subject
          </button>

          {hasUnpublishedChanges && (
            <button
              onClick={() => onNavigate('version-management')}
              className="btn btn-warning"
              style={{
                background: '#f59e0b',
                color: 'white',
                animation: 'pulse 2s infinite'
              }}
            >
              📦 Publish Changes
            </button>
          )}

          <button
            onClick={loadData}
            className="btn btn-secondary"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="admin-page-content">
        {error && (
          <div className="error-summary">
            <h4>Error:</h4>
            <ul>
              <li>{error}</li>
            </ul>
          </div>
        )}

        {/* Add/Edit Modal */}
        <FormModal
          isOpen={showAddForm}
          onClose={resetForm}
          title={editingSubject ? 'Edit Subject' : 'Add Subject'}
          onSubmit={handleSubmit}
          submitLabel={editingSubject ? 'Update' : 'Create'}
          size="medium"
        >
          <div className="form-grid">
            <div className="form-group">
              <label className="form-label">Subject Name *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                placeholder="e.g., mathematics"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Display Name *</label>
              <input
                type="text"
                name="display_name"
                value={formData.display_name}
                onChange={handleInputChange}
                required
                placeholder="e.g., Mathematics"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label className="form-label">Subject Group</label>
              <select
                name="subject_group"
                value={formData.subject_group}
                onChange={handleInputChange}
                className="form-select"
              >
                <option value="">No group</option>
                {subjectGroups.map(group => (
                  <option key={group.value} value={group.value}>
                    {group.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label className="form-label">Grade From *</label>
              <select
                name="grade_from"
                value={formData.grade_from}
                onChange={handleInputChange}
                required
                className="form-select"
              >
                <option value="">Select grade</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.grade_level}>
                    {grade.grade_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Grade To *</label>
              <select
                name="grade_to"
                value={formData.grade_to}
                onChange={handleInputChange}
                required
                className="form-select"
              >
                <option value="">Select grade</option>
                {grades.map(grade => (
                  <option key={grade.id} value={grade.grade_level}>
                    {grade.grade_name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label className="form-label">Sort Order</label>
              <input
                type="number"
                name="sort_order"
                value={formData.sort_order}
                onChange={handleInputChange}
                min="0"
                placeholder="Display order"
                className="form-input"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <div className="form-checkbox-group">
                <input
                  type="checkbox"
                  name="is_compulsory"
                  checked={formData.is_compulsory}
                  onChange={handleInputChange}
                  className="form-checkbox"
                  id="is_compulsory"
                />
                <label htmlFor="is_compulsory" className="form-label">Compulsory Subject</label>
              </div>
            </div>

            <div className="form-group">
              <div className="form-checkbox-group">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="form-checkbox"
                  id="is_active_subject"
                />
                <label htmlFor="is_active_subject" className="form-label">Active</label>
              </div>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Description</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows="3"
              placeholder="Optional description for this subject"
              className="form-textarea"
            />
          </div>
        </FormModal>

        {/* Subjects Table */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">
              Subjects ({filteredSubjects.length} of {subjects.length})
            </h3>
          </div>

          <table className="data-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Display Name</th>
                <th>Grade Range</th>
                <th>Group</th>
                <th>Compulsory</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredSubjects.map(subject => (
                <tr
                  key={subject.id}
                  className="clickable-row"
                  onClick={() => handleEdit(subject)}
                  style={{ cursor: 'pointer' }}
                >
                  <td style={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                    {subject.name}
                  </td>
                  <td style={{ fontWeight: '500' }}>{subject.display_name}</td>
                  <td>{getGradeRange(subject.grade_from, subject.grade_to)}</td>
                  <td>
                    {subject.subject_group && (
                      <span className={`group-badge ${subject.subject_group}`}>
                        {subjectGroups.find(g => g.value === subject.subject_group)?.label || subject.subject_group}
                      </span>
                    )}
                  </td>
                  <td>
                    <span className={`compulsory ${subject.is_compulsory ? 'yes' : 'no'}`}>
                      {subject.is_compulsory ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td>
                    <span className={`status-badge ${subject.is_active ? 'active' : 'inactive'}`}>
                      {subject.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td onClick={(e) => e.stopPropagation()}>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <button
                        onClick={() => handleEdit(subject)}
                        className="btn btn-outline"
                        style={{ padding: '4px 8px', fontSize: '0.75rem' }}
                        title="Edit subject"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(subject.id)}
                        className="btn btn-danger"
                        style={{ padding: '4px 8px', fontSize: '0.75rem' }}
                        title="Delete subject"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {filteredSubjects.length === 0 && (
            <div style={{ padding: '48px 24px', textAlign: 'center', color: '#6b7280' }}>
              <div style={{ fontSize: '3rem', marginBottom: '16px' }}>📚</div>
              <h3 style={{ margin: '0 0 8px 0', fontSize: '1.125rem' }}>
                {subjects.length === 0 ? 'No subjects configured' : 'No subjects match your filters'}
              </h3>
              <p style={{ margin: 0, fontSize: '0.875rem' }}>
                {subjects.length === 0 
                  ? 'Start by adding subjects for your grade levels.'
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubjectManagerPage;
