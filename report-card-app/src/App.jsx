import { useState, useEffect } from 'react';
import dataService from './services/dataService';
import LoginForm from './components/LoginForm';
import AdminDashboard from './components/AdminDashboardMain';
import StudentForm from './components/StudentForm';
import AttitudeStep from './components/AttitudeStep';
import ClassroomStep from './components/ClassroomStep';
import SocialEmotionalStep from './components/SocialEmotionalStep';
import ConceptStep from './components/ConceptStep';
import SubjectStep from './components/SubjectStep';
import ReportDisplay from './components/ReportDisplay';
import { ATTITUDE_CATEGORIES, CLASSROOM_CATEGORIES, SOCIAL_EMOTIONAL_CATEGORIES } from './data/reportCardData';
import './App.css';

function App() {
  const [currentStep, setCurrentStep] = useState('login');
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [formData, setFormData] = useState({
    teacherName: '',
    studentName: '',
    grade: '',
    gender: 'neutral',
    subjects: [],
    assessment: '1st Term Assessment Profile 2023 - 2024'
  });
  const [selections, setSelections] = useState({
    attitudeResponsibility: [],
    classroomBehavior: [],
    socialEmotional: [],
    concept: [],
    subjects: {}
  });

  // Multi-step selections
  const [attitudeSteps, setAttitudeSteps] = useState({});
  const [classroomSteps, setClassroomSteps] = useState({});
  const [socialEmotionalSteps, setSocialEmotionalSteps] = useState({});
  const [conceptSelections, setConceptSelections] = useState([]);
  const [subjectSteps, setSubjectSteps] = useState({});

  // Step tracking
  const [currentAttitudeStep, setCurrentAttitudeStep] = useState(0);
  const [currentClassroomStep, setCurrentClassroomStep] = useState(0);
  const [currentSocialEmotionalStep, setCurrentSocialEmotionalStep] = useState(0);
  const [currentSubjectStep, setCurrentSubjectStep] = useState(0);

  // Get category arrays
  const attitudeCategories = Object.keys(ATTITUDE_CATEGORIES);
  const classroomCategories = Object.keys(CLASSROOM_CATEGORIES);
  const socialEmotionalCategories = Object.keys(SOCIAL_EMOTIONAL_CATEGORIES);

  // Utility function to scroll to top smoothly
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('access_token');
    localStorage.removeItem('user_info');

    // Reset state
    setUser(null);
    setIsAdmin(false);
    setCurrentStep('login');
    setFormData({
      teacherName: '',
      studentName: '',
      grade: '',
      gender: 'neutral',
      subjects: [],
      assessment: '1st Term Assessment Profile 2023 - 2024'
    });
    setSelections({
      attitudeResponsibility: [],
      classroomBehavior: [],
      socialEmotional: [],
      concept: [],
      subjects: {}
    });

    scrollToTop();
  };

  useEffect(() => {
    const initializeData = async () => {
      try {
        // Check for existing authentication
        const token = localStorage.getItem('access_token');
        const userInfo = localStorage.getItem('user_info');

        if (token && userInfo) {
          const parsedUser = JSON.parse(userInfo);
          setUser(parsedUser);
          setIsAdmin(parsedUser.role === 'admin');

          if (parsedUser.full_name) {
            setFormData(prev => ({ ...prev, teacherName: parsedUser.full_name }));
          }

          // Skip login step if already authenticated
          if (parsedUser.role === 'admin') {
            setCurrentStep('admin-dashboard');
          } else {
            setCurrentStep('student');
          }
        }

        const parsedData = await dataService.loadAllData();
        setData(parsedData);
      } catch (error) {
        console.error('Failed to load data:', error);
        // Clear invalid auth data
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_info');
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, []);

  const handleStepComplete = (stepData) => {
    switch (currentStep) {
      case 'login':
        // Handle both teacher and admin login
        setUser(stepData.user);
        setIsAdmin(stepData.isAdmin || stepData.user?.role === 'admin');

        if (stepData.teacherName) {
          setFormData(prev => ({ ...prev, teacherName: stepData.teacherName }));
        } else if (stepData.user?.full_name) {
          setFormData(prev => ({ ...prev, teacherName: stepData.user.full_name }));
        }

        // Redirect admin users to admin dashboard
        if (stepData.isAdmin || stepData.user?.role === 'admin') {
          setCurrentStep('admin-dashboard');
        } else {
          setCurrentStep('student');
        }
        scrollToTop();
        break;
      case 'student':
        setFormData(prev => ({
          ...prev,
          studentName: stepData.studentName,
          grade: stepData.grade,
          gender: stepData.gender,
          subjects: stepData.subjects
        }));
        setCurrentStep('attitude-step');
        setCurrentAttitudeStep(0);
        scrollToTop();
        break;
      case 'attitude-step':
        // This will be handled by attitude step handlers
        break;
      case 'classroom-step':
        // This will be handled by classroom step handlers
        break;
      case 'social-emotional-step':
        // This will be handled by social-emotional step handlers
        break;
      case 'sections':
        setSelections(prev => ({ ...prev, ...stepData }));
        setCurrentStep('report');
        scrollToTop();
        break;
      default:
        break;
    }
  };

  // Attitude step handlers
  const handleAttitudeStepNext = () => {
    if (currentAttitudeStep < attitudeCategories.length - 1) {
      setCurrentAttitudeStep(prev => prev + 1);
      scrollToTop();
    } else {
      // Combine all attitude selections
      const allAttitudeSelections = Object.values(attitudeSteps).flat();
      setSelections(prev => ({ ...prev, attitudeResponsibility: allAttitudeSelections }));
      setCurrentStep('classroom-step');
      setCurrentClassroomStep(0);
      scrollToTop();
    }
  };

  const handleAttitudeStepBack = () => {
    if (currentAttitudeStep > 0) {
      setCurrentAttitudeStep(prev => prev - 1);
      scrollToTop();
    } else {
      setCurrentStep('student');
      scrollToTop();
    }
  };

  const handleAttitudeStepSelection = (categoryName, selections) => {
    setAttitudeSteps(prev => ({ ...prev, [categoryName]: selections }));
  };

  // Classroom step handlers
  const handleClassroomStepNext = () => {
    if (currentClassroomStep < classroomCategories.length - 1) {
      setCurrentClassroomStep(prev => prev + 1);
      scrollToTop();
    } else {
      // Combine all classroom selections
      const allClassroomSelections = Object.values(classroomSteps).flat();
      setSelections(prev => ({ ...prev, classroomBehavior: allClassroomSelections }));
      setCurrentStep('social-emotional-step');
      setCurrentSocialEmotionalStep(0);
      scrollToTop();
    }
  };

  const handleClassroomStepBack = () => {
    if (currentClassroomStep > 0) {
      setCurrentClassroomStep(prev => prev - 1);
      scrollToTop();
    } else {
      setCurrentStep('attitude-step');
      setCurrentAttitudeStep(attitudeCategories.length - 1);
      scrollToTop();
    }
  };

  const handleClassroomStepSelection = (categoryName, selections) => {
    setClassroomSteps(prev => ({ ...prev, [categoryName]: selections }));
  };

  // Social-emotional step handlers
  const handleSocialEmotionalStepNext = () => {
    if (currentSocialEmotionalStep < socialEmotionalCategories.length - 1) {
      setCurrentSocialEmotionalStep(prev => prev + 1);
      scrollToTop();
    } else {
      // Combine all social-emotional selections
      const allSocialEmotionalSelections = Object.values(socialEmotionalSteps).flat();
      setSelections(prev => ({ ...prev, socialEmotional: allSocialEmotionalSelections }));
      setCurrentStep('concept-step');
      scrollToTop();
    }
  };

  const handleSocialEmotionalStepBack = () => {
    if (currentSocialEmotionalStep > 0) {
      setCurrentSocialEmotionalStep(prev => prev - 1);
      scrollToTop();
    } else {
      setCurrentStep('classroom-step');
      setCurrentClassroomStep(classroomCategories.length - 1);
      scrollToTop();
    }
  };

  const handleSocialEmotionalStepSelection = (categoryName, selections) => {
    setSocialEmotionalSteps(prev => ({ ...prev, [categoryName]: selections }));
  };

  // Concept step handlers
  const handleConceptStepNext = () => {
    setSelections(prev => ({ ...prev, concept: conceptSelections }));
    setCurrentStep('subject-step');
    setCurrentSubjectStep(0);
    scrollToTop();
  };

  const handleConceptStepBack = () => {
    setCurrentStep('social-emotional-step');
    setCurrentSocialEmotionalStep(socialEmotionalCategories.length - 1);
    scrollToTop();
  };

  // Subject step handlers
  const handleSubjectStepNext = () => {
    if (currentSubjectStep < formData.subjects.length - 1) {
      setCurrentSubjectStep(prev => prev + 1);
      scrollToTop();
    } else {
      // Combine all subject selections
      setSelections(prev => ({ ...prev, subjects: subjectSteps }));
      setCurrentStep('report');
      scrollToTop();
    }
  };

  const handleSubjectStepBack = () => {
    if (currentSubjectStep > 0) {
      setCurrentSubjectStep(prev => prev - 1);
      scrollToTop();
    } else {
      setCurrentStep('concept-step');
      scrollToTop();
    }
  };

  const handleSubjectStepSelection = (subject, selections) => {
    setSubjectSteps(prev => ({ ...prev, [subject]: selections }));
  };

  const handleBack = () => {
    switch (currentStep) {
      case 'student':
        setCurrentStep('login');
        scrollToTop();
        break;
      case 'attitude-step':
        handleAttitudeStepBack();
        break;
      case 'classroom-step':
        handleClassroomStepBack();
        break;
      case 'social-emotional-step':
        handleSocialEmotionalStepBack();
        break;
      case 'concept-step':
        handleConceptStepBack();
        break;
      case 'subject-step':
        handleSubjectStepBack();
        break;
      case 'report':
        setCurrentStep('subject-step');
        setCurrentSubjectStep(formData.subjects.length - 1);
        scrollToTop();
        break;
      default:
        break;
    }
  };

  const handleReset = () => {
    setCurrentStep('login');
    setFormData({
      teacherName: '',
      studentName: '',
      grade: '',
      gender: 'neutral',
      subjects: [],
      assessment: '1st Term Assessment Profile 2023 - 2024'
    });
    setSelections({
      attitudeResponsibility: [],
      classroomBehavior: [],
      socialEmotional: [],
      concept: [],
      subjects: {}
    });
    setAttitudeSteps({});
    setClassroomSteps({});
    setSocialEmotionalSteps({});
    setConceptSelections([]);
    setSubjectSteps({});
    setCurrentAttitudeStep(0);
    setCurrentClassroomStep(0);
    setCurrentSocialEmotionalStep(0);
    setCurrentSubjectStep(0);
  };

  if (loading) {
    return (
      <div className="app">
        <div className="container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <h2>Loading Report Card System...</h2>
            <p>Please wait while we initialize the application...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="app">
        <div className="container">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <h2>Error Loading Data</h2>
            <p>Unable to load report card data. Please refresh the page.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <div className="container">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <h1 className="app-title">Report Card Generator</h1>
              <p className="app-subtitle">Professional Assessment Tool for Educators</p>
            </div>
            {user && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                <div style={{ textAlign: 'right', color: 'white' }}>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {user.full_name || user.username}
                  </div>
                  <div style={{ fontSize: '12px', opacity: 0.8 }}>
                    {isAdmin ? 'Administrator' : 'Teacher'}
                  </div>
                </div>
                <button
                  onClick={handleLogout}
                  style={{
                    background: 'rgba(255,255,255,0.2)',
                    border: '1px solid rgba(255,255,255,0.3)',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="app-main">
        <div className="container">
          <div className="progress-indicator">
            <div className={`progress-step ${currentStep === 'login' ? 'active' : currentStep !== 'login' ? 'completed' : ''}`}>
              <span>1</span>
              <label>Login</label>
            </div>
            {isAdmin && currentStep === 'admin-dashboard' && (
              <div className="progress-step active">
                <span>2</span>
                <label>Admin Dashboard</label>
              </div>
            )}
            <div className={`progress-step ${currentStep === 'student' ? 'active' : ['attitude-step', 'classroom-step', 'social-emotional-step', 'sections', 'report'].includes(currentStep) ? 'completed' : ''}`}>
              <span>2</span>
              <label>Student Info</label>
            </div>
            <div className={`progress-step ${currentStep === 'attitude-step' ? 'active' : ['classroom-step', 'social-emotional-step', 'sections', 'report'].includes(currentStep) ? 'completed' : ''}`}>
              <span>3</span>
              <label>Attitude</label>
            </div>
            <div className={`progress-step ${currentStep === 'classroom-step' ? 'active' : ['social-emotional-step', 'sections', 'report'].includes(currentStep) ? 'completed' : ''}`}>
              <span>4</span>
              <label>Classroom</label>
            </div>
            <div className={`progress-step ${currentStep === 'social-emotional-step' ? 'active' : ['concept-step', 'subject-step', 'report'].includes(currentStep) ? 'completed' : ''}`}>
              <span>5</span>
              <label>Social</label>
            </div>
            <div className={`progress-step ${currentStep === 'concept-step' ? 'active' : ['subject-step', 'report'].includes(currentStep) ? 'completed' : ''}`}>
              <span>6</span>
              <label>Concept</label>
            </div>
            <div className={`progress-step ${currentStep === 'subject-step' ? 'active' : currentStep === 'report' ? 'completed' : ''}`}>
              <span>7</span>
              <label>Subjects</label>
            </div>
            <div className={`progress-step ${currentStep === 'report' ? 'active' : ''}`}>
              <span>8</span>
              <label>Report</label>
            </div>
          </div>

          <div className="form-container">
            {currentStep === 'login' && (
              <LoginForm onComplete={handleStepComplete} />
            )}

            {currentStep === 'admin-dashboard' && (
              <AdminDashboard user={user} onLogout={handleLogout} />
            )}

            {currentStep === 'student' && (
              <StudentForm
                data={data}
                onComplete={handleStepComplete}
                onBack={handleBack}
                initialData={formData}
              />
            )}

            {currentStep === 'attitude-step' && (
              <AttitudeStep
                stepNumber={currentAttitudeStep + 1}
                totalSteps={attitudeCategories.length}
                categoryName={attitudeCategories[currentAttitudeStep]}
                formData={formData}
                selections={attitudeSteps[attitudeCategories[currentAttitudeStep]]}
                onSelectionChange={(selections) =>
                  handleAttitudeStepSelection(attitudeCategories[currentAttitudeStep], selections)
                }
                onNext={handleAttitudeStepNext}
                onBack={handleAttitudeStepBack}
              />
            )}

            {currentStep === 'classroom-step' && (
              <ClassroomStep
                stepNumber={currentClassroomStep + 1}
                totalSteps={classroomCategories.length}
                categoryName={classroomCategories[currentClassroomStep]}
                formData={formData}
                selections={classroomSteps[classroomCategories[currentClassroomStep]]}
                onSelectionChange={(selections) =>
                  handleClassroomStepSelection(classroomCategories[currentClassroomStep], selections)
                }
                onNext={handleClassroomStepNext}
                onBack={handleClassroomStepBack}
              />
            )}

            {currentStep === 'social-emotional-step' && (
              <SocialEmotionalStep
                stepNumber={currentSocialEmotionalStep + 1}
                totalSteps={socialEmotionalCategories.length}
                categoryName={socialEmotionalCategories[currentSocialEmotionalStep]}
                formData={formData}
                selections={socialEmotionalSteps[socialEmotionalCategories[currentSocialEmotionalStep]]}
                onSelectionChange={(selections) =>
                  handleSocialEmotionalStepSelection(socialEmotionalCategories[currentSocialEmotionalStep], selections)
                }
                onNext={handleSocialEmotionalStepNext}
                onBack={handleSocialEmotionalStepBack}
              />
            )}

            {currentStep === 'concept-step' && (
              <ConceptStep
                formData={formData}
                selections={conceptSelections}
                onSelectionChange={setConceptSelections}
                onNext={handleConceptStepNext}
                onBack={handleConceptStepBack}
              />
            )}

            {currentStep === 'subject-step' && (
              <SubjectStep
                stepNumber={currentSubjectStep + 1}
                totalSteps={formData.subjects.length}
                subject={formData.subjects[currentSubjectStep]}
                formData={formData}
                selections={subjectSteps[formData.subjects[currentSubjectStep]]}
                onSelectionChange={(selections) =>
                  handleSubjectStepSelection(formData.subjects[currentSubjectStep], selections)
                }
                onNext={handleSubjectStepNext}
                onBack={handleSubjectStepBack}
              />
            )}

            {currentStep === 'report' && (
              <ReportDisplay
                formData={formData}
                selections={selections}
                onBack={handleBack}
                onReset={handleReset}
              />
            )}
          </div>
        </div>
      </main>

      <footer className="app-footer">
        <div className="container">
          <p>&copy; 2024 Report Card Generator. Professional Assessment Tool.</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
