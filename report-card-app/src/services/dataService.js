/**
 * Data service for loading configuration data from the backend API.
 * 
 * This service replaces the hardcoded data with dynamic loading from the server.
 */

class DataService {
  constructor() {
    this.baseURL = '/api';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Get authorization headers with JWT token
   */
  getAuthHeaders() {
    const token = localStorage.getItem('access_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  /**
   * Make an authenticated API request
   */
  async apiRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getAuthHeaders(),
      ...options
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid, redirect to login
          localStorage.removeItem('access_token');
          localStorage.removeItem('user_info');
          window.location.reload();
          return null;
        }
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request error for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Get data from cache or fetch from API
   */
  async getCachedData(key, fetchFunction) {
    const cached = this.cache.get(key);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }

    const data = await fetchFunction();
    this.cache.set(key, { data, timestamp: now });
    return data;
  }

  /**
   * Clear cache for a specific key or all cache
   */
  clearCache(key = null) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Load all categories
   */
  async getCategories(categoryType = null, isActive = true) {
    const cacheKey = `categories_${categoryType}_${isActive}`;
    
    return this.getCachedData(cacheKey, async () => {
      const params = new URLSearchParams();
      if (categoryType) params.append('category_type', categoryType);
      if (isActive !== null) params.append('is_active', isActive);
      
      const endpoint = `/config/categories${params.toString() ? '?' + params.toString() : ''}`;
      return await this.apiRequest(endpoint);
    });
  }

  /**
   * Load all subjects
   */
  async getSubjects(grade = null, isActive = true) {
    const cacheKey = `subjects_${grade}_${isActive}`;
    
    return this.getCachedData(cacheKey, async () => {
      const params = new URLSearchParams();
      if (grade) params.append('grade', grade);
      if (isActive !== null) params.append('is_active', isActive);
      
      const endpoint = `/config/subjects${params.toString() ? '?' + params.toString() : ''}`;
      return await this.apiRequest(endpoint);
    });
  }

  /**
   * Load assessment points
   */
  async getAssessmentPoints(filters = {}) {
    const { categoryId, subjectId, pointType, grade, isActive = true } = filters;
    const cacheKey = `assessment_points_${JSON.stringify(filters)}`;

    return this.getCachedData(cacheKey, async () => {
      const params = new URLSearchParams();
      if (categoryId && categoryId !== '') params.append('category_id', categoryId);
      if (subjectId && subjectId !== '') params.append('subject_id', subjectId);
      if (pointType && pointType !== '') params.append('point_type', pointType);
      if (grade && grade !== '') params.append('grade', grade);
      if (isActive !== null) params.append('is_active', isActive);

      const endpoint = `/config/assessment-points${params.toString() ? '?' + params.toString() : ''}`;
      return await this.apiRequest(endpoint);
    });
  }

  /**
   * Load configuration statistics
   */
  async getConfigStats() {
    return this.getCachedData('config_stats', async () => {
      return await this.apiRequest('/config/stats');
    });
  }

  // Grade Management Methods

  /**
   * Get all grades
   */
  async getGrades(isActive = null) {
    const params = new URLSearchParams();
    if (isActive !== null) {
      params.append('is_active', isActive);
    }
    const url = `/config/grades${params.toString() ? '?' + params.toString() : ''}`;
    return await this.apiRequest(url);
  }

  /**
   * Get a specific grade by ID
   */
  async getGrade(gradeId) {
    return await this.apiRequest(`/config/grades/${gradeId}`);
  }

  /**
   * Create a new grade
   */
  async createGrade(gradeData) {
    return await this.apiRequest('/config/grades', {
      method: 'POST',
      body: JSON.stringify(gradeData)
    });
  }

  /**
   * Update an existing grade
   */
  async updateGrade(gradeId, gradeData) {
    return await this.apiRequest(`/config/grades/${gradeId}`, {
      method: 'PUT',
      body: JSON.stringify(gradeData)
    });
  }

  /**
   * Delete a grade
   */
  async deleteGrade(gradeId) {
    return await this.apiRequest(`/config/grades/${gradeId}`, {
      method: 'DELETE'
    });
  }

  // Category Management Methods

  /**
   * Get all categories
   */
  async getCategories(categoryType = null, isActive = null) {
    const params = new URLSearchParams();
    if (categoryType) {
      params.append('category_type', categoryType);
    }
    if (isActive !== null) {
      params.append('is_active', isActive);
    }
    const url = `/config/categories${params.toString() ? '?' + params.toString() : ''}`;
    return await this.apiRequest(url);
  }

  /**
   * Get a specific category by ID
   */
  async getCategory(categoryId) {
    return await this.apiRequest(`/config/categories/${categoryId}`);
  }

  /**
   * Create a new category
   */
  async createCategory(categoryData) {
    return await this.apiRequest('/config/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    });
  }

  /**
   * Update an existing category
   */
  async updateCategory(categoryId, categoryData) {
    return await this.apiRequest(`/config/categories/${categoryId}`, {
      method: 'PUT',
      body: JSON.stringify(categoryData)
    });
  }

  /**
   * Delete a category
   */
  async deleteCategory(categoryId) {
    return await this.apiRequest(`/config/categories/${categoryId}`, {
      method: 'DELETE'
    });
  }

  // Subject Management Methods

  /**
   * Get all subjects
   */
  async getSubjects(grade = null, isActive = null) {
    const params = new URLSearchParams();
    if (grade !== null) {
      params.append('grade', grade);
    }
    if (isActive !== null) {
      params.append('is_active', isActive);
    }
    const url = `/config/subjects${params.toString() ? '?' + params.toString() : ''}`;
    return await this.apiRequest(url);
  }

  /**
   * Get a specific subject by ID
   */
  async getSubject(subjectId) {
    return await this.apiRequest(`/config/subjects/${subjectId}`);
  }

  /**
   * Create a new subject
   */
  async createSubject(subjectData) {
    return await this.apiRequest('/config/subjects', {
      method: 'POST',
      body: JSON.stringify(subjectData)
    });
  }

  /**
   * Update an existing subject
   */
  async updateSubject(subjectId, subjectData) {
    return await this.apiRequest(`/config/subjects/${subjectId}`, {
      method: 'PUT',
      body: JSON.stringify(subjectData)
    });
  }

  /**
   * Delete a subject
   */
  async deleteSubject(subjectId) {
    return await this.apiRequest(`/config/subjects/${subjectId}`, {
      method: 'DELETE'
    });
  }

  // Assessment Point Management Methods

  /**
   * Get all assessment points
   */
  async getAssessmentPoints(categoryId = null, subjectId = null, pointType = null, grade = null) {
    const params = new URLSearchParams();
    if (categoryId && categoryId !== '') {
      params.append('category_id', categoryId);
    }
    if (subjectId && subjectId !== '') {
      params.append('subject_id', subjectId);
    }
    if (pointType && pointType !== '') {
      params.append('point_type', pointType);
    }
    if (grade !== null && grade !== '') {
      params.append('grade', grade);
    }
    const url = `/config/assessment-points${params.toString() ? '?' + params.toString() : ''}`;
    return await this.apiRequest(url);
  }

  /**
   * Get a specific assessment point by ID
   */
  async getAssessmentPoint(pointId) {
    return await this.apiRequest(`/config/assessment-points/${pointId}`);
  }

  /**
   * Create a new assessment point
   */
  async createAssessmentPoint(pointData) {
    return await this.apiRequest('/config/assessment-points', {
      method: 'POST',
      body: JSON.stringify(pointData)
    });
  }

  /**
   * Update an existing assessment point
   */
  async updateAssessmentPoint(pointId, pointData) {
    return await this.apiRequest(`/config/assessment-points/${pointId}`, {
      method: 'PUT',
      body: JSON.stringify(pointData)
    });
  }

  /**
   * Delete an assessment point
   */
  async deleteAssessmentPoint(pointId) {
    return await this.apiRequest(`/config/assessment-points/${pointId}`, {
      method: 'DELETE'
    });
  }

  // Version Management Methods

  /**
   * Get all configuration versions
   */
  async getVersions() {
    return await this.apiRequest('/config/versions');
  }

  /**
   * Create a new configuration version
   */
  async createVersion(versionData) {
    return await this.apiRequest('/config/versions', {
      method: 'POST',
      body: JSON.stringify(versionData)
    });
  }

  /**
   * Publish a configuration version
   */
  async publishVersion(versionId) {
    return await this.apiRequest(`/config/versions/${versionId}/publish`, {
      method: 'PUT'
    });
  }

  /**
   * Set a version as current
   */
  async setCurrentVersion(versionId) {
    return await this.apiRequest(`/config/versions/${versionId}/set-current`, {
      method: 'PUT'
    });
  }

  /**
   * Load all configuration versions
   */
  async getVersions(includeDrafts = true) {
    const cacheKey = `versions_${includeDrafts}`;

    return this.getCachedData(cacheKey, async () => {
      const params = new URLSearchParams();
      params.append('include_drafts', includeDrafts);

      const endpoint = `/versions/?${params.toString()}`;
      return await this.apiRequest(endpoint);
    });
  }

  /**
   * Get current published version
   */
  async getCurrentVersion() {
    return this.getCachedData('current_version', async () => {
      return await this.apiRequest('/versions/current');
    });
  }

  /**
   * Transform backend data to frontend format for compatibility
   */
  transformCategoriesData(categories) {
    const grouped = {};
    
    categories.forEach(category => {
      const type = category.category_type;
      if (!grouped[type]) {
        grouped[type] = {};
      }
      grouped[type][category.name] = {
        displayName: category.display_name,
        description: category.description || '',
        points: [] // Will be populated by assessment points
      };
    });
    
    return grouped;
  }

  /**
   * Transform subjects data to frontend format
   */
  transformSubjectsData(subjects) {
    return subjects.map(subject => ({
      name: subject.name,
      displayName: subject.display_name,
      gradeFrom: subject.grade_from,
      gradeTo: subject.grade_to,
      isCompulsory: subject.is_compulsory,
      subjectGroup: subject.subject_group,
      excludesSubjects: subject.excludes_subjects || [],
      alternativeTo: subject.alternative_to,
      options: subject.options || [],
      sortOrder: subject.sort_order
    }));
  }

  /**
   * Transform assessment points to frontend format
   */
  transformAssessmentPointsData(points) {
    const grouped = {};
    
    points.forEach(point => {
      let key;
      if (point.category && point.category.name) {
        key = point.category.name;
      } else if (point.subject && point.subject.name) {
        key = point.subject.name;
      } else {
        key = point.point_type;
      }
      
      if (!grouped[key]) {
        grouped[key] = [];
      }
      
      grouped[key].push({
        text: point.point_text,
        type: point.point_type,
        gradeFrom: point.grade_from,
        gradeTo: point.grade_to,
        sortOrder: point.sort_order
      });
    });
    
    // Sort points within each group
    Object.keys(grouped).forEach(key => {
      grouped[key].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    });
    
    return grouped;
  }

  /**
   * Load all data needed for the report card application
   */
  async loadAllData(grade = null) {
    try {
      console.log('Loading configuration data from server...');
      
      // Load all required data in parallel
      const [categories, subjects, behaviorPoints, subjectPoints, conceptPoints] = await Promise.all([
        this.getCategories(),
        this.getSubjects(grade),
        this.getAssessmentPoints({ pointType: 'behavior' }),
        this.getAssessmentPoints({ pointType: 'subject' }),
        this.getAssessmentPoints({ pointType: 'concept' })
      ]);

      // Transform data to frontend format
      const transformedData = {
        categories: this.transformCategoriesData(categories),
        subjects: this.transformSubjectsData(subjects),
        behaviorPoints: this.transformAssessmentPointsData(behaviorPoints),
        subjectPoints: this.transformAssessmentPointsData(subjectPoints),
        conceptPoints: this.transformAssessmentPointsData(conceptPoints),
        
        // Metadata
        loadedAt: new Date().toISOString(),
        grade: grade,
        source: 'server'
      };

      console.log('Configuration data loaded successfully:', transformedData);
      return transformedData;
      
    } catch (error) {
      console.error('Failed to load configuration data:', error);
      
      // Fallback to empty data structure
      return {
        categories: {},
        subjects: [],
        behaviorPoints: {},
        subjectPoints: {},
        conceptPoints: {},
        loadedAt: new Date().toISOString(),
        grade: grade,
        source: 'fallback',
        error: error.message
      };
    }
  }

  /**
   * Migrate CSV data (admin only)
   */
  async migrateCsvData() {
    try {
      const result = await this.apiRequest('/config/migrate-csv', {
        method: 'POST'
      });
      
      // Clear cache after migration
      this.clearCache();
      
      return result;
    } catch (error) {
      console.error('CSV migration failed:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const dataService = new DataService();
export default dataService;
