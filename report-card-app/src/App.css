/* Editorial Studio Design System */

/* Application Layout - Full viewport utilization */
#root {
  width: 100%;
  height: 100%;
  min-height: 100vh;
}

/* Reset and Base Styles */
*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  width: 100%;
  height: 100%;
}

:root {
  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Color Palette */
  --color-black: #000000;
  --color-gray-900: #111827;
  --color-gray-800: #1f2937;
  --color-gray-700: #374151;
  --color-gray-600: #4b5563;
  --color-gray-500: #6b7280;
  --color-gray-400: #9ca3af;
  --color-gray-300: #d1d5db;
  --color-gray-200: #e5e7eb;
  --color-gray-100: #f3f4f6;
  --color-gray-50: #f9fafb;
  --color-white: #ffffff;

  --color-primary: #000000;
  --color-primary-light: #374151;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-error: #ef4444;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Layout */
  --container-max-width: 1200px;
  --container-padding: var(--space-6);
  --content-max-width: 900px;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: var(--color-gray-900);
  background-color: var(--color-white);
  min-height: 100vh;
}

/* Clean Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  background: #f9fafb;
}

/* Clean Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
  background-color: #fff;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
  font-family: inherit;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 16px;
  align-items: end;
}

.form-checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

/* Clean Button Styles */
.btn {
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
  border-color: #6b7280;
}

.btn-secondary:hover {
  background: #4b5563;
  border-color: #4b5563;
}

.btn-outline {
  background: transparent;
  color: #374151;
  border-color: #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-danger {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* Clean Table Styles */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.data-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.data-table tr:hover {
  background: #f9fafb;
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Clickable Row */
.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.clickable-row:hover {
  background: #f3f4f6;
}

/* Clean Card Styles */
.card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

/* App Layout */
.app {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
}

.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  width: 100%;
  box-sizing: border-box;
}

/* Header */
.app-header {
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--space-3) 0;
}

.app-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--color-black);
  margin-bottom: var(--space-1);
}

.app-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  font-weight: 400;
  letter-spacing: 0.025em;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: var(--space-4) 0;
  background-color: transparent;
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--space-4);
  gap: var(--space-4);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  position: relative;
}

.progress-step span {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-xs);
  background-color: var(--color-gray-300);
  color: var(--color-gray-600);
  transition: all var(--transition-base);
}

.progress-step.active span {
  background-color: var(--color-black);
  color: var(--color-white);
}

.progress-step.completed span {
  background-color: var(--color-success);
  color: var(--color-white);
}

.progress-step label {
  font-size: var(--font-size-xs);
  font-weight: 500;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.progress-step.active label {
  color: var(--color-black);
}

/* Form Container with Animation */
.form-container {
  background-color: var(--color-white);
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
  animation: slideInUp 0.5s ease-out;
  transition: all var(--transition-base);
}

.form-container:hover {
  box-shadow: 0 8px 12px -1px rgba(0, 0, 0, 0.15), 0 4px 8px -1px rgba(0, 0, 0, 0.1);
}

/* Slide in animation */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Section */
.form-section {
  padding: var(--space-4);
}

.form-header {
  margin-bottom: var(--space-3);
  text-align: center;
}

.form-header h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: var(--space-2);
  letter-spacing: -0.025em;
}

.form-header p {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.4;
}

/* Form Styles */
.form {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Browser Reset for Form Elements */
input, select, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  outline: none !important;
  box-shadow: none !important;
}

input:focus, select:focus, textarea:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Completely Fixed Form Input Styling - Single Clean Border */
.form-input,
.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-base), box-shadow var(--transition-base);
  background-color: var(--color-white);
  color: var(--color-gray-900) !important;
  box-shadow: none !important;
  outline: none !important;
  position: relative;
}

.form-input:hover,
.form-select:hover {
  border-color: var(--color-gray-400);
  box-shadow: none;
}

.form-input:focus,
.form-select:focus {
  outline: none !important;
  border-color: var(--color-black) !important;
  color: var(--color-gray-900) !important;
  box-shadow: 0 0 0 1px var(--color-black) !important;
}

.form-input::placeholder {
  color: var(--color-gray-500);
}

.form-error {
  display: block;
  color: var(--color-error);
  font-size: var(--font-size-sm);
  margin-top: var(--space-2);
  font-weight: 500;
}

/* Completely Fixed Error states - Single Clean Border */
.form-input.error,
.form-select.error {
  border-color: var(--color-error) !important;
  background-color: #fef2f2 !important;
  box-shadow: 0 0 0 1px var(--color-error) !important;
  outline: none !important;
}

.grade-option.error .grade-option-label {
  border-color: var(--color-error) !important;
  background-color: #fef2f2;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.subjects-grid.error {
  border: 2px solid var(--color-error);
  border-radius: 8px;
  background-color: #fef2f2;
  padding: var(--space-3);
}

/* Error summary near buttons */
.error-summary {
  background-color: #fef2f2;
  border: 1px solid var(--color-error);
  border-radius: 8px;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.error-summary h4 {
  color: var(--color-error);
  font-size: var(--font-size-base);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.error-summary ul {
  margin: 0;
  padding-left: var(--space-5);
  color: var(--color-error);
  font-size: var(--font-size-sm);
}

.error-summary li {
  margin-bottom: var(--space-1);
}

/* Subjects Grid */
.subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-3);
  margin-top: var(--space-3);
}

/* Enhanced Subject Items with Animations */
.subject-item {
  padding: var(--space-3);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  transition: all var(--transition-base);
  animation: fadeInUp 0.3s ease-out;
  animation-fill-mode: both;
  background-color: var(--color-white);
}

.subject-item:nth-child(1) { animation-delay: 0.1s; }
.subject-item:nth-child(2) { animation-delay: 0.15s; }
.subject-item:nth-child(3) { animation-delay: 0.2s; }
.subject-item:nth-child(4) { animation-delay: 0.25s; }

.subject-item:hover {
  border-color: var(--color-gray-400);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.subject-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Fixed Modern Custom Checkbox Styling */
.subject-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.subject-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
  z-index: 1;
}

.subject-checkbox::before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 12px;
  border: 2px solid var(--color-gray-300);
  border-radius: 4px;
  background-color: var(--color-white);
  transition: all var(--transition-base);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.subject-checkbox:hover::before {
  border-color: var(--color-gray-400);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.subject-checkbox input[type="checkbox"]:checked + *::before,
.subject-checkbox input[type="checkbox"]:checked ~ *::before,
.subject-checkbox:has(input[type="checkbox"]:checked)::before {
  background-color: var(--color-black);
  border-color: var(--color-black);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.subject-checkbox::after {
  content: '✓';
  position: absolute;
  left: 0;
  top: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%) scale(0);
  color: var(--color-white);
  font-size: 14px;
  line-height: 1;
  font-weight: 800;
  transition: transform var(--transition-base);
  pointer-events: none;
  z-index: 2;
}

.subject-checkbox input[type="checkbox"]:checked + *::after,
.subject-checkbox input[type="checkbox"]:checked ~ *::after,
.subject-checkbox:has(input[type="checkbox"]:checked)::after {
  transform: translateY(-50%) scale(1);
}

.subject-checkbox input[type="checkbox"]:focus + *::before,
.subject-checkbox:has(input[type="checkbox"]:focus)::before {
  outline: 2px solid var(--color-black);
  outline-offset: 2px;
}

.subject-name {
  font-weight: 500;
  color: var(--color-gray-900);
}

.subject-with-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* Modern Subject Option Select */
.subject-option-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-gray-200);
  border-radius: 6px;
  font-size: var(--font-size-sm);
  background-color: var(--color-white);
  color: var(--color-gray-900) !important;
  transition: all var(--transition-base);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.subject-option-select:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.subject-option-select:focus {
  outline: none;
  border-color: var(--color-black);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Points Container */
.points-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  background-color: var(--color-gray-50);
}

.point-item {
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--color-gray-200);
}

.point-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.point-checkbox {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  width: 100%;
  min-height: 44px; /* Ensure minimum touch target size */
}


/* Section Groups */
.section-group {
  margin-bottom: var(--space-12);
  padding-bottom: var(--space-8);
  border-bottom: 1px solid var(--color-gray-200);
}

.section-group:last-child {
  border-bottom: none;
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
}

.subject-section {
  margin-bottom: var(--space-8);
}

.subject-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}

/* Attitude Categories */
.attitude-categories {
  display: grid;
  gap: var(--space-10);
}

.category-section {
  border: 1px solid var(--color-gray-200);
  border-radius: 12px;
  padding: var(--space-6);
  background-color: var(--color-gray-50);
}

.category-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-3);
  border-bottom: 2px solid var(--color-gray-300);
}

.points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-4);
}

/* Enhanced Point Items with Animations */
.points-grid .point-item {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  transition: all var(--transition-base);
  cursor: pointer;
  animation: fadeInUp 0.3s ease-out;
  animation-fill-mode: both;
}

.points-grid .point-item:nth-child(1) { animation-delay: 0.1s; }
.points-grid .point-item:nth-child(2) { animation-delay: 0.15s; }
.points-grid .point-item:nth-child(3) { animation-delay: 0.2s; }
.points-grid .point-item:nth-child(4) { animation-delay: 0.25s; }
.points-grid .point-item:nth-child(5) { animation-delay: 0.3s; }
.points-grid .point-item:nth-child(6) { animation-delay: 0.35s; }

.points-grid .point-item:hover {
  border-color: var(--color-gray-400);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Radio Option Styling for Math/EVS */
.radio-option {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: var(--space-3);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  transition: all var(--transition-base);
  background-color: var(--color-white);
  margin-bottom: var(--space-2);
}

.radio-option:hover {
  border-color: var(--color-gray-400);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.radio-option input[type="radio"] {
  margin-right: var(--space-3);
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.radio-option input[type="radio"]:checked + .radio-label {
  font-weight: 600;
  color: var(--color-black);
}

.radio-option:has(input[type="radio"]:checked) {
  border-color: var(--color-black);
  background-color: var(--color-gray-50);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.radio-label {
  flex: 1;
  transition: all var(--transition-base);
}


/* Step Components */
.attitude-step-container {
  margin-bottom: var(--space-8);
}

.category-main-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-black);
  margin: var(--space-4) 0;
  padding: var(--space-4);
  background-color: var(--color-gray-100);
  border-radius: 8px;
  text-align: center;
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-black);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Buttons with Animations */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-base);
  min-width: 100px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--color-black);
  color: var(--color-white);
  border-color: var(--color-black);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-gray-800);
  border-color: var(--color-gray-800);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--color-white);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-gray-200);
  gap: var(--space-4);
}

/* Selected Preview */
.selected-preview {
  margin-top: var(--space-8);
  padding: var(--space-6);
  background-color: var(--color-gray-50);
  border-radius: 8px;
  border: 1px solid var(--color-gray-200);
}

.selected-preview h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: var(--space-4);
}

.preview-content {
  font-size: var(--font-size-sm);
  line-height: 1.6;
  color: var(--color-gray-800);
}

.preview-empty {
  color: var(--color-gray-500);
  font-style: italic;
}

/* Report Display */
.report-container {
  max-width: 900px;
  margin: 0 auto;
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-12);
  padding: var(--space-8);
  background-color: var(--color-gray-50);
  border-radius: 8px;
  border: 1px solid var(--color-gray-200);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.summary-item label {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.summary-item span {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--color-gray-900);
}

.report-sections {
  margin-bottom: var(--space-12);
}

.report-section {
  margin-bottom: var(--space-10);
  padding-bottom: var(--space-8);
  border-bottom: 1px solid var(--color-gray-200);
}

.report-section:last-child {
  border-bottom: none;
}

.report-section h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-black);
  margin-bottom: var(--space-4);
  letter-spacing: -0.025em;
}

.report-section p {
  font-size: var(--font-size-base);
  line-height: 1.7;
  color: var(--color-gray-800);
}

.subject-feedback {
  margin-bottom: var(--space-6);
}

.subject-feedback h4 {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--color-gray-900);
  margin-bottom: var(--space-3);
}

.subject-feedback p {
  font-size: var(--font-size-sm);
  line-height: 1.6;
  color: var(--color-gray-700);
}

/* JSON Section */
.json-section {
  margin-top: var(--space-12);
  padding-top: var(--space-8);
  border-top: 1px solid var(--color-gray-200);
}

.json-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.json-header h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-black);
}

.json-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.json-display {
  background-color: var(--color-gray-900);
  border-radius: 8px;
  padding: var(--space-6);
  overflow-x: auto;
}

.json-display pre {
  color: var(--color-gray-100);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: var(--space-6);
}

/* Enhanced Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-black);
  border-radius: 50%;
  animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-700);
}

/* Form Info */
.form-info {
  margin-top: var(--space-8);
  padding-top: var(--space-6);
  border-top: 1px solid var(--color-gray-200);
}

.info-text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  text-align: center;
  line-height: 1.5;
}

/* Footer */
.app-footer {
  background-color: var(--color-gray-900);
  color: var(--color-gray-400);
  padding: var(--space-8) 0;
  text-align: center;
  margin-top: auto;
}

.app-footer p {
  font-size: var(--font-size-sm);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-container {
    max-width: 100%;
  }

  .subjects-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .points-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  :root {
    --container-padding: var(--space-3);
  }

  .app-header {
    padding: var(--space-2) 0;
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  .app-subtitle {
    font-size: var(--font-size-sm);
  }

  .app-main {
    padding: var(--space-3) 0;
  }

  .form-header h2 {
    font-size: var(--font-size-lg);
  }

  .form-header p {
    font-size: var(--font-size-xs);
  }

  .form-header {
    margin-bottom: var(--space-2);
  }

  .progress-indicator {
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    flex-wrap: wrap;
  }

  .progress-step span {
    width: 28px;
    height: 28px;
    font-size: 0.625rem;
  }

  .progress-step label {
    font-size: 0.5rem;
  }

  .form-section {
    padding: var(--space-3);
  }

  .subjects-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .form-group {
    margin-bottom: var(--space-3);
  }

  .points-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
    gap: var(--space-2);
    margin-top: var(--space-3);
    padding-top: var(--space-3);
  }

  .form-actions .btn {
    width: 100%;
  }

  .json-header {
    flex-direction: column;
    align-items: stretch;
  }

  .json-actions {
    justify-content: center;
  }

  .report-summary {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

@media (max-width: 480px) {
  :root {
    --container-padding: var(--space-2);
  }

  .app-header {
    padding: var(--space-2) 0;
  }

  .app-title {
    font-size: var(--font-size-xl);
  }

  .app-main {
    padding: var(--space-2) 0;
  }

  .form-section {
    padding: var(--space-2);
  }

  .form-header {
    margin-bottom: var(--space-2);
  }

  .points-container {
    max-height: 250px;
  }

  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
  }

  .progress-indicator {
    gap: var(--space-2);
    margin-bottom: var(--space-2);
  }

  .progress-step span {
    width: 24px;
    height: 24px;
    font-size: 0.5rem;
  }
}

/* Admin Layout System - Full Viewport Utilization */
.admin-layout {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: var(--color-gray-50);
  overflow: hidden;
  margin: 0;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.admin-sidebar {
  width: 280px;
  background: var(--color-white);
  border-right: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-base);
  position: relative;
  z-index: 100;
}

.admin-sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  min-height: 64px;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 600;
  color: var(--color-gray-900);
}

.logo-icon {
  font-size: var(--font-size-xl);
  flex-shrink: 0;
}

.logo-text {
  font-size: var(--font-size-lg);
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
}

.nav-item {
  margin-bottom: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  color: var(--color-gray-700);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-900);
}

.nav-link.active {
  background: var(--color-primary);
  color: var(--color-white);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--color-accent);
}

.nav-icon {
  font-size: var(--font-size-lg);
  margin-right: var(--space-3);
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.nav-label {
  flex: 1;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
}

.nav-badge {
  background: var(--color-accent);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  margin-left: var(--space-2);
}

.nav-arrow {
  margin-left: var(--space-2);
  font-size: var(--font-size-xs);
  transition: transform var(--transition-fast);
}

.nav-arrow.expanded {
  transform: rotate(180deg);
}

.nav-children {
  background: var(--color-gray-50);
}

.nav-children .nav-link {
  padding-left: var(--space-8);
  font-size: var(--font-size-sm);
}

.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--color-gray-200);
}

.sidebar-user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background: var(--color-gray-50);
  border-radius: 6px;
  margin-bottom: var(--space-3);
}

.user-details {
  flex: 1;
}

.user-details .user-name {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--space-1);
}

.user-details .user-role {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
}

.logout-btn {
  background: var(--color-error);
  color: var(--color-white);
  border: none;
  border-radius: 4px;
  padding: var(--space-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.sidebar-toggle {
  width: 100%;
  padding: var(--space-2);
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-300);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background: var(--color-gray-200);
}

.admin-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  width: 100%;
  transition: margin-left var(--transition-base);
}

.admin-main.sidebar-collapsed {
  margin-left: 0;
}

.admin-header {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  flex: 1;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--space-2);
}

.mobile-menu-toggle-floating {
  display: none;
  position: fixed;
  top: var(--space-4);
  left: var(--space-4);
  z-index: 1000;
  background: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all var(--transition-fast);
}

.mobile-menu-toggle-floating:hover {
  background: var(--color-gray-50);
  transform: scale(1.05);
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-separator {
  margin: 0 var(--space-2);
  color: var(--color-gray-400);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 var(--space-6);
}

.search-container {
  position: relative;
  width: 100%;
}

.global-search {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
  border: 1px solid var(--color-gray-300);
  border-radius: 8px;
  font-size: var(--font-size-sm);
  background: var(--color-gray-50);
  transition: all var(--transition-fast);
}

.global-search:focus {
  outline: none;
  border-color: var(--color-primary);
  background: var(--color-white);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.quick-actions {
  display: flex;
  gap: var(--space-2);
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.quick-action-btn:hover {
  background: var(--color-gray-200);
  border-color: var(--color-gray-400);
}

.action-icon {
  font-size: var(--font-size-base);
}

.action-label {
  font-weight: 500;
}

.user-menu {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.user-menu-trigger:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: var(--color-gray-300);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
}

.user-name {
  font-weight: 500;
  color: var(--color-gray-900);
}

.dropdown-arrow {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
}

.user-info {
  padding: var(--space-4);
}

.user-info .user-name {
  font-weight: 600;
  color: var(--color-gray-900);
}

.user-role {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-top: var(--space-1);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background var(--transition-fast);
  font-size: var(--font-size-sm);
}

.menu-item:hover {
  background: var(--color-gray-100);
}

.menu-icon {
  font-size: var(--font-size-base);
}

.admin-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: var(--color-gray-50);
  height: 100%;
  width: 100%;
}

/* Mobile Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  display: none;
}

@media (max-width: 768px) {
  .sidebar-overlay {
    display: block;
  }
}

/* Admin Layout Responsive Styles */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    bottom: 0;
    z-index: 200;
    transition: left var(--transition-base);
  }

  .admin-sidebar.open {
    left: 0;
  }

  .admin-sidebar.collapsed {
    left: -64px;
  }

  .admin-sidebar.collapsed.open {
    left: 0;
    width: 64px;
  }

  .admin-main {
    margin-left: 0;
    width: 100%;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-menu-toggle-floating {
    display: block;
  }

  .header-center {
    display: none;
  }

  .quick-actions {
    display: none;
  }

  .quick-action-btn .action-label {
    display: none;
  }

  .breadcrumb {
    display: none;
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .admin-content {
    padding: var(--space-2);
  }
}

/* Enhanced Admin Components */
.admin-form {
  max-width: none;
  width: 100%;
}

/* Modern Form Controls */
.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: #ffffff;
}

.form-control:hover {
  border-color: #d1d5db;
}

.form-control::placeholder {
  color: #9ca3af;
}

.form-control:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
  border-color: #e5e7eb;
}

/* Textarea specific styling */
textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

/* Select specific styling */
select.form-control {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}

/* Input group styling */
.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-group .form-control {
  flex: 1;
}

/* Form group styling */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.form-section {
  background: var(--color-white);
  border-radius: 8px;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  border: 1px solid var(--color-gray-200);
}

.form-section h3 {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-gray-900);
  font-size: var(--font-size-lg);
  font-weight: 600;
  border-bottom: 1px solid var(--color-gray-200);
  padding-bottom: var(--space-2);
}

.form-help {
  display: block;
  margin-top: var(--space-1);
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

/* Assessment Periods */
.assessment-periods {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.period-input-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.period-input-group .form-control {
  flex: 1;
}

/* Success/Error Messages */
.success-summary {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  padding: var(--space-3);
  border-radius: 4px;
  margin-bottom: var(--space-4);
}

.success-summary h4 {
  margin: 0 0 var(--space-1) 0;
  font-size: var(--font-size-base);
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No Data State */
.no-data {
  text-align: center;
  padding: var(--space-6);
  color: var(--color-gray-600);
  font-style: italic;
}

/* File Info */
.file-info {
  margin-top: var(--space-2);
  padding: var(--space-2);
  background: var(--color-gray-50);
  border-radius: 4px;
  font-size: var(--font-size-sm);
}

/* Bulk Operations */
.bulk-operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.bulk-operation-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  transition: all var(--transition-fast);
}

.bulk-operation-card:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.operation-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.operation-icon {
  font-size: var(--font-size-xl);
}

.operation-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-gray-900);
}

.operation-description {
  color: var(--color-gray-600);
  margin-bottom: var(--space-3);
  line-height: 1.5;
}

.operation-details {
  background: var(--color-gray-50);
  padding: var(--space-3);
  border-radius: 4px;
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
  line-height: 1.6;
}

.operation-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.file-upload-section {
  margin: var(--space-2) 0;
}

/* Operation Progress */
.operation-progress {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-gray-200);
  border-radius: 4px;
  overflow: hidden;
  margin: var(--space-2) 0;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.3s ease;
}

/* Export Types */
.export-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.export-type-card {
  background: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.export-type-card:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.export-type-card.selected {
  border-color: var(--color-primary);
  background: var(--color-primary-light, #f0f8ff);
}

.export-type-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.export-icon {
  font-size: var(--font-size-xl);
}

.export-type-header h4 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-gray-900);
}

.export-description {
  color: var(--color-gray-600);
  margin-bottom: var(--space-3);
  line-height: 1.5;
}

.export-fields {
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
}

.export-fields ul {
  margin: var(--space-1) 0 0 var(--space-4);
  padding: 0;
}

.export-fields li {
  margin-bottom: var(--space-1);
}

/* Export Filters */
.export-filters {
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  margin-top: var(--space-4);
}

.export-filters h4 {
  margin: 0 0 var(--space-3) 0;
  color: var(--color-gray-900);
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

/* Migration Sources */
.migration-sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.migration-source-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  transition: all var(--transition-fast);
}

.migration-source-card:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.source-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.source-icon {
  font-size: var(--font-size-xl);
}

.source-header h4 {
  margin: 0;
  flex: 1;
  font-size: var(--font-size-lg);
  color: var(--color-gray-900);
}

.source-description {
  color: var(--color-gray-600);
  margin-bottom: var(--space-3);
  line-height: 1.5;
}

.supported-files {
  background: var(--color-gray-50);
  padding: var(--space-2);
  border-radius: 4px;
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
}

.source-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.selected-files {
  background: var(--color-gray-50);
  padding: var(--space-2);
  border-radius: 4px;
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
}

.coming-soon {
  text-align: center;
  padding: var(--space-4);
  color: var(--color-gray-500);
  font-style: italic;
}

/* Quick Migration */
.quick-migration-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  align-items: flex-start;
}

.template-downloads {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.template-downloads span {
  font-weight: 500;
  color: var(--color-gray-700);
}

/* Validation Results */
.validation-summary {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
}

.validation-status {
  padding: var(--space-3);
  border-radius: 6px;
  font-weight: 600;
  margin-bottom: var(--space-3);
}

.validation-status.valid {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.validation-status.invalid {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.validation-stats {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.validation-warnings {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: var(--space-3);
  border-radius: 4px;
  margin-bottom: var(--space-3);
}

.validation-warnings h4 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--font-size-base);
}

.validation-warnings ul {
  margin: 0;
  padding-left: var(--space-4);
}

.validation-errors {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: var(--space-3);
  border-radius: 4px;
  margin-bottom: var(--space-3);
}

.validation-errors h4 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--font-size-base);
}

.validation-errors ul {
  margin: 0;
  padding-left: var(--space-4);
}

/* Enhanced Button Styles */
.btn.btn-small {
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-sm);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Grid Improvements */
@media (max-width: 768px) {
  .bulk-operations-grid,
  .export-types-grid,
  .migration-sources-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .validation-stats {
    flex-direction: column;
    gap: var(--space-2);
  }

  .template-downloads {
    flex-direction: column;
    align-items: flex-start;
  }

  .quick-migration-actions {
    width: 100%;
  }
}

/* Analytics Components */
.analytics-controls {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--color-white);
  border-radius: 8px;
  border: 1px solid var(--color-gray-200);
}

.control-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.control-group label {
  font-weight: 500;
  color: var(--color-gray-700);
  white-space: nowrap;
}

.control-group .form-control {
  min-width: 150px;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.metric-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
}

.metric-card:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.metric-content h3 {
  margin: 0;
  font-size: var(--font-size-xl);
  color: var(--color-gray-900);
  font-weight: 700;
}

.metric-content p {
  margin: var(--space-1) 0;
  color: var(--color-gray-700);
  font-weight: 500;
}

.metric-content small {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
}

/* Analytics Sections */
.analytics-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-6);
}

.analytics-section {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
}

.analytics-section h3 {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-gray-900);
  font-size: var(--font-size-lg);
  font-weight: 600;
  border-bottom: 1px solid var(--color-gray-200);
  padding-bottom: var(--space-2);
}

/* Usage List */
.usage-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.usage-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  background: var(--color-gray-50);
  border-radius: 4px;
}

.usage-rank {
  background: var(--color-primary);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: 600;
  flex-shrink: 0;
}

.usage-text {
  flex: 1;
  color: var(--color-gray-900);
}

.usage-count {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Category Chart */
.category-chart {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.category-bar {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.category-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
}

.category-progress {
  height: 8px;
  background: var(--color-gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.category-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.3s ease;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--color-gray-50);
  border-radius: 4px;
  border-left: 3px solid var(--color-primary);
}

.activity-time {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  white-space: nowrap;
  min-width: 120px;
}

.activity-content {
  flex: 1;
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.activity-content strong {
  color: var(--color-gray-900);
}

/* Enhanced Quick Action Cards */
.quick-action-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  padding: var(--space-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  min-height: 120px;
  justify-content: center;
}

.quick-action-card:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.quick-action-card.primary {
  background: var(--color-gray-900);
  color: var(--color-white);
  border: none;
}

.quick-action-card.primary:hover {
  background: var(--color-gray-700);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 1.5rem;
  margin-bottom: var(--space-1);
}

.action-label {
  font-weight: 600;
  font-size: var(--font-size-base);
  margin-bottom: var(--space-1);
}

.action-description {
  font-size: var(--font-size-sm);
  opacity: 0.8;
  line-height: 1.3;
}

/* Production-Ready Features */
.feature-badge {
  display: inline-block;
  background: var(--color-primary);
  color: white;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  margin-left: var(--space-1);
}

.feature-badge.new {
  background: #10b981;
}

.feature-badge.beta {
  background: #f59e0b;
}

.feature-badge.essential {
  background: #ef4444;
}

/* Responsive Analytics */
@media (max-width: 768px) {
  .analytics-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .analytics-sections {
    grid-template-columns: 1fr;
  }

  .activity-item {
    flex-direction: column;
    gap: var(--space-2);
  }

  .activity-time {
    min-width: auto;
  }

  .quick-action-card {
    min-height: 100px;
  }
}

@media (max-width: 480px) {
  .admin-header {
    padding: var(--space-3) var(--space-4);
  }

  .header-left {
    gap: var(--space-2);
  }

  .page-title {
    font-size: var(--font-size-lg);
  }

  .admin-content {
    padding: var(--space-2);
  }

  .user-menu-trigger .user-name {
    display: none;
  }

  .quick-actions {
    display: none;
  }
}

/* Admin Page Content Styles - Full Screen */
.admin-page {
  background: var(--color-white);
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.admin-page-header {
  padding: var(--space-6) var(--space-6) var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: var(--color-white);
  margin-bottom: 0;
  flex-shrink: 0;
}

.admin-page-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-2) 0;
}

.admin-page-description {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
  line-height: 1.5;
}

.admin-page-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  margin-top: var(--space-4);
  flex-wrap: wrap;
}

.admin-page-filters {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.admin-page-content {
  padding: var(--space-6);
  flex: 1;
  overflow-y: auto;
  background: var(--color-gray-50);
}

.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.admin-stat-card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 6px;
  padding: var(--space-3);
  text-align: center;
  transition: all var(--transition-fast);
}

.admin-stat-card:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.stat-icon {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-1);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  font-weight: 500;
}

.stat-change {
  font-size: var(--font-size-xs);
  margin-top: var(--space-1);
}

.stat-change.positive {
  color: var(--color-success);
}

.stat-change.negative {
  color: var(--color-error);
}

/* Enhanced Table Styles for Admin */
.admin-table-container {
  background: var(--color-white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background: var(--color-gray-50);
  padding: var(--space-4);
  text-align: left;
  font-weight: 600;
  color: var(--color-gray-900);
  border-bottom: 1px solid var(--color-gray-200);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-gray-100);
  vertical-align: middle;
}

.admin-table tr:hover {
  background: var(--color-gray-50);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.draft {
  background: #fff3cd;
  color: #856404;
}

.status-badge.published {
  background: #d1ecf1;
  color: #0c5460;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid var(--color-gray-300);
  background: var(--color-white);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.btn-icon:hover {
  background: var(--color-gray-100);
  border-color: var(--color-gray-400);
}

.btn-icon.edit {
  color: var(--color-primary);
}

.btn-icon.delete {
  color: var(--color-error);
}

.btn-icon.delete:hover {
  background: #fef2f2;
  border-color: var(--color-error);
}

/* Subject Group Badges */
.group-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: capitalize;
  display: inline-block;
}

.group-badge.core {
  background: #e3f2fd;
  color: #1565c0;
}

.group-badge.science {
  background: #e8f5e8;
  color: #2e7d32;
}

.group-badge.arts {
  background: #fce4ec;
  color: #c2185b;
}

.group-badge.languages {
  background: #f3e5f5;
  color: #7b1fa2;
}

.group-badge.physical {
  background: #fff3e0;
  color: #ef6c00;
}

.group-badge.elective {
  background: #f1f8e9;
  color: #558b2f;
}

/* Compulsory Status */
.compulsory {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  display: inline-block;
}

.compulsory.yes {
  background: #d4edda;
  color: #155724;
}

.compulsory.no {
  background: #f8d7da;
  color: #721c24;
}

/* Filter Controls */
.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filter-group label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-gray-700);
}

.filter-input,
.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-gray-300);
  border-radius: 6px;
  font-size: var(--font-size-sm);
  background: var(--color-white);
  transition: border-color var(--transition-fast);
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  color: var(--color-gray-600);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.btn:focus,
.form-input:focus,
.form-select:focus {
  outline: 2px solid var(--color-black);
  outline-offset: 2px;
}

/* Custom Grade Selector */
.grade-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.grade-option {
  position: relative;
  cursor: pointer;
}

.grade-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  cursor: pointer;
}

.grade-option-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3);
  border: 2px solid var(--color-gray-300);
  border-radius: 8px;
  background-color: var(--color-white);
  color: var(--color-gray-700);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  min-height: 48px;
  user-select: none;
}

.grade-option:hover .grade-option-label {
  border-color: var(--color-gray-400);
  background-color: var(--color-gray-50);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grade-option input[type="radio"]:checked + .grade-option-label {
  border-color: var(--color-black);
  background-color: var(--color-black);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.grade-option input[type="radio"]:focus + .grade-option-label {
  outline: 2px solid var(--color-black);
  outline-offset: 2px;
}

/* Animation for grade selection */
@keyframes gradeSelect {
  0% {
    transform: translateY(-2px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.05);
  }
  100% {
    transform: translateY(-2px) scale(1);
  }
}

.grade-option input[type="radio"]:checked + .grade-option-label {
  animation: gradeSelect 0.3s ease-out;
}

/* Custom Dropdown Styling */
.custom-dropdown {
  position: relative;
  width: 100%;
}

.custom-dropdown-trigger {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  font-size: var(--font-size-sm);
  background-color: var(--color-white);
  color: var(--color-gray-900);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all var(--transition-base);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.custom-dropdown-trigger:hover {
  border-color: var(--color-gray-300);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.custom-dropdown-trigger.open {
  border-color: var(--color-black);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 0 2px rgba(0, 0, 0, 0.1);
  background-color: var(--color-white);
}

.custom-dropdown-arrow {
  transition: transform var(--transition-base);
  font-size: 12px;
  color: var(--color-gray-500);
}

.custom-dropdown-trigger.open .custom-dropdown-arrow {
  transform: rotate(180deg);
}

.custom-dropdown { position: relative; }
.custom-dropdown.open { z-index: 10000; }

.custom-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: 8px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.16), 0 6px 12px rgba(0, 0, 0, 0.10);
  z-index: 10001;
  max-height: 260px;
  overflow-y: auto;
  margin-top: 10px;
  opacity: 0;
  transform: translateY(-8px);
  transition: all var(--transition-base);
  pointer-events: none;
}

.custom-dropdown-menu.open {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.custom-dropdown-option {
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  font-size: var(--font-size-sm);
  color: var(--color-gray-900);
}

.custom-dropdown-option:hover {
  background-color: var(--color-gray-50);
}

.custom-dropdown-option.selected {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
  font-weight: 500;
}

.custom-dropdown-option:first-child {
  border-radius: 8px 8px 0 0;
}

.custom-dropdown-option:last-child {
  border-radius: 0 0 8px 8px;
}

.custom-dropdown-option:only-child {
  border-radius: 8px;
}

/* Fixed Custom Dropdown Error State */
.custom-dropdown-trigger.error {
  border-color: var(--color-error) !important;
  background-color: #fef2f2 !important;
  box-shadow: inset 0 1px 2px rgba(239, 68, 68, 0.1), 0 0 0 2px rgba(239, 68, 68, 0.15);
}

/* Robust Points Checkbox Rendering (no :has, cross-browser) */
.point-checkbox { position: relative; display: flex; align-items: flex-start; }
.point-checkbox input[type="checkbox"] { position: absolute; opacity: 0; width: 20px; height: 20px; margin: 0; z-index: 1; cursor: pointer; }
.point-checkbox .point-text { display: inline; }
.point-checkbox .point-text::before { content: ''; width: 20px; height: 20px; margin-right: 12px; border: 2px solid var(--color-gray-300); border-radius: 4px; background-color: var(--color-white); box-shadow: 0 1px 3px rgba(0,0,0,0.1); flex-shrink: 0; transition: all var(--transition-base); }
.point-checkbox:hover .point-text::before { border-color: var(--color-gray-400); box-shadow: 0 2px 4px rgba(0,0,0,0.15); }
.point-checkbox .point-text::after { content: '✓'; position: absolute; left: 0; top: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; transform: translateY(-50%) scale(0); color: var(--color-white); font-size: 14px; line-height: 1; font-weight: 800; pointer-events: none; transition: transform var(--transition-base); }
.point-checkbox input[type="checkbox"]:checked + .point-text::before { background-color: var(--color-black); border-color: var(--color-black); box-shadow: 0 2px 8px rgba(0,0,0,0.2); }
.point-checkbox input[type="checkbox"]:checked + .point-text::after { transform: translateY(-50%) scale(1); }

/* High Contrast Support */
@media (prefers-contrast: high) {
  :root {
    --color-gray-300: #666666;
    --color-gray-400: #555555;
    --color-gray-500: #444444;
  }
}

/* Version Control Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}
