"""
Pytest configuration and fixtures for the Report Card Backend Service tests.
"""

import os
import tempfile
from pathlib import Path
from typing import Generator

import pytest
from fastapi.testclient import TestClient
from docxtpl import DocxTemplate

from app.main import app
from app.config import Settings, get_settings


@pytest.fixture
def temp_template() -> Generator[str, None, None]:
    """Create a temporary Word template for testing."""
    # Create a minimal .docx template
    with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as tmp_file:
        template_path = tmp_file.name

        # Create a simple template using python-docx first
        from docx import Document
        doc = Document()
        doc.add_paragraph("Student Report Card")
        doc.add_paragraph("Student: {{ student }}")
        doc.add_paragraph("Assessment: {{ assessment }}")
        doc.add_paragraph("Attitude: {{ sections.Attitude_and_Responsibility }}")
        doc.save(template_path)

        yield template_path

        # Cleanup
        os.unlink(template_path)


@pytest.fixture
def test_settings(temp_template: str) -> Settings:
    """Create test settings with temporary template."""
    return Settings(
        template_path=temp_template,
        allowed_origins="http://localhost:3000,http://testserver",
        host="127.0.0.1",
        port=8000,
        log_level="DEBUG",
        output_filename_pattern="test_report_{student}_{timestamp}.docx",
        max_output_size_mb=5,
        enable_image_support=False,
        template_cache=False,
        auth_required=False,
        debug=True,
        log_requests=False,
        enable_profiling=False
    )


@pytest.fixture
def override_settings(test_settings: Settings):
    """Override app settings for testing."""
    app.dependency_overrides[get_settings] = lambda: test_settings
    yield test_settings
    app.dependency_overrides.clear()


@pytest.fixture
def client(override_settings: Settings) -> TestClient:
    """Create a test client with overridden settings."""
    return TestClient(app)


@pytest.fixture
def sample_request_data() -> dict:
    """Sample request data for testing."""
    return {
        "assessment": "1st Term Assessment Profile 2023 - 2024",
        "student": "John Doe",
        "sections": {
            "Attitude_and_Responsibility": "He is a balanced individual and responds appropriately to situations.",
            "Class_Room_Behaviour": "He shows commitment in the classroom to a large extent.",
            "Social_and_Emotional_Behaviour": "He interacts freely with everyone.",
            "Concept": "His concepts are clear and he knows content in detail.",
            "Subject_wise_Feedback": {
                "English_Language": "He comprehends questions well and provides detailed answers.",
                "Mathematics": "He is thorough with concepts and quick with calculations.",
                "Science": "He shows good understanding of scientific concepts."
            }
        },
        "teacher": "Ms. Smith",
        "grade": "Grade 5",
        "school": "Test Elementary School"
    }


@pytest.fixture
def minimal_request_data() -> dict:
    """Minimal valid request data for testing."""
    return {
        "assessment": "Test Assessment",
        "student": "Jane Doe",
        "sections": {
            "Attitude_and_Responsibility": "Good attitude."
        }
    }


@pytest.fixture
def invalid_request_data() -> dict:
    """Invalid request data for testing validation."""
    return {
        "assessment": "",  # Empty assessment
        "student": "",     # Empty student name
        "sections": {}     # Empty sections
    }


@pytest.fixture
def large_request_data() -> dict:
    """Large request data for testing size limits."""
    large_text = "A" * 10000  # 10KB of text
    
    return {
        "assessment": "Large Data Test",
        "student": "Test Student",
        "sections": {
            "Attitude_and_Responsibility": large_text,
            "Class_Room_Behaviour": large_text,
            "Social_and_Emotional_Behaviour": large_text,
            "Concept": large_text,
            "Subject_wise_Feedback": {
                f"Subject_{i}": large_text for i in range(20)
            }
        }
    }
