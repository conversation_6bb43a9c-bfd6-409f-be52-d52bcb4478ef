"""
Tests for security utilities and validation.
"""

import pytest
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>

from app.utils.security import (
    sanitize_filename,
    generate_safe_filename,
    validate_file_size,
    validate_content_type,
    generate_request_id,
    sanitize_log_data
)


class TestFilenameSanitization:
    """Tests for filename sanitization functions."""
    
    def test_sanitize_filename_basic(self):
        """Test basic filename sanitization."""
        result = sanitize_filename("test_file.docx")
        assert result == "test_file.docx"
    
    def test_sanitize_filename_with_spaces(self):
        """Test sanitization of filename with spaces."""
        result = sanitize_filename("test file name.docx")
        assert result == "test_file_name.docx"
    
    def test_sanitize_filename_with_special_chars(self):
        """Test sanitization of filename with special characters."""
        result = sanitize_filename("test@file#name$.docx")
        assert result == "testfilename.docx"
    
    def test_sanitize_filename_with_path_traversal(self):
        """Test sanitization removes path traversal attempts."""
        result = sanitize_filename("../../../etc/passwd")
        assert result == "etcpasswd"
        
        result = sanitize_filename("..\\..\\windows\\system32\\config")
        assert result == "windowssystem32config"
    
    def test_sanitize_filename_too_long(self):
        """Test sanitization of overly long filenames."""
        long_name = "a" * 200 + ".docx"
        result = sanitize_filename(long_name, max_length=50)
        
        assert len(result) <= 50
        assert result.endswith(".docx")
    
    def test_sanitize_filename_empty(self):
        """Test sanitization of empty filename."""
        with pytest.raises(ValueError, match="Filename cannot be empty"):
            sanitize_filename("")
        
        with pytest.raises(ValueError, match="Filename cannot be empty"):
            sanitize_filename("   ")
    
    def test_sanitize_filename_only_special_chars(self):
        """Test sanitization of filename with only special characters."""
        with pytest.raises(ValueError, match="Filename becomes empty"):
            sanitize_filename("@#$%^&*()")
    
    def test_sanitize_filename_hidden_file(self):
        """Test sanitization of hidden files (starting with dot)."""
        result = sanitize_filename(".hidden_file.docx")
        assert result == "file_hidden_file.docx"
    
    def test_generate_safe_filename_basic(self):
        """Test basic safe filename generation."""
        pattern = "report_{student}_{timestamp}.docx"
        result = generate_safe_filename(pattern, student="John Doe")
        
        assert "John_Doe" in result
        assert result.endswith(".docx")
        assert "report_" in result
    
    def test_generate_safe_filename_with_special_chars(self):
        """Test safe filename generation with special characters in variables."""
        pattern = "report_{student}.docx"
        result = generate_safe_filename(pattern, student="John@Doe#123")
        
        assert "JohnDoe123" in result
        assert "@" not in result
        assert "#" not in result
    
    def test_generate_safe_filename_missing_variable(self):
        """Test safe filename generation with missing variable."""
        pattern = "report_{student}_{missing_var}.docx"
        
        with pytest.raises(ValueError, match="Missing variable"):
            generate_safe_filename(pattern, student="John Doe")
    
    def test_generate_safe_filename_with_timestamp(self):
        """Test safe filename generation includes timestamp."""
        pattern = "report_{student}_{timestamp}.docx"
        result = generate_safe_filename(pattern, student="John Doe")
        
        # Should contain a timestamp-like string
        parts = result.split("_")
        assert len(parts) >= 3  # report, student, timestamp
        
        # Timestamp should be numeric
        timestamp_part = parts[-1].replace(".docx", "")
        assert timestamp_part.replace("_", "").isdigit()


class TestFileSizeValidation:
    """Tests for file size validation."""
    
    def test_validate_file_size_within_limit(self):
        """Test file size validation within limits."""
        # Should not raise an exception
        validate_file_size(1000, 2000)
    
    def test_validate_file_size_exceeds_limit(self):
        """Test file size validation when limit is exceeded."""
        with pytest.raises(HTTPException) as exc_info:
            validate_file_size(2000, 1000)
        
        assert exc_info.value.status_code == 413
        assert "too large" in str(exc_info.value.detail).lower()
    
    def test_validate_file_size_exact_limit(self):
        """Test file size validation at exact limit."""
        # Should not raise an exception
        validate_file_size(1000, 1000)


class TestContentTypeValidation:
    """Tests for content type validation."""
    
    def test_validate_content_type_allowed(self):
        """Test content type validation with allowed type."""
        allowed_types = ["application/json", "text/plain"]
        
        # Should not raise an exception
        validate_content_type("application/json", allowed_types)
    
    def test_validate_content_type_not_allowed(self):
        """Test content type validation with disallowed type."""
        allowed_types = ["application/json", "text/plain"]
        
        with pytest.raises(HTTPException) as exc_info:
            validate_content_type("application/xml", allowed_types)
        
        assert exc_info.value.status_code == 400
        assert "not allowed" in str(exc_info.value.detail)


class TestRequestIdGeneration:
    """Tests for request ID generation."""
    
    def test_generate_request_id(self):
        """Test request ID generation."""
        request_id = generate_request_id()
        
        assert isinstance(request_id, str)
        assert len(request_id) == 12
        assert request_id.isalnum()
    
    def test_generate_request_id_uniqueness(self):
        """Test that generated request IDs are unique."""
        ids = [generate_request_id() for _ in range(100)]
        
        # All IDs should be unique
        assert len(set(ids)) == 100


class TestLogDataSanitization:
    """Tests for log data sanitization."""
    
    def test_sanitize_log_data_basic(self):
        """Test basic log data sanitization."""
        data = {
            "user": "john_doe",
            "action": "login",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        result = sanitize_log_data(data)
        
        assert result == data  # No sensitive data, should be unchanged
    
    def test_sanitize_log_data_sensitive_keys(self):
        """Test sanitization of sensitive keys."""
        data = {
            "user": "john_doe",
            "password": "secret123",
            "token": "abc123xyz",
            "api_key": "key_123",
            "authorization": "Bearer token123"
        }
        
        result = sanitize_log_data(data)
        
        assert result["user"] == "john_doe"
        assert result["password"] == "[REDACTED]"
        assert result["token"] == "[REDACTED]"
        assert result["api_key"] == "[REDACTED]"
        assert result["authorization"] == "[REDACTED]"
    
    def test_sanitize_log_data_nested(self):
        """Test sanitization of nested data structures."""
        data = {
            "user_info": {
                "username": "john_doe",
                "password": "secret123"
            },
            "request_data": {
                "action": "update",
                "auth_token": "token123"
            }
        }
        
        result = sanitize_log_data(data)
        
        assert result["user_info"]["username"] == "john_doe"
        assert result["user_info"]["password"] == "[REDACTED]"
        assert result["request_data"]["action"] == "update"
        assert result["request_data"]["auth_token"] == "[REDACTED]"
    
    def test_sanitize_log_data_long_strings(self):
        """Test sanitization of very long strings."""
        long_string = "a" * 2000
        data = {
            "short_field": "normal text",
            "long_field": long_string
        }
        
        result = sanitize_log_data(data)
        
        assert result["short_field"] == "normal text"
        assert len(result["long_field"]) <= 1013  # 1000 + "...[TRUNCATED]"
        assert result["long_field"].endswith("...[TRUNCATED]")
    
    def test_sanitize_log_data_case_insensitive(self):
        """Test that sanitization is case-insensitive for sensitive keys."""
        data = {
            "PASSWORD": "secret123",
            "Token": "abc123xyz",
            "API_KEY": "key_123"
        }
        
        result = sanitize_log_data(data)
        
        assert result["PASSWORD"] == "[REDACTED]"
        assert result["Token"] == "[REDACTED]"
        assert result["API_KEY"] == "[REDACTED]"


class TestInputValidation:
    """Tests for general input validation."""
    
    def test_student_name_validation_valid(self):
        """Test valid student name patterns."""
        from app.models import DocumentGenerationRequest
        
        valid_names = [
            "John Doe",
            "Mary-Jane Smith",
            "O'Connor",
            "Jean-Pierre",
            "Anna Maria",
            "José García"
        ]
        
        for name in valid_names:
            # Should not raise validation error
            request_data = {
                "assessment": "Test Assessment",
                "student": name,
                "sections": {"Attitude_and_Responsibility": "Good"}
            }
            request = DocumentGenerationRequest(**request_data)
            assert request.student == name.strip()
    
    def test_student_name_validation_invalid(self):
        """Test invalid student name patterns."""
        from app.models import DocumentGenerationRequest
        from pydantic import ValidationError
        
        invalid_names = [
            "",           # Empty
            "   ",        # Only spaces
            "John123",    # Numbers
            "John@Doe",   # Special characters
            "John<script>", # HTML/script tags
        ]
        
        for name in invalid_names:
            request_data = {
                "assessment": "Test Assessment",
                "student": name,
                "sections": {"Attitude_and_Responsibility": "Good"}
            }
            
            with pytest.raises(ValidationError):
                DocumentGenerationRequest(**request_data)
