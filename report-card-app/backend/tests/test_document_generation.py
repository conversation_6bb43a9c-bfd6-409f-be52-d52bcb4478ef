"""
Tests for the document generation service.
"""

import io
import zipfile
from unittest.mock import Mock, patch

import pytest
from fastapi import HTT<PERSON>Exception

from app.services.document_service import (
    DocumentGenerationService,
    DocumentGenerationError,
    TemplateNotFoundError,
    TemplateCacheManager
)
from app.models import DocumentGenerationRequest


class TestDocumentGenerationService:
    """Tests for the DocumentGenerationService class."""
    
    @pytest.fixture
    def service(self, test_settings):
        """Create a document generation service for testing."""
        with patch('app.services.document_service.get_settings', return_value=test_settings):
            return DocumentGenerationService()
    
    @pytest.fixture
    def sample_request(self, sample_request_data):
        """Create a sample DocumentGenerationRequest."""
        return DocumentGenerationRequest(**sample_request_data)
    
    async def test_generate_document_success(self, service, sample_request):
        """Test successful document generation."""
        document_stream, filename = await service.generate_document(
            sample_request, "test-request-id"
        )
        
        # Check that we got a stream
        assert isinstance(document_stream, io.BytesIO)
        
        # Check that the stream has content
        content = document_stream.getvalue()
        assert len(content) > 0
        
        # Check that it's a valid ZIP file (DOCX format)
        document_stream.seek(0)
        assert zipfile.is_zipfile(document_stream)
        
        # Check filename
        assert isinstance(filename, str)
        assert filename.endswith('.docx')
        assert 'John_Doe' in filename  # Student name should be in filename
    
    async def test_generate_document_minimal_data(self, service, minimal_request_data):
        """Test document generation with minimal data."""
        request = DocumentGenerationRequest(**minimal_request_data)
        
        document_stream, filename = await service.generate_document(
            request, "test-request-id"
        )
        
        assert isinstance(document_stream, io.BytesIO)
        assert len(document_stream.getvalue()) > 0
        assert filename.endswith('.docx')
    
    def test_prepare_context(self, service, sample_request):
        """Test context preparation for template rendering."""
        context = service._prepare_context(sample_request)
        
        # Check required fields
        assert context["student"] == "John Doe"
        assert context["assessment"] == "1st Term Assessment Profile 2023 - 2024"
        assert "sections" in context
        
        # Check sections
        sections = context["sections"]
        assert "Attitude_and_Responsibility" in sections
        assert "Subject_wise_Feedback" in sections
        
        # Check optional fields
        assert context["teacher"] == "Ms. Smith"
        assert context["grade"] == "Grade 5"
        assert context["school"] == "Test Elementary School"
        
        # Check metadata
        assert "generated_at" in context
        assert "app_name" in context
        assert "app_version" in context
        assert "date" in context
    
    def test_prepare_context_minimal(self, service, minimal_request_data):
        """Test context preparation with minimal data."""
        request = DocumentGenerationRequest(**minimal_request_data)
        context = service._prepare_context(request)
        
        # Check required fields
        assert context["student"] == "Jane Doe"
        assert context["assessment"] == "Test Assessment"
        
        # Check that optional fields are not present or have defaults
        assert "teacher" not in context
        assert "grade" not in context
        assert "school" not in context
        assert "date" in context  # Should have default date
    
    def test_generate_filename(self, service, sample_request):
        """Test filename generation."""
        filename = service._generate_filename(sample_request)
        
        assert isinstance(filename, str)
        assert filename.endswith('.docx')
        assert 'John_Doe' in filename
        
        # Should contain timestamp
        assert len(filename.split('_')) >= 3  # report_name_timestamp.docx
    
    def test_generate_filename_fallback(self, service, sample_request):
        """Test filename generation fallback."""
        # Mock the settings to have an invalid pattern
        with patch.object(service.settings, 'output_filename_pattern', '{invalid_var}'):
            filename = service._generate_filename(sample_request)
            
            # Should fall back to simple filename
            assert filename.startswith('report_')
            assert filename.endswith('.docx')
    
    def test_check_template_health_success(self, service):
        """Test template health check with valid template."""
        health = service.check_template_health()
        
        assert health["template_loaded"] is True
        assert "template_path" in health
        assert "file_size_bytes" in health
        assert "last_modified" in health
    
    def test_check_template_health_missing_template(self, test_settings):
        """Test template health check with missing template."""
        # Create service with non-existent template
        test_settings.template_path = "/nonexistent/template.docx"
        
        with patch('app.services.document_service.get_settings', return_value=test_settings):
            service = DocumentGenerationService()
            health = service.check_template_health()
            
            assert health["template_loaded"] is False
            assert "error" in health


class TestTemplateCacheManager:
    """Tests for the TemplateCacheManager class."""
    
    @pytest.fixture
    def cache_manager(self, test_settings):
        """Create a template cache manager for testing."""
        with patch('app.services.document_service.get_settings', return_value=test_settings):
            return TemplateCacheManager()
    
    def test_get_template_no_cache(self, cache_manager, temp_template):
        """Test getting template without caching."""
        # Ensure caching is disabled
        cache_manager.settings.template_cache = False
        
        template = cache_manager.get_template(temp_template)
        
        # Should return a DocxTemplate instance
        from docxtpl import DocxTemplate
        assert isinstance(template, DocxTemplate)
        
        # Cache should be empty
        assert len(cache_manager._cache) == 0
    
    def test_get_template_with_cache(self, cache_manager, temp_template):
        """Test getting template with caching enabled."""
        # Enable caching
        cache_manager.settings.template_cache = True
        
        # First call should load and cache
        template1 = cache_manager.get_template(temp_template)
        assert len(cache_manager._cache) == 1
        
        # Second call should return cached version
        template2 = cache_manager.get_template(temp_template)
        assert template1 is template2  # Same object
    
    def test_get_template_nonexistent(self, cache_manager):
        """Test getting non-existent template."""
        with pytest.raises(TemplateNotFoundError):
            cache_manager.get_template("/nonexistent/template.docx")
    
    def test_clear_cache(self, cache_manager, temp_template):
        """Test clearing the template cache."""
        # Enable caching and load a template
        cache_manager.settings.template_cache = True
        cache_manager.get_template(temp_template)
        
        assert len(cache_manager._cache) == 1
        
        # Clear cache
        cache_manager.clear_cache()
        
        assert len(cache_manager._cache) == 0
        assert len(cache_manager._last_modified) == 0


class TestDocumentGenerationErrors:
    """Tests for error handling in document generation."""
    
    @pytest.fixture
    def service(self, test_settings):
        """Create a document generation service for testing."""
        with patch('app.services.document_service.get_settings', return_value=test_settings):
            return DocumentGenerationService()
    
    async def test_template_not_found_error(self, test_settings, sample_request_data):
        """Test handling of template not found error."""
        # Set non-existent template path
        test_settings.template_path = "/nonexistent/template.docx"
        
        with patch('app.services.document_service.get_settings', return_value=test_settings):
            service = DocumentGenerationService()
            request = DocumentGenerationRequest(**sample_request_data)
            
            with pytest.raises(HTTPException) as exc_info:
                await service.generate_document(request, "test-request-id")
            
            assert exc_info.value.status_code == 404
            assert "Template not found" in str(exc_info.value.detail)
    
    async def test_document_generation_error(self, service, sample_request_data):
        """Test handling of document generation errors."""
        request = DocumentGenerationRequest(**sample_request_data)
        
        # Mock template rendering to raise an exception
        with patch.object(service.template_cache, 'get_template') as mock_get_template:
            mock_template = Mock()
            mock_template.render.side_effect = Exception("Template rendering failed")
            mock_get_template.return_value = mock_template
            
            with pytest.raises(HTTPException) as exc_info:
                await service.generate_document(request, "test-request-id")
            
            assert exc_info.value.status_code == 500
            assert "Document generation failed" in str(exc_info.value.detail)
    
    async def test_file_size_limit_exceeded(self, service, large_request_data):
        """Test handling of file size limit exceeded."""
        # Set a very small size limit
        service.settings.max_output_size_mb = 0.001  # 1KB limit
        
        request = DocumentGenerationRequest(**large_request_data)
        
        with pytest.raises(HTTPException) as exc_info:
            await service.generate_document(request, "test-request-id")
        
        assert exc_info.value.status_code == 413
        assert "too large" in str(exc_info.value.detail).lower()


class TestDocumentContent:
    """Tests for document content validation."""
    
    @pytest.fixture
    def service(self, test_settings):
        """Create a document generation service for testing."""
        with patch('app.services.document_service.get_settings', return_value=test_settings):
            return DocumentGenerationService()
    
    async def test_document_contains_student_name(self, service, sample_request_data):
        """Test that generated document contains student name."""
        request = DocumentGenerationRequest(**sample_request_data)
        
        document_stream, _ = await service.generate_document(
            request, "test-request-id"
        )
        
        # Extract text from the document to verify content
        # Note: This is a simplified check - in a real test you might
        # want to use python-docx to properly extract and verify content
        content = document_stream.getvalue()
        
        # DOCX files are ZIP archives, so we can check if the student name
        # appears in the raw content (it should be in the document.xml)
        assert b"John Doe" in content or b"John_Doe" in content
    
    async def test_document_contains_assessment(self, service, sample_request_data):
        """Test that generated document contains assessment information."""
        request = DocumentGenerationRequest(**sample_request_data)
        
        document_stream, _ = await service.generate_document(
            request, "test-request-id"
        )
        
        content = document_stream.getvalue()
        
        # Check for assessment text
        assert b"1st Term Assessment" in content
