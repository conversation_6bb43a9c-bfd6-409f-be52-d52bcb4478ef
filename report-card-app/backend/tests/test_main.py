"""
Tests for the main FastAPI application endpoints.
"""

import pytest
from fastapi.testclient import TestClient


class TestHealthEndpoint:
    """Tests for the health check endpoint."""
    
    def test_health_check_success(self, client: TestClient):
        """Test successful health check."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "template_loaded" in data
        assert "version" in data
        assert "timestamp" in data
        assert data["status"] == "ok"
    
    def test_health_check_template_status(self, client: TestClient):
        """Test health check includes template status."""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should indicate template is loaded (using temp template)
        assert data["template_loaded"] is True


class TestRootEndpoint:
    """Tests for the root endpoint."""
    
    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint returns service information."""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "service" in data
        assert "version" in data
        assert "status" in data
        assert data["service"] == "Report Card Backend Service"
        assert data["status"] == "running"


class TestDocumentGenerationEndpoint:
    """Tests for the document generation endpoint."""
    
    def test_generate_document_success(self, client: TestClient, sample_request_data: dict):
        """Test successful document generation."""
        response = client.post("/api/generate-doc", json=sample_request_data)
        
        assert response.status_code == 200
        
        # Check content type
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        
        # Check content disposition header
        assert "attachment" in response.headers["content-disposition"]
        assert ".docx" in response.headers["content-disposition"]
        
        # Check request ID header
        assert "x-request-id" in response.headers
        
        # Check that we got binary content
        assert len(response.content) > 0
        
        # Check that it starts with ZIP header (DOCX files are ZIP archives)
        assert response.content[:2] == b'PK'
    
    def test_generate_document_minimal_data(self, client: TestClient, minimal_request_data: dict):
        """Test document generation with minimal data."""
        response = client.post("/api/generate-doc", json=minimal_request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        assert len(response.content) > 0
    
    def test_generate_document_invalid_data(self, client: TestClient, invalid_request_data: dict):
        """Test document generation with invalid data."""
        response = client.post("/api/generate-doc", json=invalid_request_data)
        
        assert response.status_code == 422  # Validation error
        data = response.json()
        
        assert "error" in data
        assert "details" in data
    
    def test_generate_document_missing_fields(self, client: TestClient):
        """Test document generation with missing required fields."""
        incomplete_data = {
            "student": "Test Student"
            # Missing assessment and sections
        }
        
        response = client.post("/api/generate-doc", json=incomplete_data)
        
        assert response.status_code == 422
        data = response.json()
        
        assert "error" in data
        assert "details" in data
    
    def test_generate_document_empty_json(self, client: TestClient):
        """Test document generation with empty JSON."""
        response = client.post("/api/generate-doc", json={})
        
        assert response.status_code == 422
    
    def test_generate_document_invalid_json(self, client: TestClient):
        """Test document generation with invalid JSON."""
        response = client.post(
            "/api/generate-doc",
            data="invalid json",
            headers={"content-type": "application/json"}
        )
        
        assert response.status_code == 422
    
    def test_generate_document_wrong_content_type(self, client: TestClient, sample_request_data: dict):
        """Test document generation with wrong content type."""
        import json
        
        response = client.post(
            "/api/generate-doc",
            data=json.dumps(sample_request_data),
            headers={"content-type": "text/plain"}
        )
        
        assert response.status_code == 422


class TestCORSHeaders:
    """Tests for CORS configuration."""
    
    def test_cors_preflight_request(self, client: TestClient):
        """Test CORS preflight request."""
        response = client.options(
            "/api/generate-doc",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        
        # Should allow the request
        assert response.status_code in [200, 204]
    
    def test_cors_actual_request(self, client: TestClient, sample_request_data: dict):
        """Test CORS headers on actual request."""
        response = client.post(
            "/api/generate-doc",
            json=sample_request_data,
            headers={"Origin": "http://localhost:3000"}
        )
        
        assert response.status_code == 200
        # CORS headers should be present (handled by FastAPI middleware)


class TestSecurityHeaders:
    """Tests for security headers."""
    
    def test_security_headers_present(self, client: TestClient):
        """Test that security headers are present."""
        response = client.get("/health")
        
        assert response.status_code == 200
        
        # Check for security headers
        assert "x-content-type-options" in response.headers
        assert "x-frame-options" in response.headers
        assert "x-xss-protection" in response.headers
        assert "referrer-policy" in response.headers
        
        assert response.headers["x-content-type-options"] == "nosniff"
        assert response.headers["x-frame-options"] == "DENY"


class TestErrorHandling:
    """Tests for error handling."""
    
    def test_404_error(self, client: TestClient):
        """Test 404 error handling."""
        response = client.get("/nonexistent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        
        assert "error" in data
        assert "timestamp" in data
        assert "request_id" in data
    
    def test_method_not_allowed(self, client: TestClient):
        """Test method not allowed error."""
        response = client.put("/health")
        
        assert response.status_code == 405


class TestRequestLogging:
    """Tests for request logging and tracking."""
    
    def test_request_id_header(self, client: TestClient):
        """Test that request ID is included in response headers."""
        response = client.get("/health")
        
        assert response.status_code == 200
        # Request ID should be added by middleware
        assert "x-request-id" in response.headers
        
        # Should be a non-empty string
        request_id = response.headers["x-request-id"]
        assert isinstance(request_id, str)
        assert len(request_id) > 0
