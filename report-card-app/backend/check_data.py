#!/usr/bin/env python3
"""
Script to check imported data in the database.
"""

import asyncio
from app.database.connection import get_database
from app.database.models import Category, Subject, AssessmentPoint
from sqlalchemy import select, func

async def check_data():
    async for session in get_database():
        try:
            # Count categories
            categories_count = await session.scalar(select(func.count(Category.id)))
            print(f'Categories: {categories_count}')
            
            # Count subjects  
            subjects_count = await session.scalar(select(func.count(Subject.id)))
            print(f'Subjects: {subjects_count}')
            
            # Count assessment points
            points_count = await session.scalar(select(func.count(AssessmentPoint.id)))
            print(f'Assessment Points: {points_count}')
            
            # Show some sample data
            categories = await session.execute(select(Category).limit(5))
            print('\nSample Categories:')
            for cat in categories.scalars():
                print(f'  - {cat.display_name} ({cat.category_type})')
                
            subjects = await session.execute(select(Subject).limit(5))
            print('\nSample Subjects:')
            for subj in subjects.scalars():
                print(f'  - {subj.display_name} (Grades {subj.grade_from}-{subj.grade_to})')
                
        finally:
            break

if __name__ == "__main__":
    asyncio.run(check_data())
