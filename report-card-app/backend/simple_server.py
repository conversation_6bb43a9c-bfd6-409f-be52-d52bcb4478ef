#!/usr/bin/env python3
"""
Simple FastAPI server for testing the Report Card Backend.
This version removes the problematic error handling to get the API docs working.
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.models import DocumentGenerationRequest
from app.services.document_service import DocumentGenerationService
from fastapi.responses import StreamingResponse
import io

app = FastAPI(
    title="Report Card Backend",
    description="Backend service for generating Word documents from JSON data",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://testserver"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize document service
doc_service = DocumentGenerationService()

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {
        "status": "ok",
        "template_loaded": True,
        "version": "1.0.0"
    }

@app.post("/api/generate-doc")
async def generate_document(request: DocumentGenerationRequest):
    """Generate a Word document from JSON data."""
    import uuid
    request_id = str(uuid.uuid4())
    document_stream, filename = await doc_service.generate_document(request, request_id)

    return StreamingResponse(
        document_stream,
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        headers={"Content-Disposition": f'attachment; filename="{filename}"'}
    )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
