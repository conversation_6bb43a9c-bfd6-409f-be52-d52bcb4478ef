#!/usr/bin/env python3
"""
Fix grades and configuration version issues.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.connection import get_database, init_database
from app.database.models import ConfigurationVersion, Grade
from sqlalchemy import select, delete
from datetime import datetime

async def fix_grades():
    """Fix grades and configuration version issues."""

    # Initialize database first
    await init_database()

    async for session in get_database():
        try:
            print("🔧 Fixing grades and configuration version...")
            
            # Check if configuration version exists
            result = await session.execute(
                select(ConfigurationVersion).where(ConfigurationVersion.id == 1)
            )
            config_version = result.scalar_one_or_none()
            
            if not config_version:
                print("📝 Creating default configuration version...")
                config_version = ConfigurationVersion(
                    id=1,
                    version_name="Default",
                    description="Default configuration version",
                    is_current=True,
                    is_published=True,
                    created_by=1,
                    published_at=datetime.utcnow()
                )
                session.add(config_version)
                await session.flush()
                print("✅ Created default configuration version")
            else:
                print("✅ Configuration version already exists")
            
            # Check existing grades
            result = await session.execute(
                select(Grade).where(Grade.version_id == 1)
            )
            existing_grades = result.scalars().all()
            
            if existing_grades:
                print(f"📚 Found {len(existing_grades)} existing grades, clearing them...")
                # Delete existing grades for version 1
                await session.execute(
                    delete(Grade).where(Grade.version_id == 1)
                )
                await session.flush()
            
            # Create standard K-12 grades
            grades_data = [
                {"level": 1, "name": "Grade 1", "code": "1", "age": "5-6 years"},
                {"level": 2, "name": "Grade 2", "code": "2", "age": "6-7 years"},
                {"level": 3, "name": "Grade 3", "code": "3", "age": "7-8 years"},
                {"level": 4, "name": "Grade 4", "code": "4", "age": "8-9 years"},
                {"level": 5, "name": "Grade 5", "code": "5", "age": "9-10 years"},
                {"level": 6, "name": "Grade 6", "code": "6", "age": "10-11 years"},
                {"level": 7, "name": "Grade 7", "code": "7", "age": "11-12 years"},
                {"level": 8, "name": "Grade 8", "code": "8", "age": "12-13 years"},
                {"level": 9, "name": "Grade 9", "code": "9", "age": "13-14 years"},
                {"level": 10, "name": "Grade 10", "code": "10", "age": "14-15 years"},
                {"level": 11, "name": "Grade 11", "code": "11", "age": "15-16 years"},
                {"level": 12, "name": "Grade 12", "code": "12", "age": "16-17 years"},
            ]
            
            print("📚 Creating standard K-12 grades...")
            for i, grade_data in enumerate(grades_data, 1):
                grade = Grade(
                    version_id=1,
                    grade_level=grade_data["level"],
                    grade_name=grade_data["name"],
                    grade_code=grade_data["code"],
                    description="",
                    age_range=grade_data["age"],
                    sort_order=i,
                    is_active=True
                )
                session.add(grade)
            
            await session.commit()
            print(f"✅ Created {len(grades_data)} grades successfully")
            
            # Verify the fix
            result = await session.execute(
                select(Grade).where(Grade.version_id == 1)
            )
            final_grades = result.scalars().all()
            print(f"🔍 Verification: Found {len(final_grades)} grades in database")
            
            for grade in final_grades:
                print(f"  - {grade.grade_name} (Level {grade.grade_level})")
            
            print("🎉 Database fix completed successfully!")
            
        except Exception as e:
            print(f"❌ Error fixing database: {e}")
            await session.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(fix_grades())
