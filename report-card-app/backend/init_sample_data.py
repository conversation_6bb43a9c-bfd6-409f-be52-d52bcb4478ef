#!/usr/bin/env python3
"""
Initialize database with sample data for testing.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def init_sample_data():
    """Initialize database with sample data."""
    try:
        from app.database.connection import init_database, get_database
        from app.database.models import User, ConfigurationVersion
        from app.utils.security import hash_password
        from sqlalchemy import select
        
        print("🔧 Initializing database with sample data...")
        
        # Initialize database
        await init_database()
        print("✅ Database connection established")
        
        async for session in get_database():
            try:
                # Check/create admin user
                result = await session.execute(
                    select(User).where(User.username == "admin")
                )
                admin_user = result.scalar_one_or_none()
                
                if not admin_user:
                    print("👤 Creating admin user...")
                    admin_user = User(
                        username="admin",
                        email="<EMAIL>",
                        hashed_password=hash_password("admin123"),
                        full_name="System Administrator",
                        role="admin",
                        is_active=True
                    )
                    session.add(admin_user)
                    await session.flush()
                    print(f"✅ Created admin user (ID: {admin_user.id})")
                else:
                    print(f"✅ Admin user exists (ID: {admin_user.id})")
                
                # Check existing versions
                result = await session.execute(select(ConfigurationVersion))
                existing_versions = result.scalars().all()
                print(f"📋 Found {len(existing_versions)} existing versions")
                
                # Create sample versions if none exist
                if len(existing_versions) == 0:
                    print("📝 Creating sample configuration versions...")
                    
                    # Initial version
                    version1 = ConfigurationVersion(
                        version_name="Initial Configuration",
                        description="Initial setup with default assessment criteria and school settings",
                        is_published=True,
                        is_current=True,
                        created_by=admin_user.id,
                        created_at=datetime.utcnow(),
                        published_at=datetime.utcnow()
                    )
                    session.add(version1)
                    
                    # Academic year version
                    version2 = ConfigurationVersion(
                        version_name="2024-2025 Academic Year",
                        description="Updated configuration for the 2024-2025 academic year with new assessment standards",
                        is_published=True,
                        is_current=False,
                        created_by=admin_user.id,
                        created_at=datetime.utcnow(),
                        published_at=datetime.utcnow()
                    )
                    session.add(version2)
                    
                    # Draft version
                    version3 = ConfigurationVersion(
                        version_name="Draft - New Grading System",
                        description="Draft configuration implementing competency-based grading system (not yet published)",
                        is_published=False,
                        is_current=False,
                        created_by=admin_user.id,
                        created_at=datetime.utcnow(),
                        published_at=None
                    )
                    session.add(version3)
                    
                    await session.flush()
                    print(f"✅ Created 3 sample versions")
                
                await session.commit()
                
                # Verify final state
                result = await session.execute(select(ConfigurationVersion))
                all_versions = result.scalars().all()
                
                print(f"\n📊 Database Status:")
                print(f"   👤 Admin user: admin/admin123")
                print(f"   📋 Total versions: {len(all_versions)}")
                
                for version in all_versions:
                    status = "🟢 Current" if version.is_current else ("🔵 Published" if version.is_published else "🟡 Draft")
                    print(f"      - {version.version_name} ({status})")
                
                print(f"\n✅ Sample data initialization complete!")
                print(f"🌐 Backend running at: http://localhost:8000")
                print(f"🎯 Frontend running at: http://localhost:5173")
                print(f"📝 Login with: admin / admin123")
                
            except Exception as e:
                print(f"❌ Error during initialization: {e}")
                await session.rollback()
                raise
            finally:
                break
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure you're running this from the backend directory")
    except Exception as e:
        print(f"❌ Initialization error: {e}")

if __name__ == "__main__":
    asyncio.run(init_sample_data())
