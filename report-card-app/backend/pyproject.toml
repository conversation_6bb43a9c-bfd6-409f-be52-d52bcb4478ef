[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "report-card-backend"
version = "1.0.0"
description = "Backend service for generating Word documents from JSON data"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Report Card Team", email = "<EMAIL>"},
]
keywords = ["fastapi", "document-generation", "word", "docx", "report-card"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",

    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Office/Business",
    "Topic :: Text Processing",
]

dependencies = [
    # Core FastAPI dependencies
    "fastapi>=0.115.0,<0.116.0",
    "uvicorn[standard]>=0.32.0,<0.33.0",
    "pydantic>=2.10.0,<2.11.0",
    "pydantic-settings>=2.7.0,<2.8.0",
    
    # Document generation
    "docxtpl>=0.18.0,<0.19.0",
    "python-docx>=1.1.0,<1.2.0",
    
    # HTTP and CORS
    "python-multipart>=0.0.20,<0.1.0",
    "starlette>=0.41.0,<0.42.0",
    "requests>=2.31.0,<3.0.0",
    
    # Environment and configuration
    "python-dotenv>=1.0.0,<1.1.0",
    
    # Logging and monitoring
    "structlog>=24.4.0,<25.0.0",
    "rich>=13.9.0,<14.0.0",
    
    # Security
    "passlib[bcrypt]>=1.7.4,<1.8.0",
    "python-jose[cryptography]>=3.3.0,<3.4.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=8.3.0,<9.0.0",
    "pytest-asyncio>=0.24.0,<0.25.0",
    "pytest-cov>=6.0.0,<7.0.0",
    "httpx>=0.28.0,<0.29.0",
    
    # Code quality
    "black>=24.10.0,<25.0.0",
    "flake8>=7.1.0,<8.0.0",
    "mypy>=1.13.0,<2.0.0",
    "isort>=5.13.0,<6.0.0",
]

production = [
    # Image processing (optional)
    "Pillow>=11.0.0,<12.0.0",
    
    # Production server
    "gunicorn>=23.0.0,<24.0.0",
]

monitoring = [
    # Metrics and monitoring
    "prometheus-client>=0.21.0,<0.22.0",
    "sentry-sdk[fastapi]>=2.18.0,<3.0.0",
]

[project.urls]
Homepage = "https://github.com/reportcard/backend"
Documentation = "https://github.com/reportcard/backend#readme"
Repository = "https://github.com/reportcard/backend.git"
Issues = "https://github.com/reportcard/backend/issues"

[project.scripts]
report-card-server = "app.main:main"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "httpx>=0.28.0",
    "black>=24.10.0",
    "flake8>=7.1.0",
    "mypy>=1.13.0",
    "isort>=5.13.0",
]

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "docxtpl.*",
    "docx.*",
    "passlib.*",
    "jose.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
    "--strict-config",
    "--color=yes",
    "--cov=app",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "security: Security-related tests",
    "document: Document generation tests",
]
asyncio_mode = "auto"
timeout = 300

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
