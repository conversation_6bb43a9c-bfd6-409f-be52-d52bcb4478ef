#!/usr/bin/env python3
"""
<PERSON>ript to run the initial data migration for the Report Card Backend Service.

This script migrates CSV data to the database and creates the initial admin user.
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.migration_utils import run_initial_migration


if __name__ == "__main__":
    print("Running initial data migration...")
    try:
        asyncio.run(run_initial_migration())
        print("Migration completed successfully!")
    except Exception as e:
        print(f"Migration failed: {e}")
        sys.exit(1)
