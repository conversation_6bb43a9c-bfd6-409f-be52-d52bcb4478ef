#!/usr/bin/env python3
"""
Fix database issues - create default version and grades
"""
import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.connection import init_database, get_database
from app.database.models import ConfigurationVersion, Grade, User, Base
from sqlalchemy import select
from datetime import datetime

async def fix_database():
    """Fix database by creating default version and grades."""
    print("Initializing database...")
    await init_database()
    
    async for session in get_database():
        try:
            print("Checking for existing configuration version...")
            
            # Check if we have any configuration version
            version_result = await session.execute(select(ConfigurationVersion))
            version = version_result.scalar_one_or_none()
            
            if not version:
                print("No configuration version found, creating default...")
                
                # Create default version
                version = ConfigurationVersion(
                    version_name="Initial Configuration",
                    description="Default configuration with standard grades",
                    is_published=True,
                    is_current=True,
                    created_by=1,  # Default admin user
                    created_at=datetime.utcnow()
                )
                session.add(version)
                await session.flush()  # Get the ID
                print(f"Created configuration version with ID: {version.id}")
            else:
                print(f"Found existing version: {version.version_name} (ID: {version.id})")
            
            # Check if we have grades for this version
            grades_result = await session.execute(
                select(Grade).where(Grade.version_id == version.id)
            )
            existing_grades = grades_result.scalars().all()
            
            if not existing_grades:
                print("No grades found, creating default grades...")
                
                # Default grades for a typical school system
                default_grades = [
                    {"level": 0, "name": "Kindergarten", "code": "K", "age": "5-6 years", "order": 0},
                    {"level": 1, "name": "Grade 1", "code": "1", "age": "6-7 years", "order": 1},
                    {"level": 2, "name": "Grade 2", "code": "2", "age": "7-8 years", "order": 2},
                    {"level": 3, "name": "Grade 3", "code": "3", "age": "8-9 years", "order": 3},
                    {"level": 4, "name": "Grade 4", "code": "4", "age": "9-10 years", "order": 4},
                    {"level": 5, "name": "Grade 5", "code": "5", "age": "10-11 years", "order": 5},
                    {"level": 6, "name": "Grade 6", "code": "6", "age": "11-12 years", "order": 6},
                    {"level": 7, "name": "Grade 7", "code": "7", "age": "12-13 years", "order": 7},
                    {"level": 8, "name": "Grade 8", "code": "8", "age": "13-14 years", "order": 8},
                    {"level": 9, "name": "Grade 9", "code": "9", "age": "14-15 years", "order": 9},
                    {"level": 10, "name": "Grade 10", "code": "10", "age": "15-16 years", "order": 10},
                    {"level": 11, "name": "Grade 11", "code": "11", "age": "16-17 years", "order": 11},
                    {"level": 12, "name": "Grade 12", "code": "12", "age": "17-18 years", "order": 12},
                ]
                
                # Create grade records
                for grade_data in default_grades:
                    grade = Grade(
                        version_id=version.id,
                        grade_level=grade_data["level"],
                        grade_name=grade_data["name"],
                        grade_code=grade_data["code"],
                        description=f"Standard {grade_data['name']} curriculum",
                        age_range=grade_data["age"],
                        sort_order=grade_data["order"],
                        is_active=True
                    )
                    session.add(grade)
                
                print(f"Created {len(default_grades)} default grades")
            else:
                print(f"Found {len(existing_grades)} existing grades")
            
            # Commit all changes
            await session.commit()
            print("Database fix completed successfully!")
            
            # Verify the fix
            grades_result = await session.execute(
                select(Grade).where(Grade.version_id == version.id)
            )
            all_grades = grades_result.scalars().all()
            print(f"Total grades in database: {len(all_grades)}")
            
            for grade in all_grades[:5]:  # Show first 5 grades
                print(f"  - {grade.grade_name} (Level {grade.grade_level})")
            
            if len(all_grades) > 5:
                print(f"  ... and {len(all_grades) - 5} more")
                
        except Exception as e:
            print(f"Error fixing database: {e}")
            await session.rollback()
            raise
        finally:
            break

if __name__ == "__main__":
    asyncio.run(fix_database())
