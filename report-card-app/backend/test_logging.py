#!/usr/bin/env python3
"""
Test script to demonstrate the beautiful logging system.
Run this to see how the logging looks in action.
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.logging import configure_logging, get_logger


async def test_logging():
    """Test the logging system with various log levels and scenarios."""
    
    # Configure logging
    configure_logging()
    
    # Get loggers for different components
    main_logger = get_logger("main")
    db_logger = get_logger("database")
    api_logger = get_logger("api")
    doc_logger = get_logger("document_generation")
    
    print("\n🧪 Testing Beautiful Logging System")
    print("=" * 50)
    
    # Test different log levels
    main_logger.debug("🔍 Debug message - checking internal state", component="startup", details="verbose debugging info")
    
    main_logger.info("ℹ️  Application starting up", version="1.0.0", environment="development", port=8000)
    
    main_logger.warning("⚠️  Configuration warning", issue="missing optional config", recommendation="add SMTP settings")
    
    main_logger.error("❌ Database connection failed", error="Connection timeout", retry_count=3, max_retries=5)
    
    main_logger.critical("🚨 Critical system failure", component="auth_service", action="shutting_down")
    
    print("\n📊 Component-specific logging:")
    print("-" * 30)
    
    # Database operations
    db_logger.info("🔧 Initializing database connection", driver="postgresql", host="localhost", database="report_card")
    db_logger.info("✅ Database migration completed", tables_created=5, migration_version="v1.2.3")
    db_logger.warning("⚠️  Slow query detected", query="SELECT * FROM students", duration="2.5s", threshold="1.0s")
    
    # API operations
    api_logger.info("🌐 API endpoint called", method="POST", path="/api/v1/reports", user_id="user123", request_id="req-456")
    api_logger.warning("🚫 Rate limit approaching", user_id="user123", requests_count=95, limit=100, window="1min")
    api_logger.error("💥 API validation failed", endpoint="/api/v1/reports", errors=["missing required field: student_id"])
    
    # Document generation
    doc_logger.info("📄 Starting document generation", student="John Doe", template="report_card_v2.docx", request_id="req-789")
    doc_logger.info("✅ Document generated successfully", filename="john_doe_report.docx", size_bytes=45678, duration="1.2s")
    doc_logger.error("❌ Template processing failed", template="report_card_v2.docx", error="Missing placeholder: {student_grade}")
    
    print("\n🎯 Performance and metrics logging:")
    print("-" * 35)
    
    # Performance logging
    perf_logger = get_logger("performance")
    perf_logger.info("⚡ Function performance", function="generate_report", duration="0.85s", memory_usage="12MB", cpu_usage="15%")
    perf_logger.info("📈 Request metrics", endpoint="/health", response_time="0.05s", status_code=200, cache_hit=True)
    
    print("\n🔒 Security and authentication logging:")
    print("-" * 40)
    
    # Security logging
    auth_logger = get_logger("auth")
    auth_logger.info("🔐 User login successful", user_id="teacher123", role="teacher", ip_address="*************")
    auth_logger.warning("⚠️  Failed login attempt", username="admin", ip_address="*************", attempt_count=3)
    auth_logger.error("🚨 Suspicious activity detected", user_id="user456", activity="multiple_failed_logins", ip_address="********")
    
    print("\n✨ Logging test completed!")
    print("=" * 50)
    print("🎨 Features demonstrated:")
    print("  • Beautiful colored console output")
    print("  • Emoji indicators for log levels")
    print("  • Structured key-value pairs")
    print("  • Component-specific loggers")
    print("  • Timestamp formatting")
    print("  • Clean, readable format")
    print("\n💡 In production, logs will be in JSON format for better parsing!")


if __name__ == "__main__":
    asyncio.run(test_logging())
