#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a sample Word template for the Report Card Backend Service.

This script creates a basic .docx template with Jinja2 placeholders
that matches the JSON structure from the React frontend.
"""

import os
import sys
from pathlib import Path

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
except ImportError:
    print("Error: python-docx not found. Installing...")
    os.system("pip install python-docx")
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH


def create_sample_template():
    """Create a sample Word template with Jinja2 placeholders."""
    
    # Create a new document
    doc = Document()
    
    # Set up styles
    styles = doc.styles
    
    # Title
    title = doc.add_heading('STUDENT REPORT CARD', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # School and Assessment info
    doc.add_paragraph()
    school_para = doc.add_paragraph('{{ school }}')
    school_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    assessment_para = doc.add_paragraph('{{ assessment }}')
    assessment_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # Student Information Section
    doc.add_heading('Student Information', level=1)
    
    info_table = doc.add_table(rows=4, cols=2)
    info_table.style = 'Table Grid'
    
    # Student info rows
    info_data = [
        ('Student Name:', '{{ student }}'),
        ('Teacher:', '{% if teacher %}{{ teacher }}{% else %}Not specified{% endif %}'),
        ('Grade:', '{% if grade %}{{ grade }}{% else %}Not specified{% endif %}'),
        ('Date:', '{{ date }}')
    ]
    
    for i, (label, value) in enumerate(info_data):
        row = info_table.rows[i]
        row.cells[0].text = label
        row.cells[1].text = value
    
    doc.add_paragraph()
    
    # Behavioral Assessment Section
    doc.add_heading('Behavioral Assessment', level=1)
    
    # Attitude and Responsibility
    doc.add_heading('Attitude and Responsibility', level=2)
    doc.add_paragraph('{% if sections.Attitude_and_Responsibility %}{{ sections.Attitude_and_Responsibility }}{% else %}No assessment provided.{% endif %}')
    
    # Classroom Behavior
    doc.add_heading('Classroom Behaviour', level=2)
    doc.add_paragraph('{% if sections.Class_Room_Behaviour %}{{ sections.Class_Room_Behaviour }}{% else %}No assessment provided.{% endif %}')
    
    # Social and Emotional Behavior
    doc.add_heading('Social and Emotional Behaviour', level=2)
    doc.add_paragraph('{% if sections.Social_and_Emotional_Behaviour %}{{ sections.Social_and_Emotional_Behaviour }}{% else %}No assessment provided.{% endif %}')
    
    doc.add_paragraph()
    
    # Academic Assessment Section
    doc.add_heading('Academic Assessment', level=1)
    
    # Concept Understanding
    doc.add_heading('Concept Understanding', level=2)
    doc.add_paragraph('{% if sections.Concept %}{{ sections.Concept }}{% else %}No assessment provided.{% endif %}')
    
    # Subject-wise Feedback
    doc.add_heading('Subject-wise Performance', level=2)
    doc.add_paragraph('{% if sections.Subject_wise_Feedback %}')
    doc.add_paragraph('{% for subject, feedback in sections.Subject_wise_Feedback.items() %}')
    doc.add_paragraph('{% if feedback %}')
    
    # Subject feedback template
    subject_para = doc.add_paragraph()
    subject_run = subject_para.add_run('{{ subject.replace("_", " ").title() }}:')
    subject_run.bold = True
    
    doc.add_paragraph('{{ feedback }}')
    doc.add_paragraph()
    doc.add_paragraph('{% endif %}')
    doc.add_paragraph('{% endfor %}')
    doc.add_paragraph('{% else %}')
    doc.add_paragraph('No subject-specific feedback available for this assessment.')
    doc.add_paragraph('{% endif %}')
    
    doc.add_paragraph()
    
    # Summary Section
    doc.add_heading('Assessment Summary', level=1)
    doc.add_paragraph('This report provides a comprehensive assessment of {{ student }}\'s performance during the {{ assessment }} period. The evaluation covers behavioral aspects, concept understanding, and subject-specific performance.')
    
    doc.add_paragraph()
    
    # Footer
    doc.add_paragraph('_' * 80)
    footer_para = doc.add_paragraph('Generated on: {{ generated_at }}')
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    app_para = doc.add_paragraph('{{ app_name }} v{{ app_version }}')
    app_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    return doc


def main():
    """Main function to create and save the template."""
    
    # Ensure templates directory exists
    templates_dir = Path('templates')
    templates_dir.mkdir(exist_ok=True)
    
    # Create the template
    print("Creating sample Word template...")
    doc = create_sample_template()
    
    # Save the template
    template_path = templates_dir / 'report_template.docx'
    doc.save(template_path)
    
    print(f"✅ Template created successfully: {template_path}")
    print(f"📁 File size: {template_path.stat().st_size} bytes")
    
    # Verify the template exists and is readable
    if template_path.exists():
        print("✅ Template file verification passed")
        
        # Show template structure
        print("\n📋 Template Structure:")
        print("   - Title: Student Report Card")
        print("   - Student Information Table")
        print("   - Behavioral Assessment Sections")
        print("   - Academic Assessment Sections")
        print("   - Subject-wise Feedback Loop")
        print("   - Summary and Footer")
        
        print(f"\n🔧 Template Variables Used:")
        variables = [
            "{{ school }}", "{{ assessment }}", "{{ student }}",
            "{{ teacher }}", "{{ grade }}", "{{ date }}",
            "{{ sections.Attitude_and_Responsibility }}",
            "{{ sections.Class_Room_Behaviour }}",
            "{{ sections.Social_and_Emotional_Behaviour }}",
            "{{ sections.Concept }}",
            "{{ sections.Subject_wise_Feedback }}",
            "{{ generated_at }}", "{{ app_name }}", "{{ app_version }}"
        ]
        
        for var in variables:
            print(f"   - {var}")
        
        print(f"\n🚀 Next Steps:")
        print(f"   1. Review the template in Microsoft Word or LibreOffice")
        print(f"   2. Customize the layout and formatting as needed")
        print(f"   3. Test the backend service with: python examples/sample_requests.py")
        print(f"   4. Start the backend: uvicorn app.main:app --reload")
        print(f"   5. Test document generation via the API")
        
    else:
        print("❌ Template file creation failed")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
