import asyncio
from app.database.connection import get_database
from sqlalchemy import text

async def check_data():
    async for session in get_database():
        try:
            # Check grades
            result = await session.execute(text("SELECT COUNT(*) FROM grades"))
            grade_count = result.scalar()
            print(f"Total grades in database: {grade_count}")
            
            # Check versions
            result = await session.execute(text("SELECT COUNT(*) FROM configuration_versions"))
            version_count = result.scalar()
            print(f"Total versions in database: {version_count}")
            
            # Check if there's a version_id = 1
            result = await session.execute(text("SELECT * FROM configuration_versions WHERE id = 1"))
            version_1 = result.fetchone()
            print(f"Version 1 exists: {version_1 is not None}")
            
            # Check grades with version_id = 1
            result = await session.execute(text("SELECT COUNT(*) FROM grades WHERE version_id = 1"))
            grades_v1_count = result.scalar()
            print(f"Grades with version_id=1: {grades_v1_count}")
            
            # List all grades
            result = await session.execute(text("SELECT id, grade_name, grade_level, version_id FROM grades LIMIT 10"))
            grades = result.fetchall()
            print(f"Sample grades: {grades}")
            
        finally:
            break

if __name__ == "__main__":
    asyncio.run(check_data())
