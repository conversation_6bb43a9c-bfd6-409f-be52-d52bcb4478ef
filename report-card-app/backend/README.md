# Report Card Backend API

A comprehensive FastAPI-based backend service for the Report Card application, providing secure authentication, data management, and document generation capabilities.

## 🚀 Features

- **FastAPI Framework**: Modern, fast web framework for building APIs
- **JWT Authentication**: Secure token-based authentication
- **SQLAlchemy ORM**: Database abstraction with async support
- **Document Generation**: Word document creation using templates
- **Role-based Access**: Admin and Teacher role management
- **Data Validation**: Pydantic models for request/response validation
- **Database Migrations**: Alembic for schema management
- **Docker Support**: Ready for containerized deployment
- **Comprehensive Testing**: Unit and integration tests included

## 🏃‍♂️ Quick Start

### Prerequisites

- **Python 3.11+** (recommended)
- **pip** (Python package manager)
- **Virtual environment** (recommended)

### Installation

1. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialize database**
   ```bash
   python run_migration.py
   ```

4. **Start the development server**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### 🌐 Access Points

- **API Server**: http://localhost:8000
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc

### 🔑 Default Account
- Admin user is auto-created on setup: `admin` / `admin`
- **Health Check**: http://localhost:8000/health

## 📁 Project Structure

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application entry point
│   ├── config.py            # Configuration settings
│   ├── models.py            # SQLAlchemy database models
│   ├── database/
│   │   ├── __init__.py
│   │   ├── connection.py    # Database connection setup
│   │   ├── models.py        # Database model definitions
│   │   └── migration_utils.py # Migration utilities
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── auth.py          # Authentication endpoints
│   │   ├── config.py        # Configuration management
│   │   ├── students.py      # Student data endpoints
│   │   ├── classes.py       # Class management
│   │   └── analytics.py     # Analytics endpoints
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py  # Authentication business logic
│   │   ├── config_service.py # Configuration management
│   │   └── document_service.py # Document generation
│   └── utils/
│       ├── __init__.py
│       ├── security.py      # Security utilities
│       └── logging.py       # Logging configuration
├── templates/
│   ├── report_template.docx # Word document template
│   └── template_guide.md    # Template customization guide
├── tests/
│   ├── __init__.py
│   ├── conftest.py          # Test configuration
│   ├── test_main.py         # Main application tests
│   ├── test_security.py     # Security tests
│   └── test_document_generation.py # Document tests
├── alembic/                 # Database migration files
├── nginx/                   # Nginx configuration
├── examples/                # Example usage scripts
├── requirements.txt         # Python dependencies
├── pyproject.toml          # Project configuration
├── pytest.ini             # Test configuration
├── Dockerfile              # Docker container definition
├── docker-compose.yml      # Docker Compose configuration
└── README.md               # This file
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/login` - User login with credentials
- `POST /api/auth/logout` - User logout (invalidate token)
- `GET /api/auth/me` - Get current authenticated user info
- `POST /api/auth/refresh` - Refresh JWT token

### Configuration Management
- `GET /api/config/grades` - Get all grade levels
- `POST /api/config/grades` - Create new grade level
- `PUT /api/config/grades/{id}` - Update grade level
- `DELETE /api/config/grades/{id}` - Delete grade level
- `GET /api/config/subjects` - Get all subjects
- `POST /api/config/subjects` - Create new subject
- `GET /api/config/categories` - Get assessment categories
- `POST /api/config/categories` - Create assessment category
- `GET /api/config/points` - Get assessment points
- `POST /api/config/points` - Create assessment point

### Student Management
- `GET /api/students` - Get all students (paginated)
- `POST /api/students` - Create new student record
- `GET /api/students/{id}` - Get student by ID
- `PUT /api/students/{id}` - Update student information
- `DELETE /api/students/{id}` - Delete student record
- `GET /api/students/search` - Search students by criteria

### Class Management
- `GET /api/classes` - Get all classes
- `POST /api/classes` - Create new class
- `GET /api/classes/{id}/students` - Get students in class
- `POST /api/classes/{id}/students` - Add student to class

### Document Generation
- `POST /api/documents/generate` - Generate report card document
- `GET /api/documents/templates` - Get available templates
- `POST /api/documents/preview` - Preview document before generation

### Analytics & Reporting
- `GET /api/analytics/dashboard` - Get dashboard statistics
- `GET /api/analytics/performance` - Get performance metrics
- `GET /api/analytics/usage` - Get usage statistics

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./report_card.db
# For PostgreSQL: postgresql+asyncpg://user:password@localhost/report_card

# Security Settings
JWT_SECRET_KEY=your-secret-key-change-in-production-use-openssl-rand-hex-32
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Server Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
WORKERS=1

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080

# Document Generation
TEMPLATE_PATH=templates/report_template.docx
OUTPUT_PATH=outputs/

# Optional: Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Optional: File Upload
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=.docx,.pdf,.jpg,.png

# Optional: Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds
```

### Database Configuration

#### SQLite (Development)
```env
DATABASE_URL=sqlite+aiosqlite:///./report_card.db
```

#### PostgreSQL (Production)
```env
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/report_card
```

#### MySQL (Alternative)
```env
DATABASE_URL=mysql+aiomysql://username:password@localhost:3306/report_card
```

## 🧪 Development

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app tests/

# Run specific test file
pytest tests/test_document_generation.py

# Run tests with verbose output
pytest -v

# Run tests and generate HTML coverage report
pytest --cov=app --cov-report=html tests/
```

### Database Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Downgrade migration
alembic downgrade -1

# View migration history
alembic history

# View current revision
alembic current
```

### Code Quality

```bash
# Format code
black app/ tests/

# Sort imports
isort app/ tests/

# Lint code
flake8 app/ tests/

# Type checking
mypy app/

# Run all quality checks
black app/ tests/ && isort app/ tests/ && flake8 app/ tests/ && mypy app/
```

## 📄 Document Generation

The backend uses `python-docx` and `docxtpl` for generating Word documents from templates.

### Template Structure

Templates are located in the `templates/` directory and use Jinja2 syntax for variable substitution.

Example template variables:
- `{{ student_name }}` - Student's full name
- `{{ grade }}` - Student's grade level
- `{{ subjects }}` - List of subject assessments
- `{{ behavior_points }}` - Behavioral assessment points

### Customizing Templates

1. Edit the Word template file in `templates/report_template.docx`
2. Use Jinja2 syntax for dynamic content
3. Test template generation with sample data

### Template Syntax Examples

```jinja2
# Variables
{{ student_name }}
{{ grade_level }}

# Conditionals
{% if student.has_special_needs %}
Special accommodations: {{ student.accommodations }}
{% endif %}

# Loops
{% for subject in subjects %}
{{ subject.name }}: {{ subject.grade }}
{% endfor %}

# Filters
{{ student_name|upper }}
{{ assessment_date|strftime('%B %d, %Y') }}
```

## 🔐 Security

### Authentication Flow

1. User submits credentials to `/api/auth/login`
2. Server validates credentials and returns JWT token
3. Client includes token in `Authorization: Bearer <token>` header
4. Server validates token on protected endpoints

### Role-based Access

- **Admin**: Full access to all endpoints
- **Teacher**: Limited access to student and document endpoints

### Security Headers

The application includes security headers:
- CORS protection
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Rate limiting

## 🚀 Deployment

### Docker Deployment

```bash
# Build image
docker build -t report-card-backend .

# Run container
docker run -p 8000:8000 report-card-backend

# Using Docker Compose
docker-compose up --build
```

### Production Deployment

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set environment variables**
   ```bash
   export DATABASE_URL=postgresql://...
   export JWT_SECRET_KEY=secure-random-key
   ```

3. **Run migrations**
   ```bash
   alembic upgrade head
   ```

4. **Start with Gunicorn**
   ```bash
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

## 📊 Monitoring

### Health Check

The application provides a health check endpoint:
- `GET /health` - Returns application status

### Logging

Logs are configured using Python's logging module:
- Application logs: `INFO` level and above
- Access logs: All HTTP requests
- Error logs: Exceptions and errors

### Metrics

For production monitoring, consider integrating:
- Prometheus metrics
- Application Performance Monitoring (APM)
- Error tracking (e.g., Sentry)

## 🔧 Troubleshooting

### Common Issues

**Database connection errors:**
- Verify DATABASE_URL is correct
- Ensure database server is running
- Check database permissions

**Authentication failures:**
- Verify JWT_SECRET_KEY is set
- Check token expiration
- Ensure CORS is configured correctly

**Document generation errors:**
- Verify template file exists
- Check template syntax
- Ensure required data is provided

**Import errors:**
- Activate virtual environment
- Install all dependencies: `pip install -r requirements.txt`

**Greenlet missing error:**
- Symptom: `ValueError: the greenlet library is required to use this function. No module named 'greenlet'`
- Fix:
  1) Ensure your virtual environment is activated: `source venv/bin/activate`
  2) Install backend dependencies: `pip install -r requirements.txt`
  3) If still failing, explicitly install greenlet: `pip install greenlet`

**bcrypt/passlib compatibility:**
- Symptom: `module 'bcrypt' has no attribute '__about__'` or `password cannot be longer than 72 bytes`
- Fix: ensure bcrypt<4 is installed. This repo pins `bcrypt==3.2.2` in requirements.txt. Reinstall deps: `pip install -r requirements.txt`

- Check Python version compatibility

### Debug Mode

Enable debug mode for development:

```bash
export LOG_LEVEL=DEBUG
uvicorn app.main:app --reload --log-level debug
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Style

- Follow PEP 8 guidelines
- Use type hints
- Write docstrings for functions and classes
- Add unit tests for new features

## 📄 License

This project is licensed under the MIT License.
