#!/usr/bin/env python3
"""
Initialize database with admin user and initial configuration version.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database.connection import init_database, get_database
from app.database.models import User, ConfigurationVersion
from app.utils.security import hash_password
from sqlalchemy import select

async def create_admin_and_version():
    """Create admin user and initial configuration version."""
    print("Initializing database...")
    await init_database()
    
    async for session in get_database():
        try:
            # Check if admin user exists
            result = await session.execute(
                select(User).where(User.username == "admin")
            )
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                print("Creating admin user...")
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    hashed_password=hash_password("admin123"),
                    full_name="System Administrator",
                    role="admin",
                    is_active=True
                )
                session.add(admin_user)
                await session.flush()  # Get the ID
                print(f"Created admin user with ID: {admin_user.id}")
            else:
                print(f"Admin user already exists with ID: {admin_user.id}")
            
            # Check if initial version exists
            result = await session.execute(
                select(ConfigurationVersion).where(
                    ConfigurationVersion.version_name == "Initial Configuration"
                )
            )
            version = result.scalar_one_or_none()
            
            if not version:
                print("Creating initial configuration version...")
                version = ConfigurationVersion(
                    version_name="Initial Configuration",
                    description="Initial configuration with default settings",
                    is_published=True,
                    is_current=True,
                    created_by=admin_user.id,
                    created_at=datetime.utcnow(),
                    published_at=datetime.utcnow()
                )
                session.add(version)
                await session.flush()
                print(f"Created initial version with ID: {version.id}")
            else:
                print(f"Initial version already exists with ID: {version.id}")
            
            await session.commit()
            print("Database initialization completed successfully!")
            
        except Exception as e:
            print(f"Error during initialization: {e}")
            await session.rollback()
            raise
        finally:
            break  # Exit the async generator

if __name__ == "__main__":
    asyncio.run(create_admin_and_version())
