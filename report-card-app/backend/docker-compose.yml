version: '3.8'

services:
  # Report Card Backend Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: report-card-backend
    ports:
      - "8000:8000"
    environment:
      # Core configuration
      - TEMPLATE_PATH=templates/report_template.docx
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - OUTPUT_FILENAME_PATTERN=report_{student}_{timestamp}.docx
      - MAX_OUTPUT_SIZE_MB=10
      
      # Optional configuration
      - ENABLE_IMAGE_SUPPORT=false
      - TEMPLATE_CACHE=false
      - AUTH_REQUIRED=false
      - MAX_REQUEST_SIZE_MB=5
      - TEMPLATE_RELOAD_INTERVAL=0
      
      # Development settings
      - DEBUG=true
      - LOG_REQUESTS=true
      - ENABLE_PROFILING=false
      
      # Application metadata
      - APP_NAME=report-card-backend
      - APP_VERSION=1.0.0
      - ENVIRONMENT=development
    volumes:
      # Mount templates directory for easy editing during development
      - ./templates:/app/templates:ro
      # Mount logs directory for persistent logging
      - ./logs:/app/logs
      # Optional: Mount app directory for development (uncomment for hot reload)
      # - ./app:/app/app:ro
    networks:
      - report-card-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development database (optional - for future features)
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: report-card-db
  #   environment:
  #     - POSTGRES_DB=reportcard
  #     - POSTGRES_USER=reportcard
  #     - POSTGRES_PASSWORD=reportcard_dev_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   networks:
  #     - report-card-network
  #   restart: unless-stopped

  # Redis cache (optional - for future features)
  # redis:
  #   image: redis:7-alpine
  #   container_name: report-card-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - report-card-network
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes

  # Nginx reverse proxy (for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: report-card-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      # SSL certificates (if using HTTPS)
      # - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - report-card-network
    restart: unless-stopped

networks:
  report-card-network:
    driver: bridge

volumes:
  # Uncomment if using optional services
  # postgres_data:
  # redis_data:
  logs:
