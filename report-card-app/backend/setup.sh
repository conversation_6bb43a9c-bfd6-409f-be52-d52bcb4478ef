#!/bin/bash

# Report Card Backend Setup Script
# Sets up the Python FastAPI backend environment

set -e  # Exit on any error

echo "🐍 Report Card Backend Setup"
echo "============================"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Check if we're in the backend directory
if [ ! -f "requirements.txt" ] || [ ! -f "app/main.py" ]; then
    print_error "Please run this script from the backend directory"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_step "Checking prerequisites..."

# Check Python
if command_exists python3; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    print_success "Python found: $PYTHON_VERSION ✓"
    PYTHON_CMD="python3"
elif command_exists python; then
    PYTHON_VERSION=$(python --version | cut -d' ' -f2)
    print_success "Python found: $PYTHON_VERSION"
    PYTHON_CMD="python"
else
    print_error "Python 3 is required but not found. Please install Python 3.9 or higher."
    exit 1
fi

# Check pip
if command_exists pip; then
    PIP_VERSION=$(pip --version | cut -d' ' -f2)
    print_success "pip found: $PIP_VERSION ✓"
else
    print_error "pip is required but not found. Please install pip."
    exit 1
fi

print_success "All prerequisites satisfied!"
echo

# Create virtual environment
print_step "Setting up Python virtual environment..."

if [ ! -d "venv" ]; then
    print_status "Creating virtual environment..."
    $PYTHON_CMD -m venv venv
    print_success "Virtual environment created ✓"
else
    print_warning "Virtual environment already exists - skipping creation"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip --quiet
print_success "pip upgraded ✓"

# Install dependencies
print_step "Installing Python dependencies..."
print_status "Installing requirements from requirements.txt..."
pip install -r requirements.txt --quiet
print_success "Dependencies installed ✓"

# Create .env file if it doesn't exist
print_step "Setting up environment configuration..."

if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    
    # Generate a secure secret key
    SECRET_KEY=$(openssl rand -hex 32 2>/dev/null || echo "fallback-secret-key-$(date +%s)")
    
    cat > .env << EOF
# ===================================
# BACKEND ENVIRONMENT CONFIGURATION
# ===================================

# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./report_card.db
# For PostgreSQL: postgresql+asyncpg://user:password@localhost/report_card

# Security Settings
JWT_SECRET_KEY=$SECRET_KEY
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Server Configuration
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
WORKERS=1

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080

# Document Generation
TEMPLATE_PATH=templates/report_template.docx
OUTPUT_PATH=outputs/

# Optional: Email Configuration (uncomment and configure if needed)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Optional: File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=.docx,.pdf,.jpg,.png

# Optional: Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Development Settings
DEBUG=false
ENABLE_PROFILING=false
ENABLE_METRICS=false
LOG_REQUESTS=false

# Environment
ENVIRONMENT=development
EOF
    print_success ".env file created ✓"
else
    print_warning ".env file already exists - skipping creation"
fi

# Initialize database
print_step "Setting up database..."

if [ -f "run_migration.py" ]; then
    print_status "Running database migrations..."
    python run_migration.py
    print_success "Database initialized ✓"
else
    print_warning "Migration script not found - skipping database initialization"
fi

# Run tests if available
print_step "Running tests..."

if [ -f "pytest.ini" ] && command_exists pytest; then
    print_status "Running test suite..."
    if pytest --quiet; then
        print_success "All tests passed ✓"
    else
        print_warning "Some tests failed - check logs for details"
    fi
else
    print_warning "pytest not found or not configured - skipping tests"
fi

# Create output directories
print_step "Creating output directories..."

mkdir -p outputs
mkdir -p logs
print_success "Output directories created ✓"

# Deactivate virtual environment
deactivate

# Create convenience scripts
print_step "Creating convenience scripts..."

# Create start script
cat > start.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Report Card Backend"
echo "==============================="

# Activate virtual environment
source venv/bin/activate

# Start the server
echo "Starting FastAPI server..."
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Deactivate when done
deactivate
EOF

chmod +x start.sh
print_success "Start script created ✓"

# Create production start script
cat > start-prod.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Report Card Backend (Production)"
echo "============================================"

# Activate virtual environment
source venv/bin/activate

# Start with Gunicorn for production
echo "Starting production server with Gunicorn..."
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Deactivate when done
deactivate
EOF

chmod +x start-prod.sh
print_success "Production start script created ✓"

# Create test script
cat > test.sh << 'EOF'
#!/bin/bash
echo "🧪 Running Backend Tests"
echo "========================"

# Activate virtual environment
source venv/bin/activate

# Run tests with coverage
echo "Running test suite..."
pytest --cov=app --cov-report=html --cov-report=term

echo ""
echo "Test results:"
echo "- Terminal output above"
echo "- HTML coverage report: htmlcov/index.html"

# Deactivate when done
deactivate
EOF

chmod +x test.sh
print_success "Test script created ✓"

print_success "Convenience scripts created! ✓"
echo

# Final summary
echo "🎉 Backend setup completed successfully!"
echo "======================================="
echo
print_status "Quick Start Commands:"
echo "  Development: ./start.sh"
echo "  Production:  ./start-prod.sh"
echo "  Run Tests:   ./test.sh"
echo
print_status "Manual Commands:"
echo "  Activate env: source venv/bin/activate"
echo "  Start server: uvicorn app.main:app --reload"
echo "  Run tests:    pytest"
echo "  Deactivate:   deactivate"
echo
print_status "Access Points:"
echo "  🔧 API Server:  http://localhost:8000"
echo "  📚 API Docs:    http://localhost:8000/docs"
echo "  🔍 ReDoc:       http://localhost:8000/redoc"
echo "  ❤️  Health:     http://localhost:8000/health"
echo
print_status "Configuration:"
echo "  📄 Environment: .env"
echo "  📊 Database:    report_card.db (SQLite)"
echo "  📁 Templates:   templates/"
echo "  📤 Outputs:     outputs/"
echo "  📝 Logs:        logs/"
echo
print_status "Next Steps:"
echo "  1. Review and customize .env file if needed"
echo "  2. Start the server: ./start.sh"
echo "  3. Test the API: http://localhost:8000/docs"
echo "  4. Check the main README.md for full documentation"
echo
print_success "Happy coding! 🚀"
