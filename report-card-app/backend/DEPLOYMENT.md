# Deployment Guide

This guide covers various deployment options for the Report Card Backend Service.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development](#local-development)
3. [Docker Deployment](#docker-deployment)
4. [Production Deployment](#production-deployment)
5. [Nginx Configuration](#nginx-configuration)
6. [Systemd Service](#systemd-service)
7. [Environment Variables](#environment-variables)
8. [Monitoring & Logging](#monitoring--logging)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

- Python 3.8+ (for non-Docker deployments)
- Docker and Docker Compose (for containerized deployments)
- Nginx (for reverse proxy setup)
- Word template file (.docx format)

## Local Development

### 1. Setup Virtual Environment

```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 4. Create Template

Create a Word template at `templates/report_template.docx` following the [template guide](templates/template_guide.md).

### 5. Run Development Server

```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The service will be available at `http://localhost:8000`.

## Docker Deployment

### Development with Docker Compose

```bash
# Build and start services
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f backend

# Stop services
docker-compose down
```

### Production Docker Build

```bash
# Build production image
docker build -t report-card-backend:latest .

# Run container
docker run -d \
  --name report-card-backend \
  -p 8000:8000 \
  --env-file .env \
  -v $(pwd)/templates:/app/templates:ro \
  -v $(pwd)/logs:/app/logs \
  report-card-backend:latest
```

## Production Deployment

### Option 1: Docker + Nginx (Recommended)

1. **Prepare environment files:**

```bash
# Production environment
cp .env.example .env.production
# Edit .env.production with production values
```

2. **Create production docker-compose:**

```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  backend:
    build: .
    container_name: report-card-backend-prod
    restart: unless-stopped
    env_file: .env.production
    volumes:
      - ./templates:/app/templates:ro
      - ./logs:/app/logs
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: report-card-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates
    depends_on:
      - backend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

3. **Deploy:**

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Option 2: Systemd Service

1. **Install application:**

```bash
# Create application directory
sudo mkdir -p /opt/report-card-backend
sudo chown $USER:$USER /opt/report-card-backend

# Copy application files
cp -r . /opt/report-card-backend/
cd /opt/report-card-backend

# Setup virtual environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

2. **Create systemd service file:**

```ini
# /etc/systemd/system/report-card-backend.service
[Unit]
Description=Report Card Backend Service
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/report-card-backend
Environment=PATH=/opt/report-card-backend/venv/bin
EnvironmentFile=/opt/report-card-backend/.env
ExecStart=/opt/report-card-backend/venv/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

3. **Enable and start service:**

```bash
sudo systemctl daemon-reload
sudo systemctl enable report-card-backend
sudo systemctl start report-card-backend
sudo systemctl status report-card-backend
```

## Nginx Configuration

### Basic Reverse Proxy

```nginx
# /etc/nginx/sites-available/report-card-backend
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /health {
        proxy_pass http://localhost:8000/health;
        proxy_set_header Host $host;
    }
}
```

### SSL Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Your location blocks here...
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### Rate Limiting

```nginx
# Add to http block
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;

# Add to location block
location /api/ {
    limit_req zone=api burst=20 nodelay;
    # ... other proxy settings
}
```

## Environment Variables

### Production Environment Example

```env
# Required
TEMPLATE_PATH=/app/templates/report_template.docx
ALLOWED_ORIGINS=https://your-frontend-domain.com
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
OUTPUT_FILENAME_PATTERN=report_{student}_{timestamp}.docx
MAX_OUTPUT_SIZE_MB=10

# Security
AUTH_REQUIRED=true
JWT_SECRET_KEY=your-super-secret-jwt-key-here
TRUSTED_HOSTS=your-domain.com,www.your-domain.com
FORCE_HTTPS=true

# Performance
TEMPLATE_CACHE=true
WORKERS=4
MAX_REQUESTS=1000

# Monitoring
ENABLE_METRICS=true
SENTRY_DSN=https://your-sentry-dsn-here
LOG_REQUESTS=true

# Environment
ENVIRONMENT=production
DEBUG=false
```

## Monitoring & Logging

### Log Management

1. **Configure log rotation:**

```bash
# /etc/logrotate.d/report-card-backend
/opt/report-card-backend/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload report-card-backend
    endscript
}
```

2. **Centralized logging with rsyslog:**

```bash
# /etc/rsyslog.d/50-report-card.conf
$ModLoad imfile
$InputFileName /opt/report-card-backend/logs/app.log
$InputFileTag report-card:
$InputFileStateFile stat-report-card
$InputFileSeverity info
$InputFileFacility local7
$InputRunFileMonitor

local7.*    @@your-log-server:514
```

### Health Monitoring

1. **Basic health check script:**

```bash
#!/bin/bash
# /usr/local/bin/check-report-card-health.sh

HEALTH_URL="http://localhost:8000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

2. **Cron job for monitoring:**

```bash
# Add to crontab
*/5 * * * * /usr/local/bin/check-report-card-health.sh || systemctl restart report-card-backend
```

### Prometheus Metrics (Optional)

If you enable metrics (`ENABLE_METRICS=true`), you can scrape them with Prometheus:

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'report-card-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

## Troubleshooting

### Common Issues

1. **Template not found:**
   - Check `TEMPLATE_PATH` environment variable
   - Ensure template file exists and is readable
   - Verify file permissions

2. **CORS errors:**
   - Check `ALLOWED_ORIGINS` configuration
   - Ensure frontend URL is included
   - Verify protocol (http vs https)

3. **Large file errors:**
   - Adjust `MAX_OUTPUT_SIZE_MB` setting
   - Check nginx `client_max_body_size`
   - Monitor disk space

4. **Performance issues:**
   - Enable template caching (`TEMPLATE_CACHE=true`)
   - Increase worker count (`WORKERS`)
   - Add nginx caching for static content

### Debug Mode

Enable debug mode for troubleshooting:

```env
DEBUG=true
LOG_LEVEL=DEBUG
LOG_REQUESTS=true
```

### Log Analysis

```bash
# View recent logs
tail -f /opt/report-card-backend/logs/app.log

# Search for errors
grep -i error /opt/report-card-backend/logs/app.log

# Monitor requests
grep "Request completed" /opt/report-card-backend/logs/app.log | tail -20
```

### Performance Monitoring

```bash
# Monitor resource usage
htop
iostat -x 1
df -h

# Check service status
systemctl status report-card-backend
journalctl -u report-card-backend -f
```

## Security Checklist

- [ ] Use HTTPS in production
- [ ] Configure proper CORS origins
- [ ] Enable authentication if required
- [ ] Set up rate limiting
- [ ] Use strong JWT secret keys
- [ ] Configure trusted hosts
- [ ] Enable security headers
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity
- [ ] Backup templates and configuration

## Backup Strategy

1. **Application files:**
   - Templates directory
   - Configuration files
   - SSL certificates

2. **Logs:**
   - Application logs
   - Access logs
   - Error logs

3. **Automated backup script:**

```bash
#!/bin/bash
# backup-report-card.sh

BACKUP_DIR="/backup/report-card/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup templates
cp -r /opt/report-card-backend/templates $BACKUP_DIR/

# Backup configuration
cp /opt/report-card-backend/.env $BACKUP_DIR/

# Backup logs (last 7 days)
find /opt/report-card-backend/logs -name "*.log" -mtime -7 -exec cp {} $BACKUP_DIR/ \;

# Compress backup
tar -czf $BACKUP_DIR.tar.gz -C /backup/report-card $(basename $BACKUP_DIR)
rm -rf $BACKUP_DIR

echo "Backup completed: $BACKUP_DIR.tar.gz"
```
