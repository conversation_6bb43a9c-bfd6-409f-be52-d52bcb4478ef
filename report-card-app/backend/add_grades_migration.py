#!/usr/bin/env python3
"""
Migration script to add Grade table and populate with default grades.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import Base, init_database, engine as db_engine
from app.database.models import Grade, ConfigurationVersion, User
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy import select
from datetime import datetime
from app.config import get_settings


async def run_migration():
    """Run the migration to add grades."""
    # Initialize database first
    await init_database()

    # Get the engine from the global variable
    from app.database.connection import engine

    if engine is None:
        print("Engine is None, database initialization failed")
        return

    try:
        # Create all tables (this will create the Grade table)
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # Add default grades
        async with AsyncSession(engine) as session:
            # Check if grades already exist
            existing_grades = await session.execute(select(Grade))
            if existing_grades.scalars().first():
                print("Grades already exist, skipping default grade creation.")
                return
            
            # Get or create default version
            version_result = await session.execute(
                select(ConfigurationVersion).where(ConfigurationVersion.is_current == True)
            )
            version = version_result.scalar_one_or_none()
            
            if not version:
                # Get admin user
                admin_result = await session.execute(
                    select(User).where(User.role == "admin")
                )
                admin_user = admin_result.scalar_one_or_none()
                
                if not admin_user:
                    print("No admin user found, creating default version with user_id=1")
                    admin_user_id = 1
                else:
                    admin_user_id = admin_user.id
                
                # Create default version
                version = ConfigurationVersion(
                    version_name="Initial Configuration",
                    description="Default configuration with standard grades",
                    is_published=True,
                    is_current=True,
                    created_by=admin_user_id
                )
                session.add(version)
                await session.flush()  # Get the ID
            
            # Default grades for a typical school system
            default_grades = [
                {"level": 0, "name": "Kindergarten", "code": "K", "age": "5-6 years", "order": 0},
                {"level": 1, "name": "Grade 1", "code": "1", "age": "6-7 years", "order": 1},
                {"level": 2, "name": "Grade 2", "code": "2", "age": "7-8 years", "order": 2},
                {"level": 3, "name": "Grade 3", "code": "3", "age": "8-9 years", "order": 3},
                {"level": 4, "name": "Grade 4", "code": "4", "age": "9-10 years", "order": 4},
                {"level": 5, "name": "Grade 5", "code": "5", "age": "10-11 years", "order": 5},
                {"level": 6, "name": "Grade 6", "code": "6", "age": "11-12 years", "order": 6},
                {"level": 7, "name": "Grade 7", "code": "7", "age": "12-13 years", "order": 7},
                {"level": 8, "name": "Grade 8", "code": "8", "age": "13-14 years", "order": 8},
                {"level": 9, "name": "Grade 9", "code": "9", "age": "14-15 years", "order": 9},
                {"level": 10, "name": "Grade 10", "code": "10", "age": "15-16 years", "order": 10},
                {"level": 11, "name": "Grade 11", "code": "11", "age": "16-17 years", "order": 11},
                {"level": 12, "name": "Grade 12", "code": "12", "age": "17-18 years", "order": 12},
            ]
            
            # Create grade records
            for grade_data in default_grades:
                grade = Grade(
                    version_id=version.id,
                    grade_level=grade_data["level"],
                    grade_name=grade_data["name"],
                    grade_code=grade_data["code"],
                    description=f"Standard {grade_data['name']} curriculum",
                    age_range=grade_data["age"],
                    sort_order=grade_data["order"],
                    is_active=True
                )
                session.add(grade)
            
            await session.commit()
            print(f"Successfully created {len(default_grades)} default grades.")
    
    except Exception as e:
        print(f"Migration failed: {e}")
        raise
    finally:
        if engine:
            await engine.dispose()


if __name__ == "__main__":
    asyncio.run(run_migration())
