#!/usr/bin/env python3
"""
Integration test script for the Report Card Backend Service.
Tests the complete end-to-end functionality.
"""

import json
import requests
import time
import sys
from pathlib import Path

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_OUTPUT_DIR = Path("test_outputs")

def test_health_endpoint():
    """Test the health check endpoint."""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        response.raise_for_status()
        
        health_data = response.json()
        print(f"✅ Health check passed: {health_data}")
        
        assert health_data["status"] == "ok"
        assert health_data["template_loaded"] is True
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_document_generation():
    """Test document generation with sample data."""
    print("📄 Testing document generation...")
    
    # Sample request data
    test_data = {
        "school": "Integration Test School",
        "assessment": "Q1 2024 Integration Test",
        "student": "<PERSON>",
        "teacher": "<PERSON><PERSON> <PERSON>",
        "grade": "6th Grade",
        "date": "2024-09-20",
        "sections": {
            "Attitude_and_Responsibility": "Shows excellent attitude and takes full responsibility for learning tasks.",
            "Class_Room_Behaviour": "Demonstrates exemplary classroom behavior and follows all rules.",
            "Social_and_Emotional_Behaviour": "Displays strong social skills and emotional maturity.",
            "Concept": "Has mastered all key concepts for this grade level.",
            "Subject_wise_Feedback": {
                "Mathematics": "Exceptional performance in all mathematical concepts",
                "Science": "Shows deep curiosity and excellent understanding of scientific principles",
                "English Language Arts": "Outstanding reading comprehension and writing skills",
                "Social Studies": "Demonstrates strong analytical thinking about historical events",
                "Art": "Creative and expressive in all artistic endeavors"
            }
        }
    }
    
    try:
        # Make the request
        response = requests.post(
            f"{BASE_URL}/api/generate-doc",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        # Check response headers
        content_type = response.headers.get("content-type")
        content_disposition = response.headers.get("content-disposition")
        
        print(f"✅ Document generation successful")
        print(f"   Content-Type: {content_type}")
        print(f"   Content-Disposition: {content_disposition}")
        print(f"   Document size: {len(response.content)} bytes")
        
        # Save the document
        TEST_OUTPUT_DIR.mkdir(exist_ok=True)
        output_file = TEST_OUTPUT_DIR / "integration_test_report.docx"
        
        with open(output_file, "wb") as f:
            f.write(response.content)
        
        print(f"✅ Document saved to: {output_file}")
        
        # Verify file was created and has content
        assert output_file.exists()
        assert output_file.stat().st_size > 1000  # Should be at least 1KB
        
        return True
        
    except Exception as e:
        print(f"❌ Document generation failed: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid data."""
    print("🚫 Testing error handling...")
    
    # Test with invalid data
    invalid_data = {
        "school": "",  # Empty school name
        "student": "",  # Empty student name
        # Missing required fields
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/generate-doc",
            json=invalid_data,
            headers={"Content-Type": "application/json"}
        )
        
        # Should return 422 for validation error
        if response.status_code == 422:
            print("✅ Error handling working correctly (422 validation error)")
            return True
        else:
            print(f"❌ Expected 422, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Report Card Backend Integration Tests")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=2)
            if response.status_code == 200:
                break
        except:
            pass
        time.sleep(1)
    else:
        print("❌ Server not responding after 10 seconds")
        sys.exit(1)
    
    # Run tests
    tests = [
        test_health_endpoint,
        test_document_generation,
        test_error_handling,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    # Summary
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
