# Report Card Template Guide

This guide explains how to create a Word template (.docx) file for the Report Card Backend Service.

## Template Location

Save your template as: `templates/report_template.docx`

## Template Structure

Create a Word document with the following structure and placeholders:

### Header Section
```
{{ school }}
{{ assessment }}

STUDENT REPORT CARD
```

### Student Information
```
Student Name: {{ student }}
{% if teacher %}Teacher: {{ teacher }}{% endif %}
{% if grade %}Grade: {{ grade }}{% endif %}
{% if date %}Date: {{ date }}{% endif %}
```

### Report Sections

#### Attitude & Responsibility
```
ATTITUDE AND RESPONSIBILITY
{{ sections.Attitude_and_Responsibility }}
```

#### Classroom Behavior
```
CLASSROOM BEHAVIOUR
{{ sections.Class_Room_Behaviour }}
```

#### Social & Emotional Behavior
```
SOCIAL AND EMOTIONAL BEHAVIOUR
{{ sections.Social_and_Emotional_Behaviour }}
```

#### Concept Understanding
```
CONCEPT
{{ sections.Concept }}
```

#### Subject-wise Feedback
```
SUBJECT-WISE FEEDBACK

{% if sections.Subject_wise_Feedback %}
{% for subject, feedback in sections.Subject_wise_Feedback.items() %}
{{ subject.replace('_', ' ').title() }}:
{{ feedback }}

{% endfor %}
{% else %}
No subject feedback provided.
{% endif %}
```

### Footer Section
```
Generated on: {{ generated_at }}
Report Card System v{{ app_version }}
```

## Jinja2 Syntax Reference

### Variables
- `{{ variable_name }}` - Outputs the variable value
- `{{ variable_name|title }}` - Applies title case formatting
- `{{ variable_name.replace('_', ' ') }}` - Replaces underscores with spaces

### Conditionals
```
{% if condition %}
Content to show if true
{% else %}
Content to show if false
{% endif %}
```

### Loops
```
{% for item in list %}
{{ item }}
{% endfor %}
```

### Comments
```
{# This is a comment and won't appear in the output #}
```

## Available Variables

### Core Variables
- `assessment` - Assessment period (e.g., "1st Term Assessment Profile 2023 - 2024")
- `student` - Student name
- `teacher` - Teacher name (optional)
- `grade` - Student grade (optional)
- `school` - School name (optional)
- `date` - Report date (optional, defaults to current date)

### Section Variables
- `sections.Attitude_and_Responsibility` - Attitude assessment text
- `sections.Class_Room_Behaviour` - Classroom behavior text
- `sections.Social_and_Emotional_Behaviour` - Social/emotional behavior text
- `sections.Concept` - Concept understanding text
- `sections.Subject_wise_Feedback` - Dictionary of subject feedback

### Subject Feedback Structure
The `Subject_wise_Feedback` is a dictionary where:
- Keys are subject names (e.g., "English_Language", "Mathematics")
- Values are feedback text for each subject

Example subjects from your frontend:
- English_Language
- English_Literature
- Second_Language
- Mathematics
- Geography
- History
- Physics
- Chemistry
- Biology

### Metadata Variables
- `generated_at` - Timestamp when document was generated
- `app_name` - Application name
- `app_version` - Application version

## Formatting Tips

### Text Formatting
- Use Word's built-in styles for consistent formatting
- Apply bold, italic, and other formatting directly in Word
- Use proper heading styles (Heading 1, Heading 2, etc.)

### Layout
- Use tables for structured data if needed
- Add page breaks where appropriate
- Consider using sections for different parts of the report

### Professional Appearance
- Use consistent fonts and sizes
- Add school logo/letterhead if needed
- Include proper margins and spacing
- Consider using a professional color scheme

## Example Template Content

Here's a complete example of what your Word template might contain:

```
[SCHOOL LETTERHEAD/LOGO]

{{ school }}
{{ assessment }}

STUDENT REPORT CARD

Student Information:
Name: {{ student }}
{% if teacher %}Teacher: {{ teacher }}{% endif %}
{% if grade %}Grade: {{ grade }}{% endif %}
Date: {{ date }}

BEHAVIORAL ASSESSMENT

Attitude and Responsibility:
{{ sections.Attitude_and_Responsibility }}

Classroom Behaviour:
{{ sections.Class_Room_Behaviour }}

Social and Emotional Behaviour:
{{ sections.Social_and_Emotional_Behaviour }}

ACADEMIC ASSESSMENT

Concept Understanding:
{{ sections.Concept }}

Subject-wise Performance:
{% if sections.Subject_wise_Feedback %}
{% for subject, feedback in sections.Subject_wise_Feedback.items() %}

{{ subject.replace('_', ' ').title() }}:
{{ feedback }}
{% endfor %}
{% else %}
No subject-specific feedback available.
{% endif %}

SUMMARY

This report provides a comprehensive assessment of {{ student }}'s 
performance during the {{ assessment }} period.

---
Generated on: {{ generated_at }}
Report Card System v{{ app_version }}
```

## Testing Your Template

1. Create your .docx template with the placeholders
2. Save it as `templates/report_template.docx`
3. Start the backend service
4. Check the health endpoint: `GET /health`
5. Test with sample data: `POST /api/generate-doc`

## Troubleshooting

### Template Not Loading
- Check file path and permissions
- Ensure file is a valid .docx format
- Check logs for specific error messages

### Variables Not Rendering
- Verify Jinja2 syntax is correct
- Check variable names match exactly
- Ensure proper spacing around `{{` and `}}`

### Formatting Issues
- Test with simple text first
- Add formatting gradually
- Check for special characters that might break rendering

## Advanced Features

### Conditional Sections
```
{% if sections.Subject_wise_Feedback %}
[Subject feedback content]
{% else %}
No subject feedback available for this assessment.
{% endif %}
```

### Dynamic Subject Lists
```
{% for subject, feedback in sections.Subject_wise_Feedback.items() %}
{% if feedback %}
{{ subject.replace('_', ' ').title() }}: {{ feedback }}
{% endif %}
{% endfor %}
```

### Custom Formatting
```
Student: {{ student|upper }}
Assessment: {{ assessment|title }}
```

Remember to save your template as a .docx file and place it in the `templates/` directory!
