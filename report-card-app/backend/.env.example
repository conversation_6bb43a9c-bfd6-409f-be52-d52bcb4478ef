# Report Card Backend Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Path to the Word template file (absolute or relative to app root)
TEMPLATE_PATH=templates/report_template.docx

# Comma-separated list of allowed origins for CORS
# Include your React development and production URLs
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173

# Server configuration
HOST=0.0.0.0
PORT=8000

# Logging level (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# Pattern for generated filenames
# Available variables: {student}, {timestamp}, {assessment}
# Example: report_John_Doe_20240120_143022.docx
OUTPUT_FILENAME_PATTERN=report_{student}_{timestamp}.docx

# Maximum size for generated documents (in megabytes)
MAX_OUTPUT_SIZE_MB=10

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Enable image support in templates (true/false)
# When enabled, templates can include images from URLs or base64 data
ENABLE_IMAGE_SUPPORT=false

# Enable template caching (true/false)
# WARNING: Only enable if your application is thread-safe
# Improves performance but may cause issues in multi-threaded environments
TEMPLATE_CACHE=false

# Require authentication for API access (true/false)
# When enabled, requests must include valid authentication
AUTH_REQUIRED=false

# Authentication configuration (if AUTH_REQUIRED=true)
# JWT_SECRET_KEY=your-secret-key-here
# JWT_ALGORITHM=HS256
# JWT_EXPIRATION_HOURS=24

# Rate limiting configuration
# RATE_LIMIT_REQUESTS=100
# RATE_LIMIT_WINDOW_MINUTES=15

# Maximum request size (in megabytes)
# Limits the size of incoming JSON requests
MAX_REQUEST_SIZE_MB=5

# Template reload interval (in seconds)
# Set to 0 to disable automatic reloading
# Useful for development when templates change frequently
TEMPLATE_RELOAD_INTERVAL=0

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Enable debug mode (true/false)
# Shows detailed error messages and enables auto-reload
DEBUG=false

# Enable request/response logging (true/false)
# Logs all incoming requests and outgoing responses
LOG_REQUESTS=true

# Enable performance profiling (true/false)
# Adds timing information to logs
ENABLE_PROFILING=false

# =============================================================================
# PRODUCTION CONFIGURATION
# =============================================================================

# Number of worker processes (for production deployment)
# WORKERS=4

# Worker class (for gunicorn deployment)
# WORKER_CLASS=uvicorn.workers.UvicornWorker

# Keep-alive timeout (seconds)
# KEEP_ALIVE=2

# Maximum requests per worker (0 = unlimited)
# MAX_REQUESTS=1000

# Maximum request jitter (prevents all workers from restarting simultaneously)
# MAX_REQUESTS_JITTER=100

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================

# Enable metrics endpoint (true/false)
# Exposes /metrics endpoint for Prometheus scraping
ENABLE_METRICS=false

# Sentry DSN for error tracking
# SENTRY_DSN=https://your-sentry-dsn-here

# Application name for logging and monitoring
APP_NAME=report-card-backend

# Application version
APP_VERSION=1.0.0

# Environment name (development, staging, production)
ENVIRONMENT=development

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Trusted hosts (comma-separated)
# TRUSTED_HOSTS=localhost,127.0.0.1,your-domain.com

# Enable HTTPS redirect (true/false)
# FORCE_HTTPS=false

# CORS configuration
# CORS_ALLOW_CREDENTIALS=false
# CORS_ALLOW_METHODS=GET,POST,OPTIONS
# CORS_ALLOW_HEADERS=Content-Type,Authorization

# Content Security Policy
# CSP_HEADER=default-src 'self'

# =============================================================================
# DATABASE CONFIGURATION (if needed for future features)
# =============================================================================

# Database URL (if adding user management, audit logs, etc.)
# DATABASE_URL=sqlite:///./app.db
# DATABASE_URL=postgresql://user:password@localhost/dbname

# Database pool configuration
# DB_POOL_SIZE=5
# DB_MAX_OVERFLOW=10
# DB_POOL_TIMEOUT=30

# =============================================================================
# EXTERNAL SERVICES (if needed for future features)
# =============================================================================

# Email configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# Cloud storage (for template storage)
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
# AWS_REGION=us-east-1
# S3_BUCKET=your-bucket-name

# Redis configuration (for caching/sessions)
# REDIS_URL=redis://localhost:6379/0
