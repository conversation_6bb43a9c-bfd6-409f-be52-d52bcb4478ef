[tool:pytest]
# Pytest configuration for Report Card Backend Service

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    security: Security-related tests
    document: Document generation tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if using pytest-cov)
# addopts = --cov=app --cov-report=html --cov-report=term-missing --cov-fail-under=80

# Asyncio configuration
asyncio_mode = auto

# Logging
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:docxtpl.*
