"""
Version management routes for the Report Card Backend Service.

This module defines API endpoints for configuration versioning and drafts.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from ..database.connection import get_database
from ..models import (
    ConfigurationVersionRequest, ConfigurationVersionResponse,
    ConfigurationExportResponse
)
from ..services.version_service import version_service
from ..services.auth_service import get_current_admin_user, get_current_active_user
from ..database.models import User
from ..utils.logging import get_logger

router = APIRouter(prefix="/api/versions", tags=["versions"])
logger = get_logger(__name__)


@router.post("/", response_model=ConfigurationVersionResponse)
async def create_version(
    version_data: ConfigurationVersionRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new configuration version (admin only)."""
    try:
        version = await version_service.create_version(session, version_data, current_user.id)
        
        return ConfigurationVersionResponse(
            id=version.id,
            version_name=version.version_name,
            description=version.description,
            is_published=version.is_published,
            is_current=version.is_current,
            created_by=version.created_by,
            created_at=version.created_at,
            published_at=version.published_at,
            creator_name=getattr(version.creator, 'full_name', None) if hasattr(version, 'creator') else None
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=List[ConfigurationVersionResponse])
async def get_versions(
    include_drafts: bool = Query(True, description="Include draft versions"),
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get all configuration versions."""
    versions = await version_service.get_versions(session, include_drafts)
    
    return [
        ConfigurationVersionResponse(
            id=version.id,
            version_name=version.version_name,
            description=version.description,
            is_published=version.is_published,
            is_current=version.is_current,
            created_by=version.created_by,
            created_at=version.created_at,
            published_at=version.published_at,
            creator_name=getattr(version.creator, 'full_name', None) if hasattr(version, 'creator') else None
        )
        for version in versions
    ]


@router.get("/current", response_model=ConfigurationVersionResponse)
async def get_current_version(
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get the current published version."""
    version = await version_service.get_current_version(session)
    
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No current version found"
        )
    
    return ConfigurationVersionResponse(
        id=version.id,
        version_name=version.version_name,
        description=version.description,
        is_published=version.is_published,
        is_current=version.is_current,
        created_by=version.created_by,
        created_at=version.created_at,
        published_at=version.published_at,
        creator_name=getattr(version.creator, 'full_name', None) if hasattr(version, 'creator') else None
    )


@router.get("/{version_id}", response_model=ConfigurationVersionResponse)
async def get_version(
    version_id: int,
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get a version by ID."""
    version = await version_service.get_version(session, version_id)
    
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Version not found"
        )
    
    return ConfigurationVersionResponse(
        id=version.id,
        version_name=version.version_name,
        description=version.description,
        is_published=version.is_published,
        is_current=version.is_current,
        created_by=version.created_by,
        created_at=version.created_at,
        published_at=version.published_at,
        creator_name=getattr(version.creator, 'full_name', None) if hasattr(version, 'creator') else None
    )


@router.post("/{version_id}/publish", response_model=ConfigurationVersionResponse)
async def publish_version(
    version_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Publish a version and make it current (admin only)."""
    try:
        version = await version_service.publish_version(session, version_id, current_user.id)
        
        return ConfigurationVersionResponse(
            id=version.id,
            version_name=version.version_name,
            description=version.description,
            is_published=version.is_published,
            is_current=version.is_current,
            created_by=version.created_by,
            created_at=version.created_at,
            published_at=version.published_at,
            creator_name=getattr(version.creator, 'full_name', None) if hasattr(version, 'creator') else None
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{version_id}/duplicate", response_model=ConfigurationVersionResponse)
async def duplicate_version(
    version_id: int,
    new_version_name: str = Query(..., description="Name for the new version"),
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Duplicate a version with all its configuration data (admin only)."""
    try:
        version = await version_service.duplicate_version(
            session, version_id, new_version_name, current_user.id
        )
        
        return ConfigurationVersionResponse(
            id=version.id,
            version_name=version.version_name,
            description=version.description,
            is_published=version.is_published,
            is_current=version.is_current,
            created_by=version.created_by,
            created_at=version.created_at,
            published_at=version.published_at,
            creator_name=getattr(version.creator, 'full_name', None) if hasattr(version, 'creator') else None
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{version_id}")
async def delete_version(
    version_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Delete a version (admin only, only if not published)."""
    try:
        success = await version_service.delete_version(session, version_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Version not found"
            )
        
        return {"message": "Version deleted successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{version_id}/export", response_model=ConfigurationExportResponse)
async def export_version(
    version_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Export a version's configuration data (admin only)."""
    try:
        export_data = await version_service.export_version(session, version_id)
        return export_data
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
