"""
Authentication routes for the Report Card Backend Service.

This module defines API endpoints for user authentication and authorization.
"""

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession

from ..database.connection import get_database
from ..models import (
    LoginRequest, TeacherLoginRequest, LoginResponse,
    PasswordChangeRequest, UserCreateRequest, UserUpdateRequest, UserInfo
)
from ..services.auth_service import auth_service, get_current_active_user, get_current_admin_user
from ..database.models import User
from ..utils.security import hash_password
from ..utils.logging import get_logger

router = APIRouter(prefix="/api/auth", tags=["authentication"])
logger = get_logger(__name__)


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    request: Request,
    session: AsyncSession = Depends(get_database)
):
    """
    Authenticate user with username and password.

    - For admin users: use username="admin" and password="admin"
    - For teachers: use any username with any password (creates temporary teacher account)
    """
    logger.info(f"Login attempt for user: {login_data.username}")

    # Authenticate user
    user = await auth_service.authenticate_user(
        session, login_data.username, login_data.password
    )

    if not user:
        logger.warning(f"Failed login attempt for user: {login_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    token_response = auth_service.create_access_token_for_user(user)

    logger.info(f"Successful login for user: {user.username} (role: {user.role})")

    return LoginResponse(**token_response)


# Teacher login endpoint removed - now using unified login endpoint


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return UserInfo(
        id=current_user.id,
        username=current_user.username,
        full_name=current_user.full_name,
        email=current_user.email,
        role=current_user.role,
        is_active=current_user.is_active
    )


@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Change user password."""
    # Verify current password
    from ..utils.security import verify_password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )
    
    # Update password
    current_user.hashed_password = hash_password(password_data.new_password)
    await session.commit()
    
    logger.info(f"Password changed for user: {current_user.username}")
    
    return {"message": "Password changed successfully"}


@router.post("/users", response_model=UserInfo)
async def create_user(
    user_data: UserCreateRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new user (admin only)."""
    # Check if username already exists
    from sqlalchemy import select
    result = await session.execute(
        select(User).where(User.username == user_data.username)
    )
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already exists"
        )
    
    # Create new user
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hash_password(user_data.password),
        role=user_data.role,
        is_active=True
    )
    
    session.add(new_user)
    await session.commit()
    await session.refresh(new_user)
    
    logger.info(f"New user created: {new_user.username} (role: {new_user.role}) by {current_user.username}")
    
    return UserInfo(
        id=new_user.id,
        username=new_user.username,
        full_name=new_user.full_name,
        email=new_user.email,
        role=new_user.role,
        is_active=new_user.is_active
    )


@router.get("/users", response_model=list[UserInfo])
async def list_users(
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """List all users (admin only)."""
    from sqlalchemy import select
    result = await session.execute(select(User).order_by(User.username))
    users = result.scalars().all()
    
    return [
        UserInfo(
            id=user.id,
            username=user.username,
            full_name=user.full_name,
            email=user.email,
            role=user.role,
            is_active=user.is_active
        )
        for user in users
    ]


@router.put("/users/{user_id}", response_model=UserInfo)
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Update user information (admin only)."""
    from sqlalchemy import select
    result = await session.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update user fields
    if user_data.email is not None:
        user.email = user_data.email
    if user_data.full_name is not None:
        user.full_name = user_data.full_name
    if user_data.role is not None:
        user.role = user_data.role
    if user_data.is_active is not None:
        user.is_active = user_data.is_active
    
    await session.commit()
    await session.refresh(user)
    
    logger.info(f"User updated: {user.username} by {current_user.username}")
    
    return UserInfo(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        email=user.email,
        role=user.role,
        is_active=user.is_active
    )


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Delete user (admin only)."""
    from sqlalchemy import select
    result = await session.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )
    
    await session.delete(user)
    await session.commit()
    
    logger.info(f"User deleted: {user.username} by {current_user.username}")
    
    return {"message": "User deleted successfully"}
