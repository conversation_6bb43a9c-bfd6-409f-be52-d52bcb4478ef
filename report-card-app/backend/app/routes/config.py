"""
Configuration routes for the Report Card Backend Service.

This module defines API endpoints for configuration management including
categories, subjects, assessment points, and school settings.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from ..database.connection import get_database
from ..models import (
    CategoryRequest, CategoryResponse, SubjectRequest, SubjectResponse,
    AssessmentPointRequest, AssessmentPointResponse, SchoolSettingsRequest,
    SchoolSettingsResponse, GradeRequest, GradeResponse,
    ConfigurationVersionRequest, ConfigurationStatsResponse, BulkImportRequest,
    BulkImportResponse, ConfigurationExportResponse
)
from ..services.config_service import config_service
from ..services.auth_service import get_current_admin_user, get_current_active_user
from ..database.models import User
from ..utils.logging import get_logger

router = APIRouter(prefix="/api/config", tags=["configuration"])
logger = get_logger(__name__)


# Category endpoints
@router.post("/categories", response_model=CategoryResponse)
async def create_category(
    category_data: CategoryRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new category (admin only)."""
    category = await config_service.create_category(session, category_data, current_user.id)
    
    return CategoryResponse(
        id=category.id,
        name=category.name,
        display_name=category.display_name,
        category_type=category.category_type,
        description=category.description,
        sort_order=category.sort_order,
        is_active=category.is_active,
        created_at=category.created_at,
        updated_at=category.updated_at
    )


@router.get("/categories", response_model=List[CategoryResponse])
async def get_categories(
    category_type: Optional[str] = Query(None, description="Filter by category type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get categories with optional filtering."""
    categories = await config_service.get_categories(session, category_type, is_active)
    
    return [
        CategoryResponse(
            id=cat.id,
            name=cat.name,
            display_name=cat.display_name,
            category_type=cat.category_type,
            description=cat.description,
            sort_order=cat.sort_order,
            is_active=cat.is_active,
            created_at=cat.created_at,
            updated_at=cat.updated_at
        )
        for cat in categories
    ]


@router.get("/categories/{category_id}", response_model=CategoryResponse)
async def get_category(
    category_id: int,
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get a category by ID."""
    category = await config_service.get_category(session, category_id)
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return CategoryResponse(
        id=category.id,
        name=category.name,
        display_name=category.display_name,
        category_type=category.category_type,
        description=category.description,
        sort_order=category.sort_order,
        is_active=category.is_active,
        created_at=category.created_at,
        updated_at=category.updated_at
    )


@router.put("/categories/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: int,
    category_data: CategoryRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Update a category (admin only)."""
    category = await config_service.update_category(session, category_id, category_data, current_user.id)
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return CategoryResponse(
        id=category.id,
        name=category.name,
        display_name=category.display_name,
        category_type=category.category_type,
        description=category.description,
        sort_order=category.sort_order,
        is_active=category.is_active,
        created_at=category.created_at,
        updated_at=category.updated_at
    )


@router.delete("/categories/{category_id}")
async def delete_category(
    category_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Delete a category (admin only)."""
    success = await config_service.delete_category(session, category_id, current_user.id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return {"message": "Category deleted successfully"}


# Subject endpoints
@router.post("/subjects", response_model=SubjectResponse)
async def create_subject(
    subject_data: SubjectRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new subject (admin only)."""
    subject = await config_service.create_subject(session, subject_data, current_user.id)
    
    return SubjectResponse(
        id=subject.id,
        name=subject.name,
        display_name=subject.display_name,
        grade_from=subject.grade_from,
        grade_to=subject.grade_to,
        is_compulsory=subject.is_compulsory,
        subject_group=subject.subject_group,
        excludes_subjects=subject.excludes_subjects,
        alternative_to=subject.alternative_to,
        options=subject.options,
        sort_order=subject.sort_order,
        is_active=subject.is_active,
        created_at=subject.created_at,
        updated_at=subject.updated_at
    )


@router.get("/subjects", response_model=List[SubjectResponse])
async def get_subjects(
    grade: Optional[int] = Query(None, description="Filter by grade"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get subjects with optional filtering."""
    subjects = await config_service.get_subjects(session, grade, is_active)
    
    return [
        SubjectResponse(
            id=subj.id,
            name=subj.name,
            display_name=subj.display_name,
            grade_from=subj.grade_from,
            grade_to=subj.grade_to,
            is_compulsory=subj.is_compulsory,
            subject_group=subj.subject_group,
            excludes_subjects=subj.excludes_subjects,
            alternative_to=subj.alternative_to,
            options=subj.options,
            sort_order=subj.sort_order,
            is_active=subj.is_active,
            created_at=subj.created_at,
            updated_at=subj.updated_at
        )
        for subj in subjects
    ]


@router.get("/subjects/{subject_id}", response_model=SubjectResponse)
async def get_subject(
    subject_id: int,
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get a subject by ID."""
    subject = await config_service.get_subject(session, subject_id)
    
    if not subject:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subject not found"
        )
    
    return SubjectResponse(
        id=subject.id,
        name=subject.name,
        display_name=subject.display_name,
        grade_from=subject.grade_from,
        grade_to=subject.grade_to,
        is_compulsory=subject.is_compulsory,
        subject_group=subject.subject_group,
        excludes_subjects=subject.excludes_subjects,
        alternative_to=subject.alternative_to,
        options=subject.options,
        sort_order=subject.sort_order,
        is_active=subject.is_active,
        created_at=subject.created_at,
        updated_at=subject.updated_at
    )


# Assessment Point endpoints
@router.post("/assessment-points", response_model=AssessmentPointResponse)
async def create_assessment_point(
    point_data: AssessmentPointRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new assessment point (admin only)."""
    point = await config_service.create_assessment_point(session, point_data, current_user.id)
    
    return AssessmentPointResponse(
        id=point.id,
        category_id=point.category_id,
        subject_id=point.subject_id,
        point_text=point.point_text,
        point_type=point.point_type,
        grade_from=point.grade_from,
        grade_to=point.grade_to,
        sort_order=point.sort_order,
        is_active=point.is_active,
        created_at=point.created_at,
        updated_at=point.updated_at,
        category=None,  # Will be populated if needed
        subject=None    # Will be populated if needed
    )


@router.get("/assessment-points", response_model=List[AssessmentPointResponse])
async def get_assessment_points(
    category_id: Optional[int] = Query(None, description="Filter by category ID"),
    subject_id: Optional[int] = Query(None, description="Filter by subject ID"),
    point_type: Optional[str] = Query(None, description="Filter by point type"),
    grade: Optional[int] = Query(None, description="Filter by grade"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get assessment points with optional filtering."""
    points = await config_service.get_assessment_points(
        session, category_id, subject_id, point_type, grade, is_active
    )
    
    return [
        AssessmentPointResponse(
            id=point.id,
            category_id=point.category_id,
            subject_id=point.subject_id,
            point_text=point.point_text,
            point_type=point.point_type,
            grade_from=point.grade_from,
            grade_to=point.grade_to,
            sort_order=point.sort_order,
            is_active=point.is_active,
            created_at=point.created_at,
            updated_at=point.updated_at,
            category={
                "id": point.category.id,
                "name": point.category.name,
                "display_name": point.category.display_name,
                "category_type": point.category.category_type,
                "description": point.category.description,
                "sort_order": point.category.sort_order,
                "is_active": point.category.is_active,
                "created_at": point.category.created_at,
                "updated_at": point.category.updated_at
            } if point.category else None,
            subject={
                "id": point.subject.id,
                "name": point.subject.name,
                "display_name": point.subject.display_name,
                "grade_from": point.subject.grade_from,
                "grade_to": point.subject.grade_to,
                "is_compulsory": point.subject.is_compulsory,
                "subject_group": point.subject.subject_group,
                "excludes_subjects": point.subject.excludes_subjects,
                "alternative_to": point.subject.alternative_to,
                "options": point.subject.options,
                "sort_order": point.subject.sort_order,
                "is_active": point.subject.is_active,
                "created_at": point.subject.created_at,
                "updated_at": point.subject.updated_at
            } if point.subject else None
        )
        for point in points
    ]


# Grade endpoints
@router.post("/grades", response_model=GradeResponse)
async def create_grade(
    grade_data: GradeRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new grade (admin only)."""
    grade = await config_service.create_grade(session, grade_data, current_user.id)
    return GradeResponse(
        id=grade.id,
        grade_level=grade.grade_level,
        grade_name=grade.grade_name,
        grade_code=grade.grade_code,
        description=grade.description,
        age_range=grade.age_range,
        sort_order=grade.sort_order,
        is_active=grade.is_active,
        created_at=grade.created_at,
        updated_at=grade.updated_at
    )


@router.get("/grades", response_model=List[GradeResponse])
async def get_grades(
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get all grades with optional filtering."""
    grades = await config_service.get_grades(session, is_active=is_active)
    return [
        GradeResponse(
            id=grade.id,
            grade_level=grade.grade_level,
            grade_name=grade.grade_name,
            grade_code=grade.grade_code,
            description=grade.description,
            age_range=grade.age_range,
            sort_order=grade.sort_order,
            is_active=grade.is_active,
            created_at=grade.created_at,
            updated_at=grade.updated_at
        )
        for grade in grades
    ]


@router.get("/grades/{grade_id}", response_model=GradeResponse)
async def get_grade(
    grade_id: int,
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get a specific grade by ID."""
    grade = await config_service.get_grade(session, grade_id)
    if not grade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Grade not found"
        )

    return GradeResponse(
        id=grade.id,
        grade_level=grade.grade_level,
        grade_name=grade.grade_name,
        grade_code=grade.grade_code,
        description=grade.description,
        age_range=grade.age_range,
        sort_order=grade.sort_order,
        is_active=grade.is_active,
        created_at=grade.created_at,
        updated_at=grade.updated_at
    )


@router.put("/grades/{grade_id}", response_model=GradeResponse)
async def update_grade(
    grade_id: int,
    grade_data: GradeRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Update a grade (admin only)."""
    grade = await config_service.update_grade(session, grade_id, grade_data, current_user.id)
    if not grade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Grade not found"
        )

    return GradeResponse(
        id=grade.id,
        grade_level=grade.grade_level,
        grade_name=grade.grade_name,
        grade_code=grade.grade_code,
        description=grade.description,
        age_range=grade.age_range,
        sort_order=grade.sort_order,
        is_active=grade.is_active,
        created_at=grade.created_at,
        updated_at=grade.updated_at
    )


@router.delete("/grades/{grade_id}")
async def delete_grade(
    grade_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Delete a grade (admin only)."""
    success = await config_service.delete_grade(session, grade_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Grade not found"
        )

    return {"message": "Grade deleted successfully"}


# Version Management endpoints
@router.get("/versions")
async def get_versions(
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get all configuration versions."""
    versions = await config_service.get_versions(session)
    return [
        {
            "id": version.id,
            "version_name": version.version_name,
            "description": version.description,
            "is_published": version.is_published,
            "is_current": version.is_current,
            "created_by": version.created_by,
            "created_at": version.created_at,
            "published_at": version.published_at
        }
        for version in versions
    ]


@router.post("/versions")
async def create_version(
    version_data: ConfigurationVersionRequest,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Create a new configuration version (admin only)."""
    version = await config_service.create_version(session, version_data, current_user.id)
    return {
        "id": version.id,
        "version_name": version.version_name,
        "description": version.description,
        "is_published": version.is_published,
        "is_current": version.is_current,
        "created_by": version.created_by,
        "created_at": version.created_at,
        "published_at": version.published_at
    }


@router.put("/versions/{version_id}/publish")
async def publish_version(
    version_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Publish a configuration version (admin only)."""
    success = await config_service.publish_version(session, version_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Version not found"
        )
    return {"message": "Version published successfully"}


@router.put("/versions/{version_id}/set-current")
async def set_current_version(
    version_id: int,
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Set a version as current (admin only)."""
    success = await config_service.set_current_version(session, version_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Version not found"
        )
    return {"message": "Version set as current successfully"}


# Statistics endpoint
@router.get("/stats", response_model=ConfigurationStatsResponse)
async def get_configuration_stats(
    current_user: User = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_database)
):
    """Get configuration statistics."""
    return await config_service.get_configuration_stats(session)


# Data migration endpoint
@router.post("/migrate-csv")
async def migrate_csv_data(
    current_user: User = Depends(get_current_admin_user),
    session: AsyncSession = Depends(get_database)
):
    """Migrate data from CSV files to database (admin only)."""
    results = await config_service.migrate_csv_data(session, current_user.id)

    if results["errors"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Migration completed with errors: {results['errors']}"
        )

    return {
        "message": "CSV data migration completed successfully",
        "results": results
    }
