"""
Data migration utilities for moving CSV data to database.

This module provides functions to migrate existing CSV data and hardcoded
frontend data to the new database structure.
"""

import csv
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from .models import (
    User, ConfigurationVersion, SchoolSettings, Category, Subject, 
    AssessmentPoint, Template, AuditLog
)
from .connection import get_database, init_database
from ..utils.security import hash_password


async def create_initial_admin_user(session: AsyncSession) -> User:
    """Create the initial admin user."""
    # Check if admin user already exists
    result = await session.execute(
        select(User).where(User.username == "admin")
    )
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
        return existing_user
    
    admin_user = User(
        username="admin",
        email="<EMAIL>",
        hashed_password=hash_password("admin"),
        full_name="System Administrator",
        role="admin",
        is_active=True
    )
    
    session.add(admin_user)
    await session.commit()
    await session.refresh(admin_user)
    
    return admin_user


async def create_initial_configuration_version(session: AsyncSession, admin_user: User) -> ConfigurationVersion:
    """Create the initial configuration version."""
    # Check if initial version already exists
    result = await session.execute(
        select(ConfigurationVersion).where(ConfigurationVersion.version_name == "Initial Configuration")
    )
    existing_version = result.scalar_one_or_none()
    
    if existing_version:
        return existing_version
    
    config_version = ConfigurationVersion(
        version_name="Initial Configuration",
        description="Initial configuration migrated from CSV files and hardcoded data",
        is_published=True,
        is_current=True,
        created_by=admin_user.id
    )
    
    session.add(config_version)
    await session.commit()
    await session.refresh(config_version)
    
    return config_version


async def migrate_csv_behavior_points(session: AsyncSession, version: ConfigurationVersion, csv_path: Path):
    """Migrate behavior points from CSV file."""
    if not csv_path.exists():
        print(f"Warning: CSV file not found: {csv_path}")
        return
    
    # Create categories for behavior points
    categories_map = {
        "ATTITUDE AND RESPONSIBILITY": "Attitude and Responsibility",
        "IN AND DURING CLASS": "Classroom Behavior", 
        "SOCIAL EMOTIONAL BEHAVIOUR": "Social Emotional Behavior",
        "SELF AWARENESS": "Self Awareness",
        "WITH PEERS": "Peer Interactions",
        "OUTDOORS": "Outdoor Behavior",
        "Conflict/Opposition": "Conflict Management",
        "Includes Others": "Inclusion",
        "While on a task": "Task Focus",
        "Once a period ends": "Transitions",
        "When places are changed and classes shuffled": "Adaptability",
        "WRITING": "Writing Skills"
    }
    
    # Create categories
    created_categories = {}
    for csv_name, display_name in categories_map.items():
        category = Category(
            version_id=version.id,
            name=csv_name.lower().replace(" ", "_"),
            display_name=display_name,
            category_type="behavior",
            description=f"Assessment points for {display_name.lower()}",
            sort_order=len(created_categories)
        )
        session.add(category)
        created_categories[csv_name] = category
    
    await session.commit()
    
    # Read and migrate CSV data
    with open(csv_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        sort_order = 0
        
        for row in reader:
            subject_category = row.get('Subject', '').strip()
            point_text = row.get('Point', '').strip()
            
            if not point_text:
                continue
            
            # Find or create category
            category = created_categories.get(subject_category)
            if not category:
                # Create a generic category for unknown subjects
                category = Category(
                    version_id=version.id,
                    name=subject_category.lower().replace(" ", "_"),
                    display_name=subject_category,
                    category_type="behavior",
                    description=f"Assessment points for {subject_category}",
                    sort_order=len(created_categories)
                )
                session.add(category)
                await session.commit()
                await session.refresh(category)
                created_categories[subject_category] = category
            
            # Create assessment point
            assessment_point = AssessmentPoint(
                version_id=version.id,
                category_id=category.id,
                point_text=point_text,
                point_type="behavior",
                sort_order=sort_order
            )
            session.add(assessment_point)
            sort_order += 1
    
    await session.commit()
    print(f"Migrated behavior points from {csv_path}")


async def migrate_csv_subject_points(session: AsyncSession, version: ConfigurationVersion, csv_path: Path):
    """Migrate subject points from CSV file."""
    if not csv_path.exists():
        print(f"Warning: CSV file not found: {csv_path}")
        return
    
    # Create subjects based on CSV data
    subjects_map = {}
    
    with open(csv_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        sort_order = 0
        
        for row in reader:
            grade_from = int(row.get('From', 1))
            grade_to = int(row.get('To', 10))
            subject_name = row.get('Subject', '').strip()
            point_text = row.get('Point', '').strip()
            
            if not point_text or not subject_name:
                continue
            
            # Create or get subject
            subject_key = f"{subject_name}_{grade_from}_{grade_to}"
            if subject_key not in subjects_map:
                subject = Subject(
                    version_id=version.id,
                    name=subject_name,
                    display_name=subject_name,
                    grade_from=grade_from,
                    grade_to=grade_to,
                    is_compulsory=subject_name in ['ENGLISH', 'MATHS'],
                    sort_order=len(subjects_map)
                )
                session.add(subject)
                await session.commit()
                await session.refresh(subject)
                subjects_map[subject_key] = subject
            else:
                subject = subjects_map[subject_key]
            
            # Create assessment point
            assessment_point = AssessmentPoint(
                version_id=version.id,
                subject_id=subject.id,
                point_text=point_text,
                point_type="subject",
                grade_from=grade_from,
                grade_to=grade_to,
                sort_order=sort_order
            )
            session.add(assessment_point)
            sort_order += 1
    
    await session.commit()
    print(f"Migrated subject points from {csv_path}")


async def migrate_csv_concept_points(session: AsyncSession, version: ConfigurationVersion, csv_path: Path):
    """Migrate concept points from CSV file."""
    if not csv_path.exists():
        print(f"Warning: CSV file not found: {csv_path}")
        return
    
    with open(csv_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        sort_order = 0
        
        for row in reader:
            subject_category = row.get('Subject', '').strip()
            point_text = row.get('Point', '').strip()
            
            if not point_text:
                continue
            
            # Determine point type based on subject category
            if subject_category in ['SCIENCE \ EVS \ MATHS', 'MATHS']:
                point_type = "subject"
            elif subject_category == 'PROJECT':
                point_type = "project"
            else:
                point_type = "concept"
            
            # Create assessment point
            assessment_point = AssessmentPoint(
                version_id=version.id,
                point_text=point_text,
                point_type=point_type,
                sort_order=sort_order
            )
            session.add(assessment_point)
            sort_order += 1
    
    await session.commit()
    print(f"Migrated concept points from {csv_path}")


async def create_initial_school_settings(session: AsyncSession, version: ConfigurationVersion):
    """Create initial school settings."""
    school_settings = SchoolSettings(
        version_id=version.id,
        school_name="Demo School",
        school_address="123 Education Street, Learning City, LC 12345",
        academic_year="2024-2025",
        assessment_periods=[
            "1st Term Assessment Profile 2024-2025",
            "2nd Term Assessment Profile 2024-2025", 
            "3rd Term Assessment Profile 2024-2025",
            "Final Assessment Profile 2024-2025"
        ]
    )
    
    session.add(school_settings)
    await session.commit()
    print("Created initial school settings")


async def run_initial_migration():
    """Run the complete initial data migration."""
    print("Starting initial data migration...")
    
    # Initialize database
    await init_database()
    
    async for session in get_database():
        try:
            # Create admin user
            admin_user = await create_initial_admin_user(session)
            print(f"Created/found admin user: {admin_user.username}")
            
            # Create initial configuration version
            config_version = await create_initial_configuration_version(session, admin_user)
            print(f"Created/found configuration version: {config_version.version_name}")
            
            # Define CSV file paths
            # Go up from app/database/migration_utils.py to the project root, then to Points
            base_path = Path(__file__).parent.parent.parent.parent.parent / "Points"
            behavior_csv = base_path / "Behavior.csv"
            subjects_csv = base_path / "Subjects.csv"
            concept_csv = base_path / "concept_report.csv"
            
            # Migrate CSV data
            await migrate_csv_behavior_points(session, config_version, behavior_csv)
            await migrate_csv_subject_points(session, config_version, subjects_csv)
            await migrate_csv_concept_points(session, config_version, concept_csv)
            
            # Create initial school settings
            await create_initial_school_settings(session, config_version)
            
            print("Initial data migration completed successfully!")
            
        except Exception as e:
            print(f"Error during migration: {e}")
            await session.rollback()
            raise
        finally:
            break  # Exit the async generator


if __name__ == "__main__":
    asyncio.run(run_initial_migration())
