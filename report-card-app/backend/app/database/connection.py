"""
Database connection management for the Report Card Backend Service.

This module handles database connection setup, session management, and initialization.
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from typing import AsyncGenerator

from ..config import get_settings


class Base(DeclarativeBase):
    """Base class for all database models."""
    pass


# Global variables for database connection
engine = None
async_session_maker = None


async def init_database():
    """Initialize the database connection and create tables."""
    global engine, async_session_maker
    
    settings = get_settings()
    
    # Create async engine with appropriate settings for SQLite vs PostgreSQL
    if "sqlite" in settings.database_url:
        # SQLite doesn't support pool_size and max_overflow
        engine = create_async_engine(
            settings.database_url,
            echo=settings.database_echo,
        )
    else:
        # PostgreSQL and other databases support connection pooling
        engine = create_async_engine(
            settings.database_url,
            echo=settings.database_echo,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
        )
    
    # Create session maker
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_database() -> AsyncGenerator[AsyncSession, None]:
    """Get a database session."""
    if async_session_maker is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def close_database():
    """Close the database connection."""
    global engine
    if engine:
        await engine.dispose()
