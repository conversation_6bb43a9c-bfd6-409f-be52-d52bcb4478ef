"""
Database models for the Report Card Backend Service.

This module defines all database tables and relationships for configuration management.
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import (
    <PERSON><PERSON><PERSON>, DateTime, Integer, String, Text, ForeignKey, 
    UniqueConstraint, Index, JSON
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from .connection import Base


class User(Base):
    """User model for authentication and authorization."""
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    email: Mapped[Optional[str]] = mapped_column(String(100), unique=True)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(100))
    role: Mapped[str] = mapped_column(String(20), default="teacher", nullable=False)  # admin, teacher
    is_active: Mapped[bool] = mapped_column(<PERSON>olean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    __table_args__ = (
        Index("idx_users_username", "username"),
        Index("idx_users_email", "email"),
        Index("idx_users_role", "role"),
    )


class ConfigurationVersion(Base):
    """Configuration version model for tracking changes and drafts."""
    __tablename__ = "configuration_versions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_current: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    created_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    published_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Relationships
    creator: Mapped["User"] = relationship("User")
    
    __table_args__ = (
        Index("idx_config_versions_published", "is_published"),
        Index("idx_config_versions_current", "is_current"),
        Index("idx_config_versions_created_by", "created_by"),
    )


class Grade(Base):
    """Grade level definitions and properties."""
    __tablename__ = "grades"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("configuration_versions.id"), nullable=False)
    grade_level: Mapped[int] = mapped_column(Integer, nullable=False)  # 0 for K, 1-12 for grades
    grade_name: Mapped[str] = mapped_column(String(20), nullable=False)  # "Kindergarten", "Grade 1", etc.
    grade_code: Mapped[str] = mapped_column(String(10), nullable=False)  # "K", "1", "2", etc.
    description: Mapped[Optional[str]] = mapped_column(Text)
    age_range: Mapped[Optional[str]] = mapped_column(String(20))  # "5-6 years", "6-7 years", etc.
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    version: Mapped["ConfigurationVersion"] = relationship("ConfigurationVersion")

    __table_args__ = (
        Index("idx_grades_version", "version_id"),
        Index("idx_grades_level", "grade_level"),
        Index("idx_grades_active", "is_active"),
        UniqueConstraint("version_id", "grade_level", name="uq_grade_level_version"),
        UniqueConstraint("version_id", "grade_code", name="uq_grade_code_version"),
    )


class SchoolSettings(Base):
    """School settings and general configuration."""
    __tablename__ = "school_settings"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("configuration_versions.id"), nullable=False)
    school_name: Mapped[str] = mapped_column(String(200), nullable=False)
    school_address: Mapped[Optional[str]] = mapped_column(Text)
    school_logo_url: Mapped[Optional[str]] = mapped_column(String(500))
    academic_year: Mapped[str] = mapped_column(String(20), nullable=False)
    assessment_periods: Mapped[list] = mapped_column(JSON, nullable=False)  # List of assessment periods
    default_template_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("templates.id"))
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    version: Mapped["ConfigurationVersion"] = relationship("ConfigurationVersion")
    default_template: Mapped[Optional["Template"]] = relationship("Template")
    
    __table_args__ = (
        Index("idx_school_settings_version", "version_id"),
    )


class Category(Base):
    """Categories for organizing assessment points."""
    __tablename__ = "categories"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("configuration_versions.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    display_name: Mapped[str] = mapped_column(String(100), nullable=False)
    category_type: Mapped[str] = mapped_column(String(50), nullable=False)  # attitude, classroom, social_emotional, subject
    description: Mapped[Optional[str]] = mapped_column(Text)
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    version: Mapped["ConfigurationVersion"] = relationship("ConfigurationVersion")
    assessment_points: Mapped[list["AssessmentPoint"]] = relationship("AssessmentPoint", back_populates="category")
    
    __table_args__ = (
        Index("idx_categories_version", "version_id"),
        Index("idx_categories_type", "category_type"),
        Index("idx_categories_active", "is_active"),
        UniqueConstraint("version_id", "name", "category_type", name="uq_category_name_type_version"),
    )


class Subject(Base):
    """Subject configuration with grade ranges and requirements."""
    __tablename__ = "subjects"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("configuration_versions.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    display_name: Mapped[str] = mapped_column(String(100), nullable=False)
    grade_from: Mapped[int] = mapped_column(Integer, nullable=False)
    grade_to: Mapped[int] = mapped_column(Integer, nullable=False)
    is_compulsory: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    subject_group: Mapped[Optional[str]] = mapped_column(String(50))  # science, commerce, etc.
    excludes_subjects: Mapped[Optional[list]] = mapped_column(JSON)  # List of subject IDs that this excludes
    alternative_to: Mapped[Optional[str]] = mapped_column(String(100))  # Alternative subject name
    options: Mapped[Optional[list]] = mapped_column(JSON)  # For subjects with multiple options
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    version: Mapped["ConfigurationVersion"] = relationship("ConfigurationVersion")
    assessment_points: Mapped[list["AssessmentPoint"]] = relationship("AssessmentPoint", back_populates="subject")
    
    __table_args__ = (
        Index("idx_subjects_version", "version_id"),
        Index("idx_subjects_grade_range", "grade_from", "grade_to"),
        Index("idx_subjects_compulsory", "is_compulsory"),
        Index("idx_subjects_group", "subject_group"),
        Index("idx_subjects_active", "is_active"),
        UniqueConstraint("version_id", "name", name="uq_subject_name_version"),
    )


class AssessmentPoint(Base):
    """Assessment points for different categories and subjects."""
    __tablename__ = "assessment_points"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("configuration_versions.id"), nullable=False)
    category_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("categories.id"))
    subject_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("subjects.id"))
    point_text: Mapped[str] = mapped_column(Text, nullable=False)
    point_type: Mapped[str] = mapped_column(String(50), nullable=False)  # behavior, subject, concept, writing, self_awareness
    grade_from: Mapped[Optional[int]] = mapped_column(Integer)
    grade_to: Mapped[Optional[int]] = mapped_column(Integer)
    sort_order: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    version: Mapped["ConfigurationVersion"] = relationship("ConfigurationVersion")
    category: Mapped[Optional["Category"]] = relationship("Category", back_populates="assessment_points")
    subject: Mapped[Optional["Subject"]] = relationship("Subject", back_populates="assessment_points")
    
    __table_args__ = (
        Index("idx_assessment_points_version", "version_id"),
        Index("idx_assessment_points_category", "category_id"),
        Index("idx_assessment_points_subject", "subject_id"),
        Index("idx_assessment_points_type", "point_type"),
        Index("idx_assessment_points_grade_range", "grade_from", "grade_to"),
        Index("idx_assessment_points_active", "is_active"),
    )


class Template(Base):
    """Word document templates for report generation."""
    __tablename__ = "templates"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("configuration_versions.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[str] = mapped_column(String(500), nullable=False)
    file_size: Mapped[int] = mapped_column(Integer, nullable=False)
    mime_type: Mapped[str] = mapped_column(String(100), default="application/vnd.openxmlformats-officedocument.wordprocessingml.document")
    is_default: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    uploaded_by: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    version: Mapped["ConfigurationVersion"] = relationship("ConfigurationVersion")
    uploader: Mapped["User"] = relationship("User")
    
    __table_args__ = (
        Index("idx_templates_version", "version_id"),
        Index("idx_templates_default", "is_default"),
        Index("idx_templates_active", "is_active"),
        Index("idx_templates_uploaded_by", "uploaded_by"),
        UniqueConstraint("version_id", "name", name="uq_template_name_version"),
    )


class AuditLog(Base):
    """Audit log for tracking configuration changes."""
    __tablename__ = "audit_logs"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)
    action: Mapped[str] = mapped_column(String(50), nullable=False)  # create, update, delete, publish, etc.
    entity_type: Mapped[str] = mapped_column(String(50), nullable=False)  # user, category, subject, etc.
    entity_id: Mapped[Optional[int]] = mapped_column(Integer)
    old_values: Mapped[Optional[dict]] = mapped_column(JSON)
    new_values: Mapped[Optional[dict]] = mapped_column(JSON)
    description: Mapped[Optional[str]] = mapped_column(Text)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45))
    user_agent: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user: Mapped["User"] = relationship("User")
    
    __table_args__ = (
        Index("idx_audit_logs_user", "user_id"),
        Index("idx_audit_logs_action", "action"),
        Index("idx_audit_logs_entity", "entity_type", "entity_id"),
        Index("idx_audit_logs_created_at", "created_at"),
    )
