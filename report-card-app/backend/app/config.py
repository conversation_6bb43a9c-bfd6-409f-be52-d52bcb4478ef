"""
Configuration management for the Report Card Backend Service.

This module handles all environment variable configuration and provides
a centralized configuration object for the application.
"""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Required settings
    template_path: str = Field(..., description="Path to the Word template file")
    allowed_origins: str = Field(..., description="Comma-separated list of allowed CORS origins")
    host: str = Field(default="0.0.0.0", description="Host to bind the server")
    port: int = Field(default=8000, description="Port to run the server")
    log_level: str = Field(default="INFO", description="Logging level")
    output_filename_pattern: str = Field(
        default="report_{student}_{timestamp}.docx",
        description="Pattern for generated filenames"
    )
    max_output_size_mb: int = Field(default=10, description="Maximum output file size in MB")
    
    # Optional settings
    enable_image_support: bool = Field(default=False, description="Enable image support in templates")
    template_cache: bool = Field(default=False, description="Enable template caching")
    auth_required: bool = Field(default=False, description="Require authentication")
    max_request_size_mb: int = Field(default=5, description="Maximum request size in MB")
    template_reload_interval: int = Field(default=0, description="Template reload interval in seconds")
    
    # Development settings
    debug: bool = Field(default=False, description="Enable debug mode")
    log_requests: bool = Field(default=True, description="Log all requests")
    enable_profiling: bool = Field(default=False, description="Enable performance profiling")
    
    # Production settings
    workers: int = Field(default=1, description="Number of worker processes")
    worker_class: str = Field(default="uvicorn.workers.UvicornWorker", description="Worker class")
    keep_alive: int = Field(default=2, description="Keep-alive timeout")
    max_requests: int = Field(default=0, description="Maximum requests per worker")
    max_requests_jitter: int = Field(default=100, description="Maximum request jitter")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=False, description="Enable metrics endpoint")
    app_name: str = Field(default="report-card-backend", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    environment: str = Field(default="development", description="Environment name")
    
    # Security settings
    trusted_hosts: Optional[str] = Field(default=None, description="Comma-separated trusted hosts")
    force_https: bool = Field(default=False, description="Force HTTPS redirect")
    cors_allow_credentials: bool = Field(default=False, description="Allow CORS credentials")
    cors_allow_methods: str = Field(default="GET,POST,OPTIONS", description="Allowed CORS methods")
    cors_allow_headers: str = Field(default="Content-Type,Authorization", description="Allowed CORS headers")
    
    # Authentication settings (if auth_required=True)
    jwt_secret_key: Optional[str] = Field(default=None, description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expiration_hours: int = Field(default=24, description="JWT expiration in hours")
    
    # Rate limiting
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per window")
    rate_limit_window_minutes: int = Field(default=15, description="Rate limit window in minutes")

    # Database settings
    database_url: str = Field(
        default="sqlite+aiosqlite:///./report_card.db",
        description="Database URL for SQLAlchemy"
    )
    database_echo: bool = Field(default=False, description="Enable SQLAlchemy query logging")
    database_pool_size: int = Field(default=5, description="Database connection pool size")
    database_max_overflow: int = Field(default=10, description="Database max overflow connections")

    # JWT configuration
    jwt_secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for JWT token signing"
    )
    jwt_algorithm: str = Field(
        default="HS256",
        description="JWT signing algorithm"
    )
    jwt_expiration_hours: int = Field(
        default=24,
        description="JWT token expiration time in hours"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("template_path")
    def validate_template_path(cls, v):
        """Validate that the template path exists."""
        path = Path(v)
        if not path.is_absolute():
            # Make relative paths relative to the app directory
            app_dir = Path(__file__).parent.parent
            path = app_dir / v
        
        if not path.exists():
            raise ValueError(f"Template file not found: {path}")
        
        if not path.suffix.lower() == ".docx":
            raise ValueError(f"Template file must be a .docx file: {path}")
        
        return str(path)
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARN", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}. Must be one of {valid_levels}")
        return v.upper()
    
    @validator("max_output_size_mb", "max_request_size_mb")
    def validate_size_limits(cls, v):
        """Validate size limits are positive."""
        if v <= 0:
            raise ValueError("Size limits must be positive integers")
        return v
    
    @validator("port")
    def validate_port(cls, v):
        """Validate port number."""
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v
    
    @property
    def allowed_origins_list(self) -> List[str]:
        """Get allowed origins as a list."""
        return [origin.strip() for origin in self.allowed_origins.split(",") if origin.strip()]
    
    @property
    def trusted_hosts_list(self) -> Optional[List[str]]:
        """Get trusted hosts as a list."""
        if not self.trusted_hosts:
            return None
        return [host.strip() for host in self.trusted_hosts.split(",") if host.strip()]
    
    @property
    def cors_allow_methods_list(self) -> List[str]:
        """Get CORS allowed methods as a list."""
        return [method.strip() for method in self.cors_allow_methods.split(",") if method.strip()]
    
    @property
    def cors_allow_headers_list(self) -> List[str]:
        """Get CORS allowed headers as a list."""
        return [header.strip() for header in self.cors_allow_headers.split(",") if header.strip()]
    
    @property
    def max_output_size_bytes(self) -> int:
        """Get maximum output size in bytes."""
        return self.max_output_size_mb * 1024 * 1024
    
    @property
    def max_request_size_bytes(self) -> int:
        """Get maximum request size in bytes."""
        return self.max_request_size_mb * 1024 * 1024


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings."""
    return settings
