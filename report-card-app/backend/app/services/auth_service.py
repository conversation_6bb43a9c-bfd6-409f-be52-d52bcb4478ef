"""
Authentication service for the Report Card Backend Service.

This module handles user authentication, JWT token management, and role-based access control.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional, Union
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..database.models import User
from ..database.connection import get_database
from ..models import TokenData, UserInfo
from ..utils.security import verify_password, create_access_token, verify_token
from ..config import get_settings


security = HTTPBearer()


class AuthService:
    """Service for handling authentication operations."""
    
    def __init__(self):
        self.settings = get_settings()
    
    async def authenticate_user(self, session: AsyncSession, username: str, password: str) -> Optional[User]:
        """Authenticate a user with username and password."""
        # Get user from database
        result = await session.execute(
            select(User).where(
                User.username == username.lower(),
                User.is_active == True
            )
        )
        user = result.scalar_one_or_none()

        if user:
            # Verify password for existing users
            if verify_password(password, user.hashed_password):
                return user
            else:
                return None

        # If user doesn't exist and it's not admin credentials, create a teacher account
        if username.lower() != 'admin':
            # Create a temporary teacher user (not stored in database)
            teacher_user = User(
                id=0,  # Temporary ID for teachers
                username=username.lower(),
                full_name=username.title(),
                email=None,
                role="teacher",
                is_active=True,
                hashed_password=""  # Not used for temporary teachers
            )
            return teacher_user

        return None
    
    async def authenticate_teacher(self, teacher_name: str) -> UserInfo:
        """Authenticate a teacher with just their name (for demo purposes)."""
        # For teachers, we create a temporary user info without database storage
        return UserInfo(
            id=0,  # Temporary ID for teachers
            username=f"teacher_{teacher_name.lower().replace(' ', '_')}",
            full_name=teacher_name,
            email=None,
            role="teacher",
            is_active=True
        )
    
    def create_access_token_for_user(self, user: Union[User, UserInfo]) -> dict:
        """Create an access token for a user."""
        # Prepare token data
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "role": user.role,
            "full_name": getattr(user, 'full_name', None)
        }
        
        # Create token
        access_token = create_access_token(token_data)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": self.settings.jwt_expiration_hours * 3600,
            "user": UserInfo(
                id=user.id,
                username=user.username,
                full_name=getattr(user, 'full_name', None),
                email=getattr(user, 'email', None),
                role=user.role,
                is_active=getattr(user, 'is_active', True)
            )
        }
    
    async def get_current_user(self, session: AsyncSession, token: str) -> User:
        """Get the current user from JWT token."""
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        try:
            # Verify and decode token
            payload = verify_token(token)
            username: str = payload.get("sub")
            user_id: int = payload.get("user_id")
            
            if username is None or user_id is None:
                raise credentials_exception
            
            token_data = TokenData(username=username, user_id=user_id)
        except Exception:
            raise credentials_exception
        
        # For teacher tokens (user_id = 0), return a temporary user
        if user_id == 0:
            return User(
                id=0,
                username=username,
                full_name=payload.get("full_name"),
                role="teacher",
                is_active=True,
                hashed_password=""  # Not used for teachers
            )
        
        # Get user from database for admin users
        result = await session.execute(
            select(User).where(
                User.id == user_id,
                User.username == username,
                User.is_active == True
            )
        )
        user = result.scalar_one_or_none()
        
        if user is None:
            raise credentials_exception
        
        return user
    
    async def require_admin(self, current_user: User) -> User:
        """Require admin role for the current user."""
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        return current_user
    
    async def require_active_user(self, current_user: User) -> User:
        """Require active user."""
        if not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Inactive user"
            )
        return current_user


# Global auth service instance
auth_service = AuthService()


# Dependency functions for FastAPI
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: AsyncSession = Depends(get_database)
) -> User:
    """FastAPI dependency to get current user from token."""
    return await auth_service.get_current_user(session, credentials.credentials)


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """FastAPI dependency to get current active user."""
    return await auth_service.require_active_user(current_user)


async def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """FastAPI dependency to get current admin user."""
    return await auth_service.require_admin(current_user)


# Optional authentication (for endpoints that work with or without auth)
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    session: AsyncSession = Depends(get_database)
) -> Optional[User]:
    """FastAPI dependency to optionally get current user from token."""
    if not credentials:
        return None
    
    try:
        return await auth_service.get_current_user(session, credentials.credentials)
    except HTTPException:
        return None
