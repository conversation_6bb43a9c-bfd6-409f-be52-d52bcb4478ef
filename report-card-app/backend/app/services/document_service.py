"""
Document generation service using python-docx-template.

This module handles the core logic for rendering Word documents from templates
using JSON data from the React frontend.
"""

import io
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, BinaryIO

from docxtpl import DocxTemplate
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

from ..config import get_settings
from ..models import DocumentGenerationRequest
from ..utils.logging import get_logger, log_document_generation
from ..utils.security import generate_safe_filename, validate_file_size


class DocumentGenerationError(Exception):
    """Custom exception for document generation errors."""
    pass


class TemplateNotFoundError(DocumentGenerationError):
    """Exception raised when template file is not found."""
    pass


class TemplateCacheManager:
    """Manages template caching if enabled."""
    
    def __init__(self):
        self._cache: Dict[str, DocxTemplate] = {}
        self._last_modified: Dict[str, float] = {}
        self.settings = get_settings()
        self.logger = get_logger("template_cache")
    
    def get_template(self, template_path: str) -> DocxTemplate:
        """Get template from cache or load from file."""
        if not self.settings.template_cache:
            return self._load_template(template_path)
        
        path_obj = Path(template_path)
        
        # Check if file exists
        if not path_obj.exists():
            raise TemplateNotFoundError(f"Template file not found: {template_path}")
        
        # Get file modification time
        current_mtime = path_obj.stat().st_mtime
        
        # Check if we need to reload
        if (template_path not in self._cache or 
            self._last_modified.get(template_path, 0) < current_mtime):
            
            self.logger.info("Loading template", template_path=template_path)
            self._cache[template_path] = self._load_template(template_path)
            self._last_modified[template_path] = current_mtime
        
        return self._cache[template_path]
    
    def _load_template(self, template_path: str) -> DocxTemplate:
        """Load template from file."""
        try:
            return DocxTemplate(template_path)
        except Exception as e:
            raise DocumentGenerationError(f"Failed to load template: {e}")
    
    def clear_cache(self):
        """Clear the template cache."""
        self._cache.clear()
        self._last_modified.clear()
        self.logger.info("Template cache cleared")


class DocumentGenerationService:
    """Service for generating Word documents from templates."""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("document_service")
        self.template_cache = TemplateCacheManager()
    
    async def generate_document(
        self, 
        request_data: DocumentGenerationRequest,
        request_id: str
    ) -> tuple[BinaryIO, str]:
        """
        Generate a Word document from the request data.
        
        Args:
            request_data: The validated request data
            request_id: Unique request identifier
            
        Returns:
            Tuple of (file_stream, filename)
            
        Raises:
            HTTPException: For various error conditions
        """
        start_time = time.time()
        
        try:
            # Get template path
            template_path = self._get_template_path(request_data.template_id)
            
            # Load template
            template = self.template_cache.get_template(template_path)
            
            # Prepare context data
            context = self._prepare_context(request_data)
            
            # Render document
            document_stream = await self._render_document(template, context)
            
            # Generate filename
            filename = self._generate_filename(request_data)
            
            # Validate output size
            document_size = len(document_stream.getvalue())
            validate_file_size(document_size, self.settings.max_output_size_bytes)
            
            # Log success
            duration = time.time() - start_time
            log_document_generation(
                request_id=request_id,
                student_name=request_data.student,
                template_path=template_path,
                output_size=document_size,
                duration=duration,
                success=True
            )
            
            # Reset stream position
            document_stream.seek(0)
            
            return document_stream, filename
            
        except TemplateNotFoundError as e:
            duration = time.time() - start_time
            log_document_generation(
                request_id=request_id,
                student_name=request_data.student,
                template_path=self.settings.template_path,
                output_size=0,
                duration=duration,
                success=False,
                error=str(e)
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template not found: {e}"
            )
            
        except DocumentGenerationError as e:
            duration = time.time() - start_time
            log_document_generation(
                request_id=request_id,
                student_name=request_data.student,
                template_path=self.settings.template_path,
                output_size=0,
                duration=duration,
                success=False,
                error=str(e)
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Document generation failed: {e}"
            )
            
        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(
                "Unexpected error during document generation",
                request_id=request_id,
                error=str(e),
                error_type=type(e).__name__
            )
            log_document_generation(
                request_id=request_id,
                student_name=request_data.student,
                template_path=self.settings.template_path,
                output_size=0,
                duration=duration,
                success=False,
                error=f"Unexpected error: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred during document generation"
            )
    
    def _get_template_path(self, template_id: Optional[str] = None) -> str:
        """Get the template file path."""
        if template_id:
            # Future feature: support multiple templates
            # For now, just use the default template
            self.logger.warning(
                "Template ID provided but multiple templates not yet supported",
                template_id=template_id
            )
        
        return self.settings.template_path
    
    def _prepare_context(self, request_data: DocumentGenerationRequest) -> Dict[str, Any]:
        """Prepare the context data for template rendering."""
        # Convert the request data to a dictionary suitable for Jinja2
        context = {
            "assessment": request_data.assessment,
            "student": request_data.student,
            "sections": {}
        }
        
        # Add sections data
        if request_data.sections:
            sections_dict = request_data.sections.dict()
            
            # Handle the main sections
            for key, value in sections_dict.items():
                if value is not None:
                    context["sections"][key] = value
        
        # Add optional fields if present
        if request_data.teacher:
            context["teacher"] = request_data.teacher
        if request_data.grade:
            context["grade"] = request_data.grade
        if request_data.school:
            context["school"] = request_data.school
        if request_data.date:
            context["date"] = request_data.date
        else:
            # Add current date if not provided
            context["date"] = datetime.now().strftime("%B %d, %Y")
        
        # Add metadata
        context["generated_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        context["app_name"] = self.settings.app_name
        context["app_version"] = self.settings.app_version
        
        self.logger.debug(
            "Prepared template context",
            student=request_data.student,
            sections_count=len(context["sections"]),
            has_teacher=bool(request_data.teacher),
            has_grade=bool(request_data.grade)
        )
        
        return context
    
    async def _render_document(self, template: DocxTemplate, context: Dict[str, Any]) -> io.BytesIO:
        """Render the document with the provided context."""
        try:
            # Render the template
            template.render(context)
            
            # Save to memory stream
            document_stream = io.BytesIO()
            template.save(document_stream)
            
            return document_stream
            
        except Exception as e:
            raise DocumentGenerationError(f"Template rendering failed: {e}")
    
    def _generate_filename(self, request_data: DocumentGenerationRequest) -> str:
        """Generate a safe filename for the document."""
        try:
            filename = generate_safe_filename(
                self.settings.output_filename_pattern,
                student=request_data.student,
                assessment=request_data.assessment,
                timestamp=datetime.now().strftime("%Y%m%d_%H%M%S")
            )
            
            # Ensure .docx extension
            if not filename.lower().endswith('.docx'):
                filename += '.docx'
            
            return filename
            
        except Exception as e:
            # Fallback to a simple filename
            self.logger.warning(
                "Failed to generate filename from pattern, using fallback",
                error=str(e),
                pattern=self.settings.output_filename_pattern
            )
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"report_{timestamp}.docx"
    
    def check_template_health(self) -> Dict[str, Any]:
        """Check if the template is accessible and valid."""
        try:
            template_path = self.settings.template_path
            path_obj = Path(template_path)
            
            if not path_obj.exists():
                return {
                    "template_loaded": False,
                    "error": f"Template file not found: {template_path}"
                }
            
            # Try to load the template
            template = self.template_cache.get_template(template_path)
            
            # Get template info
            file_size = path_obj.stat().st_size
            modified_time = datetime.fromtimestamp(path_obj.stat().st_mtime)
            
            return {
                "template_loaded": True,
                "template_path": template_path,
                "file_size_bytes": file_size,
                "last_modified": modified_time.isoformat(),
                "cache_enabled": self.settings.template_cache
            }
            
        except Exception as e:
            return {
                "template_loaded": False,
                "error": f"Template validation failed: {e}"
            }
