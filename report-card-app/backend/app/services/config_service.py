"""
Configuration service for the Report Card Backend Service.

This module handles configuration management operations including CRUD operations
for categories, subjects, assessment points, and school settings.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, delete, update
from sqlalchemy.orm import selectinload

from ..database.models import (
    Category, Subject, AssessmentPoint, SchoolSettings, Grade,
    ConfigurationVersion, AuditLog, User
)
from ..models import (
    CategoryRequest, SubjectRequest, AssessmentPointRequest,
    SchoolSettingsRequest, GradeRequest, ConfigurationVersionRequest,
    BulkImportRequest, ConfigurationStatsResponse
)
from ..utils.logging import get_logger

logger = get_logger(__name__)


class ConfigurationService:
    """Service for handling configuration operations."""
    
    async def create_category(
        self,
        session: AsyncSession,
        category_data: CategoryRequest,
        user_id: int
    ) -> Category:
        """Create a new category."""
        # Get current version (for now, we'll use version_id = 1)
        version_id = 1

        category = Category(
            version_id=version_id,
            name=category_data.name,
            display_name=category_data.display_name,
            category_type=category_data.category_type,
            description=category_data.description,
            sort_order=category_data.sort_order,
            is_active=category_data.is_active
        )
        
        session.add(category)
        await session.commit()
        await session.refresh(category)
        
        # Log the action
        await self._log_action(
            session, user_id, "create", "category", 
            category.id, f"Created category: {category.name}"
        )
        
        logger.info(f"Category created: {category.name} by user {user_id}")
        return category
    
    async def get_categories(
        self, 
        session: AsyncSession, 
        category_type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Category]:
        """Get categories with optional filtering."""
        query = select(Category)
        
        filters = []
        if category_type:
            filters.append(Category.category_type == category_type)
        if is_active is not None:
            filters.append(Category.is_active == is_active)
        
        if filters:
            query = query.where(and_(*filters))
        
        query = query.order_by(Category.sort_order, Category.name)
        
        result = await session.execute(query)
        return result.scalars().all()
    
    async def get_category(self, session: AsyncSession, category_id: int) -> Optional[Category]:
        """Get a category by ID."""
        result = await session.execute(
            select(Category).where(Category.id == category_id)
        )
        return result.scalar_one_or_none()
    
    async def update_category(
        self, 
        session: AsyncSession, 
        category_id: int, 
        category_data: CategoryRequest, 
        user_id: int
    ) -> Optional[Category]:
        """Update a category."""
        category = await self.get_category(session, category_id)
        if not category:
            return None
        
        # Update fields
        category.name = category_data.name
        category.display_name = category_data.display_name
        category.category_type = category_data.category_type
        category.description = category_data.description
        category.sort_order = category_data.sort_order
        category.is_active = category_data.is_active
        category.updated_at = datetime.utcnow()
        
        await session.commit()
        await session.refresh(category)
        
        # Log the action
        await self._log_action(
            session, user_id, "update", "category", 
            category.id, f"Updated category: {category.name}"
        )
        
        logger.info(f"Category updated: {category.name} by user {user_id}")
        return category
    
    async def delete_category(
        self, 
        session: AsyncSession, 
        category_id: int, 
        user_id: int
    ) -> bool:
        """Delete a category."""
        category = await self.get_category(session, category_id)
        if not category:
            return False
        
        # Check if category has associated assessment points
        result = await session.execute(
            select(func.count(AssessmentPoint.id)).where(
                AssessmentPoint.category_id == category_id
            )
        )
        point_count = result.scalar()
        
        if point_count > 0:
            # Soft delete by marking as inactive
            category.is_active = False
            category.updated_at = datetime.utcnow()
            await session.commit()
            
            action_desc = f"Deactivated category: {category.name} (had {point_count} assessment points)"
        else:
            # Hard delete
            await session.delete(category)
            await session.commit()
            
            action_desc = f"Deleted category: {category.name}"
        
        # Log the action
        await self._log_action(
            session, user_id, "delete", "category", 
            category.id, action_desc
        )
        
        logger.info(f"Category deleted/deactivated: {category.name} by user {user_id}")
        return True
    
    async def create_subject(
        self,
        session: AsyncSession,
        subject_data: SubjectRequest,
        user_id: int
    ) -> Subject:
        """Create a new subject."""
        # Get current version (for now, we'll use version_id = 1)
        version_id = 1

        subject = Subject(
            version_id=version_id,
            name=subject_data.name,
            display_name=subject_data.display_name,
            grade_from=subject_data.grade_from,
            grade_to=subject_data.grade_to,
            sort_order=subject_data.sort_order,
            is_active=subject_data.is_active
        )
        
        session.add(subject)
        await session.commit()
        await session.refresh(subject)
        
        # Log the action
        await self._log_action(
            session, user_id, "create", "subject", 
            subject.id, f"Created subject: {subject.name}"
        )
        
        logger.info(f"Subject created: {subject.name} by user {user_id}")
        return subject
    
    async def get_subjects(
        self, 
        session: AsyncSession, 
        grade: Optional[int] = None,
        is_active: Optional[bool] = None
    ) -> List[Subject]:
        """Get subjects with optional filtering."""
        query = select(Subject)
        
        filters = []
        if grade:
            filters.append(and_(
                Subject.grade_from <= grade,
                Subject.grade_to >= grade
            ))
        if is_active is not None:
            filters.append(Subject.is_active == is_active)
        
        if filters:
            query = query.where(and_(*filters))
        
        query = query.order_by(Subject.sort_order, Subject.name)
        
        result = await session.execute(query)
        return result.scalars().all()
    
    async def get_subject(self, session: AsyncSession, subject_id: int) -> Optional[Subject]:
        """Get a subject by ID."""
        result = await session.execute(
            select(Subject).where(Subject.id == subject_id)
        )
        return result.scalar_one_or_none()
    
    async def create_assessment_point(
        self,
        session: AsyncSession,
        point_data: AssessmentPointRequest,
        user_id: int
    ) -> AssessmentPoint:
        """Create a new assessment point."""
        # Get current version (for now, we'll use version_id = 1)
        version_id = 1

        point = AssessmentPoint(
            version_id=version_id,
            category_id=point_data.category_id,
            subject_id=point_data.subject_id,
            point_text=point_data.name,  # Use name field as point_text
            point_type=point_data.point_type,
            grade_from=point_data.grade_from,
            grade_to=point_data.grade_to,
            sort_order=point_data.sort_order,
            is_active=point_data.is_active
        )
        
        session.add(point)
        await session.commit()
        await session.refresh(point)
        
        # Log the action
        await self._log_action(
            session, user_id, "create", "assessment_point", 
            point.id, f"Created assessment point: {point.point_text[:50]}..."
        )
        
        logger.info(f"Assessment point created: {point.id} by user {user_id}")
        return point
    
    async def get_assessment_points(
        self, 
        session: AsyncSession, 
        category_id: Optional[int] = None,
        subject_id: Optional[int] = None,
        point_type: Optional[str] = None,
        grade: Optional[int] = None,
        is_active: Optional[bool] = None
    ) -> List[AssessmentPoint]:
        """Get assessment points with optional filtering."""
        query = select(AssessmentPoint).options(
            selectinload(AssessmentPoint.category),
            selectinload(AssessmentPoint.subject)
        )
        
        filters = []
        if category_id:
            filters.append(AssessmentPoint.category_id == category_id)
        if subject_id:
            filters.append(AssessmentPoint.subject_id == subject_id)
        if point_type:
            filters.append(AssessmentPoint.point_type == point_type)
        if grade:
            filters.append(or_(
                and_(
                    AssessmentPoint.grade_from.is_(None),
                    AssessmentPoint.grade_to.is_(None)
                ),
                and_(
                    AssessmentPoint.grade_from <= grade,
                    AssessmentPoint.grade_to >= grade
                )
            ))
        if is_active is not None:
            filters.append(AssessmentPoint.is_active == is_active)
        
        if filters:
            query = query.where(and_(*filters))
        
        query = query.order_by(AssessmentPoint.sort_order, AssessmentPoint.id)
        
        result = await session.execute(query)
        return result.scalars().all()
    
    async def get_configuration_stats(self, session: AsyncSession) -> ConfigurationStatsResponse:
        """Get configuration statistics."""
        # Count categories
        total_categories = await session.scalar(select(func.count(Category.id)))
        active_categories = await session.scalar(
            select(func.count(Category.id)).where(Category.is_active == True)
        )
        
        # Count subjects
        total_subjects = await session.scalar(select(func.count(Subject.id)))
        active_subjects = await session.scalar(
            select(func.count(Subject.id)).where(Subject.is_active == True)
        )
        
        # Count assessment points
        total_points = await session.scalar(select(func.count(AssessmentPoint.id)))
        active_points = await session.scalar(
            select(func.count(AssessmentPoint.id)).where(AssessmentPoint.is_active == True)
        )

        # Count grades
        total_grades = await session.scalar(select(func.count(Grade.id)))
        active_grades = await session.scalar(
            select(func.count(Grade.id)).where(Grade.is_active == True)
        )

        # Get current version
        current_version_result = await session.execute(
            select(ConfigurationVersion.version_name).where(
                ConfigurationVersion.is_current == True
            )
        )
        current_version = current_version_result.scalar_one_or_none()
        
        # Get last updated timestamp
        last_updated_result = await session.execute(
            select(func.max(Category.updated_at))
        )
        last_updated = last_updated_result.scalar()
        
        return ConfigurationStatsResponse(
            total_categories=total_categories or 0,
            total_subjects=total_subjects or 0,
            total_assessment_points=total_points or 0,
            total_grades=total_grades or 0,
            active_categories=active_categories or 0,
            active_subjects=active_subjects or 0,
            active_assessment_points=active_points or 0,
            active_grades=active_grades or 0,
            current_version=current_version,
            last_updated=last_updated
        )
    
    async def migrate_csv_data(self, session: AsyncSession, user_id: int) -> dict:
        """Migrate data from CSV files to database."""
        from ..database.migration_utils import run_initial_migration

        results = {
            "migration_completed": False,
            "errors": []
        }

        try:
            # Run the complete migration
            await run_initial_migration()
            results["migration_completed"] = True

            # Log the migration
            await self._log_action(
                session, user_id, "migrate", "csv_data",
                0, "Completed CSV data migration"
            )

            logger.info(f"CSV data migration completed by user {user_id}")

        except Exception as e:
            error_msg = f"CSV migration failed: {str(e)}"
            results["errors"].append(error_msg)
            logger.error(error_msg)

        return results

    async def _log_action(
        self,
        session: AsyncSession,
        user_id: int,
        action: str,
        entity_type: str,
        entity_id: int,
        description: str
    ):
        """Log an audit action."""
        audit_log = AuditLog(
            user_id=user_id,
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            description=description
        )
        session.add(audit_log)
        # Note: Don't commit here as this is called within other transactions

    # Grade Management Methods

    async def create_grade(
        self,
        session: AsyncSession,
        grade_data: GradeRequest,
        user_id: int
    ) -> Grade:
        """Create a new grade."""
        # Get current version (for now, we'll use version_id = 1)
        version_id = 1

        grade = Grade(
            version_id=version_id,
            grade_level=grade_data.grade_level,
            grade_name=grade_data.grade_name,
            grade_code=grade_data.grade_code,
            description=grade_data.description,
            age_range=grade_data.age_range,
            sort_order=grade_data.sort_order,
            is_active=grade_data.is_active
        )

        session.add(grade)
        await session.commit()
        await session.refresh(grade)

        # Log the action
        await self._log_action(session, user_id, "CREATE", "Grade", grade.id,
                             f"Created grade: {grade.grade_name}")

        return grade

    async def get_grades(
        self,
        session: AsyncSession,
        is_active: Optional[bool] = None
    ) -> List[Grade]:
        """Get all grades with optional filtering."""
        query = select(Grade).where(Grade.version_id == 1)

        if is_active is not None:
            query = query.where(Grade.is_active == is_active)

        query = query.order_by(Grade.sort_order, Grade.grade_level)

        result = await session.execute(query)
        return result.scalars().all()

    async def get_grade(self, session: AsyncSession, grade_id: int) -> Optional[Grade]:
        """Get a specific grade by ID."""
        query = select(Grade).where(and_(Grade.id == grade_id, Grade.version_id == 1))
        result = await session.execute(query)
        return result.scalar_one_or_none()

    async def update_grade(
        self,
        session: AsyncSession,
        grade_id: int,
        grade_data: GradeRequest,
        user_id: int
    ) -> Optional[Grade]:
        """Update an existing grade."""
        grade = await self.get_grade(session, grade_id)
        if not grade:
            return None

        # Update fields
        grade.grade_level = grade_data.grade_level
        grade.grade_name = grade_data.grade_name
        grade.grade_code = grade_data.grade_code
        grade.description = grade_data.description
        grade.age_range = grade_data.age_range
        grade.sort_order = grade_data.sort_order
        grade.is_active = grade_data.is_active

        await session.commit()
        await session.refresh(grade)

        # Log the action
        await self._log_action(session, user_id, "UPDATE", "Grade", grade.id,
                             f"Updated grade: {grade.grade_name}")

        return grade

    async def delete_grade(self, session: AsyncSession, grade_id: int) -> bool:
        """Delete a grade."""
        grade = await self.get_grade(session, grade_id)
        if not grade:
            return False

        await session.delete(grade)
        await session.commit()
        return True

    # Version Management Methods

    async def get_versions(self, session: AsyncSession) -> List[ConfigurationVersion]:
        """Get all configuration versions."""
        query = select(ConfigurationVersion).order_by(ConfigurationVersion.created_at.desc())
        result = await session.execute(query)
        return result.scalars().all()

    async def create_version(
        self,
        session: AsyncSession,
        version_data: ConfigurationVersionRequest,
        user_id: int
    ) -> ConfigurationVersion:
        """Create a new configuration version."""
        version = ConfigurationVersion(
            version_name=version_data.version_name,
            description=version_data.description,
            is_published=version_data.is_published,
            is_current=version_data.is_current,
            created_by=user_id
        )

        # If this version is set as current, unset all other current versions
        if version_data.is_current:
            # Update all existing current versions to false
            await session.execute(
                update(ConfigurationVersion).where(
                    ConfigurationVersion.is_current == True
                ).values(is_current=False)
            )

        session.add(version)
        await session.commit()
        await session.refresh(version)

        # Log the action
        await self._log_action(session, user_id, "CREATE", "ConfigurationVersion", version.id,
                             f"Created version: {version.version_name}")

        return version

    async def publish_version(
        self,
        session: AsyncSession,
        version_id: int,
        user_id: int
    ) -> bool:
        """Publish a configuration version."""
        query = select(ConfigurationVersion).where(ConfigurationVersion.id == version_id)
        result = await session.execute(query)
        version = result.scalar_one_or_none()

        if not version:
            return False

        version.is_published = True
        version.published_at = datetime.utcnow()

        await session.commit()

        # Log the action
        await self._log_action(session, user_id, "PUBLISH", "ConfigurationVersion", version.id,
                             f"Published version: {version.version_name}")

        return True

    async def set_current_version(
        self,
        session: AsyncSession,
        version_id: int,
        user_id: int
    ) -> bool:
        """Set a version as the current version."""
        query = select(ConfigurationVersion).where(ConfigurationVersion.id == version_id)
        result = await session.execute(query)
        version = result.scalar_one_or_none()

        if not version:
            return False

        # Unset all other current versions
        await session.execute(
            update(ConfigurationVersion).where(
                ConfigurationVersion.is_current == True
            ).values(is_current=False)
        )

        # Set this version as current
        version.is_current = True

        await session.commit()

        # Log the action
        await self._log_action(session, user_id, "SET_CURRENT", "ConfigurationVersion", version.id,
                             f"Set version as current: {version.version_name}")

        return True


# Global configuration service instance
config_service = ConfigurationService()
