"""
Security utilities for the Report Card Backend Service.

This module provides security-related functions including filename sanitization,
input validation, and authentication helpers.
"""

import re
import secrets
import string
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Optional

from fastapi import HTTPException, status
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext

from ..config import get_settings


# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def sanitize_filename(filename: str, max_length: int = 100) -> str:
    """
    Sanitize a filename to prevent path traversal and other security issues.
    
    Args:
        filename: The filename to sanitize
        max_length: Maximum allowed filename length
        
    Returns:
        Sanitized filename safe for use
        
    Raises:
        ValueError: If filename is invalid or too long
    """
    if not filename or not filename.strip():
        raise ValueError("Filename cannot be empty")
    
    # Remove any path components
    filename = Path(filename).name
    
    # Remove or replace dangerous characters
    # Allow only alphanumeric, spaces, hyphens, underscores, and periods
    sanitized = re.sub(r'[^\w\s\-_.]', '', filename)
    
    # Replace multiple spaces/underscores with single ones
    sanitized = re.sub(r'[\s_]+', '_', sanitized)
    
    # Remove leading/trailing spaces and dots
    sanitized = sanitized.strip(' .')
    
    # Ensure it's not empty after sanitization
    if not sanitized:
        raise ValueError("Filename becomes empty after sanitization")
    
    # Check length
    if len(sanitized) > max_length:
        # Keep the extension if present
        path_obj = Path(sanitized)
        if path_obj.suffix:
            max_stem_length = max_length - len(path_obj.suffix)
            sanitized = path_obj.stem[:max_stem_length] + path_obj.suffix
        else:
            sanitized = sanitized[:max_length]
    
    # Ensure it doesn't start with a dot (hidden file)
    if sanitized.startswith('.'):
        sanitized = 'file_' + sanitized[1:]
    
    return sanitized


def generate_safe_filename(pattern: str, **kwargs) -> str:
    """
    Generate a safe filename using a pattern and provided variables.
    
    Args:
        pattern: Filename pattern with placeholders
        **kwargs: Variables to substitute in the pattern
        
    Returns:
        Safe filename
    """
    # Add timestamp if not provided
    if 'timestamp' not in kwargs:
        kwargs['timestamp'] = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
    
    # Sanitize all string values
    sanitized_kwargs = {}
    for key, value in kwargs.items():
        if isinstance(value, str):
            # Replace spaces with underscores and remove special chars
            sanitized_value = re.sub(r'[^\w\s\-]', '', str(value))
            sanitized_value = re.sub(r'\s+', '_', sanitized_value)
            sanitized_kwargs[key] = sanitized_value
        else:
            sanitized_kwargs[key] = value
    
    try:
        filename = pattern.format(**sanitized_kwargs)
    except KeyError as e:
        raise ValueError(f"Missing variable in filename pattern: {e}")
    
    return sanitize_filename(filename)


def validate_file_size(size_bytes: int, max_size_bytes: int) -> None:
    """
    Validate that a file size is within allowed limits.
    
    Args:
        size_bytes: File size in bytes
        max_size_bytes: Maximum allowed size in bytes
        
    Raises:
        HTTPException: If file is too large
    """
    if size_bytes > max_size_bytes:
        max_mb = max_size_bytes / (1024 * 1024)
        actual_mb = size_bytes / (1024 * 1024)
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large: {actual_mb:.1f}MB exceeds limit of {max_mb:.1f}MB"
        )


def validate_content_type(content_type: str, allowed_types: list) -> None:
    """
    Validate that content type is allowed.
    
    Args:
        content_type: The content type to validate
        allowed_types: List of allowed content types
        
    Raises:
        HTTPException: If content type is not allowed
    """
    if content_type not in allowed_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Content type '{content_type}' not allowed. Allowed types: {allowed_types}"
        )


def generate_request_id() -> str:
    """Generate a unique request ID."""
    return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time
        
    Returns:
        Encoded JWT token
    """
    settings = get_settings()
    
    if not settings.jwt_secret_key:
        raise ValueError("JWT_SECRET_KEY not configured")
    
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=settings.jwt_expiration_hours)
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.jwt_secret_key, 
        algorithm=settings.jwt_algorithm
    )
    
    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    Verify and decode a JWT token.
    
    Args:
        token: JWT token to verify
        
    Returns:
        Decoded token payload
        
    Raises:
        HTTPException: If token is invalid
    """
    settings = get_settings()
    
    if not settings.jwt_secret_key:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="JWT not configured"
        )
    
    try:
        payload = jwt.decode(
            token, 
            settings.jwt_secret_key, 
            algorithms=[settings.jwt_algorithm]
        )
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def hash_password(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def validate_input_length(value: str, field_name: str, max_length: int) -> None:
    """
    Validate input string length.
    
    Args:
        value: String value to validate
        field_name: Name of the field for error messages
        max_length: Maximum allowed length
        
    Raises:
        HTTPException: If value is too long
    """
    if len(value) > max_length:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{field_name} too long: {len(value)} characters exceeds limit of {max_length}"
        )


def sanitize_log_data(data: dict) -> dict:
    """
    Sanitize data for logging by removing sensitive information.
    
    Args:
        data: Dictionary to sanitize
        
    Returns:
        Sanitized dictionary safe for logging
    """
    sensitive_keys = {
        'password', 'token', 'secret', 'key', 'auth', 'credential',
        'jwt', 'session', 'cookie', 'authorization'
    }
    
    sanitized = {}
    for key, value in data.items():
        key_lower = key.lower()
        
        # Check if key contains sensitive information
        if any(sensitive in key_lower for sensitive in sensitive_keys):
            sanitized[key] = "[REDACTED]"
        elif isinstance(value, dict):
            sanitized[key] = sanitize_log_data(value)
        elif isinstance(value, str) and len(value) > 1000:
            # Truncate very long strings
            sanitized[key] = value[:1000] + "...[TRUNCATED]"
        else:
            sanitized[key] = value
    
    return sanitized
