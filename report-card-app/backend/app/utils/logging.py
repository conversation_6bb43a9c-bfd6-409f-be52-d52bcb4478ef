"""
Logging configuration and utilities.

This module sets up structured logging for the application with proper
formatting, log levels, and request tracking.
"""

import logging
import sys
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

import structlog
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from rich.console import Console
from rich.text import Text

from ..config import get_settings

# Initialize Rich console for colored output
console = Console(force_terminal=True, width=120)

# Color mapping for log levels
LEVEL_COLORS = {
    "DEBUG": "dim white",
    "INFO": "bright_blue",
    "WARNING": "yellow",
    "ERROR": "red",
    "CRITICAL": "bold red",
}

# Emoji mapping for log levels
LEVEL_EMOJIS = {
    "DEBUG": "🔍",
    "INFO": "ℹ️ ",
    "WARNING": "⚠️ ",
    "ERROR": "❌",
    "CRITICAL": "🚨",
}


def _colored_console_renderer(logger, method_name, event_dict):
    """Custom renderer for beautiful colored console output."""
    level = event_dict.get("level", "INFO").upper()
    timestamp = event_dict.get("timestamp", "")
    logger_name = event_dict.get("logger", "app")
    event = event_dict.get("event", "")

    # Create colored level indicator
    emoji = LEVEL_EMOJIS.get(level, "📝")
    color = LEVEL_COLORS.get(level, "white")

    # Format timestamp
    if timestamp:
        time_str = f"[dim]{timestamp}[/dim]"
    else:
        time_str = f"[dim]{datetime.now().strftime('%H:%M:%S')}[/dim]"

    # Format logger name
    logger_str = f"[dim cyan]{logger_name}[/dim cyan]"

    # Format level
    level_str = f"[{color}]{emoji} {level:8}[/{color}]"

    # Format main message
    message_parts = [time_str, level_str, logger_str, f"[white]{event}[/white]"]

    # Add extra fields
    extra_fields = []
    for key, value in event_dict.items():
        if key not in ["level", "timestamp", "logger", "event"]:
            if isinstance(value, str) and len(value) > 50:
                value = value[:47] + "..."
            extra_fields.append(f"[dim]{key}[/dim]=[bright_white]{value}[/bright_white]")

    if extra_fields:
        message_parts.append(" | ".join(extra_fields))

    # Join all parts
    full_message = " ".join(message_parts)

    # Print using Rich console
    console.print(full_message, markup=True)
    return ""


def configure_logging():
    """Configure beautiful, structured logging for the application."""
    settings = get_settings()

    # Determine if we should use colored output (development vs production)
    use_colors = settings.environment == "development"

    if use_colors:
        # Development: Beautiful colored console output
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                _colored_console_renderer,
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    else:
        # Production: Structured JSON logging
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level),
    )

    # Set third-party library log levels to reduce noise
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    logging.getLogger("alembic").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""
    
    def __init__(self, app, logger_name: str = "http"):
        super().__init__(app)
        self.logger = get_logger(logger_name)
        self.settings = get_settings()
    
    async def dispatch(self, request: Request, call_next):
        """Process request and log details."""
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Log request if enabled
        if self.settings.log_requests:
            await self._log_request(request, request_id)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response if enabled
            if self.settings.log_requests:
                await self._log_response(request, response, request_id, duration)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as exc:
            # Calculate duration
            duration = time.time() - start_time
            
            # Log error
            self.logger.error(
                "Request failed",
                request_id=request_id,
                method=request.method,
                url=str(request.url),
                duration=duration,
                error=str(exc),
                error_type=type(exc).__name__
            )
            
            raise
    
    async def _log_request(self, request: Request, request_id: str):
        """Log incoming request details."""
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Get content length
        content_length = request.headers.get("content-length", "0")
        
        self.logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=client_ip,
            user_agent=request.headers.get("user-agent"),
            content_length=int(content_length),
            content_type=request.headers.get("content-type")
        )
    
    async def _log_response(self, request: Request, response, request_id: str, duration: float):
        """Log response details."""
        self.logger.info(
            "Request completed",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            path=request.url.path,
            status_code=response.status_code,
            duration=round(duration, 4),
            response_size=response.headers.get("content-length", "unknown")
        )
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"


class RequestContextLogger:
    """Context manager for adding request context to logs."""
    
    def __init__(self, request: Request):
        self.request = request
        self.logger = get_logger("app")
    
    def __enter__(self):
        """Enter context and return logger with request context."""
        request_id = getattr(self.request.state, "request_id", "unknown")
        return self.logger.bind(
            request_id=request_id,
            method=self.request.method,
            path=self.request.url.path
        )
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context."""
        pass


def log_performance(func_name: str, duration: float, **kwargs):
    """Log performance metrics for a function."""
    logger = get_logger("performance")
    logger.info(
        "Function performance",
        function=func_name,
        duration=round(duration, 4),
        **kwargs
    )


def log_document_generation(
    request_id: str,
    student_name: str,
    template_path: str,
    output_size: int,
    duration: float,
    success: bool = True,
    error: Optional[str] = None
):
    """Log document generation metrics."""
    logger = get_logger("document_generation")
    
    log_data = {
        "request_id": request_id,
        "student_name": student_name,
        "template_path": template_path,
        "output_size_bytes": output_size,
        "duration": round(duration, 4),
        "success": success,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if error:
        log_data["error"] = error
    
    if success:
        logger.info("Document generated successfully", **log_data)
    else:
        logger.error("Document generation failed", **log_data)
