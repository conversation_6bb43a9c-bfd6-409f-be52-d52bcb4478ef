"""
<PERSON><PERSON> requests for testing the Report Card Backend Service.

This module contains example request data that can be used for testing
the document generation API.
"""

import json
import requests
from datetime import datetime


# Base URL for the backend service
BASE_URL = "http://localhost:8000"


def sample_full_request():
    """Complete sample request with all fields."""
    return {
        "assessment": "1st Term Assessment Profile 2023 - 2024",
        "student": "<PERSON> Johnson",
        "sections": {
            "Attitude_and_Responsibility": "She is a balanced individual and responds appropriately to the situations. She has a strong sense of right and wrong which shows in her actions. She works hard to boost her self-confidence. It is observed that teacher's appreciation has a positive outlook on her work. She is aware of her immediate goals. She is responsible with her belongings.",
            "Class_Room_Behaviour": "She shows commitment in the classroom to a large extent. She puts a sincere effort to ensure tasks are completed and deadlines are adhered to. She likes to share her perspective with others. When performing a task, she is a focused individual and gets immersed in her work. She is a quiet child but engages in conversation with her close peers.",
            "Social_and_Emotional_Behaviour": "She interacts freely with everyone. She supports and encourages her classmates with positive comments and actions. She possesses the sensitivity and sensibility to act in accordance with the current situation. She is able to identify the problems and sees her own role in them.",
            "Concept": "Her concepts are clear to the extent that she has focused in class. Hence, knows the content in brief. Effort to get deeper into the concept would enhance the quality of the answers.",
            "Subject_wise_Feedback": {
                "English_Language": "She comprehends questions well and provides detailed answers. Her vocabulary is age-appropriate and she expresses herself clearly.",
                "English_Literature": "She reads texts attentively and identifies key points effectively. She demonstrates good understanding of literary concepts.",
                "Mathematics": "She is thorough with mathematical concepts. She is quick and accurate with calculations. Her problem-solving approach is systematic.",
                "Science": "She shows keen interest in scientific concepts. Her observations are accurate and she can relate theory to practical applications.",
                "Social_Studies": "She demonstrates good understanding of social concepts and can relate historical events to current situations.",
                "Second_Language": "Her pronunciation is clear and she shows good progress in vocabulary building. She participates actively in language activities."
            }
        },
        "teacher": "Ms. Sarah Wilson",
        "grade": "Grade 5A",
        "school": "Greenwood Elementary School",
        "date": "December 15, 2023"
    }


def sample_minimal_request():
    """Minimal sample request with only required fields."""
    return {
        "assessment": "Mid-Term Assessment 2024",
        "student": "Bob Smith",
        "sections": {
            "Attitude_and_Responsibility": "He shows good attitude towards learning and takes responsibility for his actions."
        }
    }


def sample_high_school_request():
    """Sample request for high school student with science subjects."""
    return {
        "assessment": "Final Term Assessment 2023-2024",
        "student": "Emma Davis",
        "sections": {
            "Attitude_and_Responsibility": "She demonstrates excellent leadership qualities and takes initiative in group activities. She is punctual and well-organized in her approach to studies.",
            "Class_Room_Behaviour": "She maintains excellent classroom discipline and actively participates in discussions. She helps her peers and creates a positive learning environment.",
            "Social_and_Emotional_Behaviour": "She shows maturity in handling conflicts and demonstrates empathy towards others. She is confident in expressing her opinions while respecting others' viewpoints.",
            "Concept": "Her conceptual understanding is excellent across all subjects. She can apply theoretical knowledge to solve complex problems and shows analytical thinking.",
            "Subject_wise_Feedback": {
                "English_Language": "Excellent command over language with sophisticated vocabulary. Her essays are well-structured and demonstrate critical thinking.",
                "Mathematics": "Outstanding performance in advanced mathematics. She excels in calculus and statistics with perfect problem-solving techniques.",
                "Physics": "Exceptional understanding of physics concepts. Her laboratory work is precise and she can explain complex phenomena clearly.",
                "Chemistry": "Strong grasp of chemical principles and reactions. Her practical work shows attention to detail and safety protocols.",
                "Biology": "Excellent knowledge of biological systems. She shows particular interest in genetics and molecular biology.",
                "History": "Demonstrates deep understanding of historical events and their interconnections. Her analytical essays show mature thinking.",
                "Geography": "Good spatial understanding and map skills. She can relate geographical concepts to current environmental issues."
            }
        },
        "teacher": "Dr. Michael Thompson",
        "grade": "Grade 12 Science",
        "school": "Riverside High School",
        "date": "March 20, 2024"
    }


def sample_elementary_request():
    """Sample request for elementary student with basic subjects."""
    return {
        "assessment": "Spring Term Assessment 2024",
        "student": "Charlie Brown",
        "sections": {
            "Attitude_and_Responsibility": "He is developing good study habits and shows improvement in taking care of his belongings. He responds well to encouragement and guidance.",
            "Class_Room_Behaviour": "He follows classroom rules and participates in activities. He is learning to raise his hand before speaking and shows respect for others.",
            "Social_and_Emotional_Behaviour": "He plays well with others and is learning to share. He shows kindness to his classmates and is developing emotional regulation skills.",
            "Concept": "He is building foundational concepts in all subject areas. With continued practice and support, he shows steady progress.",
            "Subject_wise_Feedback": {
                "English_Language": "He is developing reading fluency and can identify main ideas in simple texts. His writing shows creativity though spelling needs practice.",
                "Mathematics": "He understands basic number concepts and can perform simple addition and subtraction. He enjoys hands-on math activities.",
                "Science": "He shows curiosity about the natural world and enjoys science experiments. He can make simple observations and predictions.",
                "Social_Studies": "He is learning about community helpers and different cultures. He shows interest in maps and geography activities.",
                "Art": "He expresses creativity through various art mediums and follows instructions well during art projects.",
                "Physical_Education": "He participates enthusiastically in physical activities and is developing gross motor skills."
            }
        },
        "teacher": "Mrs. Jennifer Lee",
        "grade": "Grade 2",
        "school": "Sunshine Elementary School"
    }


def test_health_endpoint():
    """Test the health endpoint."""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health Check Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to health endpoint: {e}")
        return False


def test_document_generation(request_data, filename_prefix="test"):
    """Test document generation with given request data."""
    try:
        response = requests.post(
            f"{BASE_URL}/api/generate-doc",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Document Generation Status: {response.status_code}")
        
        if response.status_code == 200:
            # Save the generated document
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{filename_prefix}_{request_data['student'].replace(' ', '_')}_{timestamp}.docx"
            
            with open(filename, "wb") as f:
                f.write(response.content)
            
            print(f"Document saved as: {filename}")
            print(f"File size: {len(response.content)} bytes")
            print(f"Content-Type: {response.headers.get('content-type')}")
            print(f"Content-Disposition: {response.headers.get('content-disposition')}")
            
            return True
        else:
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to document generation endpoint: {e}")
        return False


def run_all_tests():
    """Run all sample tests."""
    print("=" * 60)
    print("Report Card Backend Service - Sample Tests")
    print("=" * 60)
    
    # Test health endpoint
    print("\n1. Testing Health Endpoint...")
    health_ok = test_health_endpoint()
    
    if not health_ok:
        print("❌ Health check failed. Make sure the backend service is running.")
        return
    
    print("✅ Health check passed!")
    
    # Test document generation with different samples
    test_cases = [
        (sample_minimal_request(), "minimal"),
        (sample_elementary_request(), "elementary"),
        (sample_full_request(), "full"),
        (sample_high_school_request(), "highschool")
    ]
    
    for i, (request_data, prefix) in enumerate(test_cases, 2):
        print(f"\n{i}. Testing Document Generation - {prefix.title()} Request...")
        success = test_document_generation(request_data, prefix)
        
        if success:
            print(f"✅ {prefix.title()} document generation successful!")
        else:
            print(f"❌ {prefix.title()} document generation failed!")
    
    print("\n" + "=" * 60)
    print("All tests completed!")
    print("=" * 60)


if __name__ == "__main__":
    # You can run individual tests or all tests
    
    # Run all tests
    run_all_tests()
    
    # Or run individual tests:
    # test_health_endpoint()
    # test_document_generation(sample_full_request(), "example")
