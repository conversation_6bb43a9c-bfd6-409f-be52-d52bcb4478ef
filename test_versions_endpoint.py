#!/usr/bin/env python3
"""
Test the versions endpoint to verify it's working.
"""

import asyncio
import aiohttp
import json

async def test_versions_endpoint():
    """Test the versions endpoint."""
    print("🧪 Testing versions endpoint...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test health endpoint first
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    print("✅ Backend health check passed")
                else:
                    print(f"❌ Backend health check failed: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Cannot connect to backend: {e}")
            print("   Make sure the backend is running on port 8000")
            return
        
        try:
            # Test versions endpoint (without authentication for now)
            async with session.get('http://localhost:8000/api/versions/') as response:
                print(f"📡 Versions endpoint status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Versions endpoint working!")
                    print(f"   Found {len(data)} versions:")
                    
                    if len(data) == 0:
                        print("   📝 No versions found - database might be empty")
                    else:
                        for version in data:
                            status = "🟢 Current" if version.get('is_current') else ("🔵 Published" if version.get('is_published') else "🟡 Draft")
                            print(f"      - {version.get('version_name', 'Unknown')} ({status})")
                else:
                    error_text = await response.text()
                    print(f"❌ Versions endpoint failed: {response.status}")
                    print(f"   Error: {error_text}")
                    
        except Exception as e:
            print(f"❌ Error testing versions endpoint: {e}")

if __name__ == "__main__":
    asyncio.run(test_versions_endpoint())
