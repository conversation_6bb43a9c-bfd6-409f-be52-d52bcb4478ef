# 🔧 Troubleshooting Guide

This guide helps you resolve common issues with the Report Card System.

## 🚀 Quick Fixes

### 1. **Complete Reset** (Nuclear Option)
If everything seems broken, start fresh:

```bash
# Stop all services
pkill -f "uvicorn\|npm\|serve"

# Clean everything
rm -rf backend/venv backend/__pycache__ backend/report_card.db
rm -rf report-card-app/node_modules report-card-app/dist

# Run setup again
./setup.sh
```

### 2. **Backend Issues**

**Backend won't start:**
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
python -c "from app.main import app; print('✅ Backend OK')"
```

**Database errors:**
```bash
cd backend
rm report_card.db  # Delete existing database
python run_migration.py  # Recreate with fresh data
```

**Missing dependencies:**
```bash
cd backend
source venv/bin/activate
pip install greenlet  # Common missing dependency
pip install -r requirements.txt
```

### 3. **Frontend Issues**

**Frontend won't start:**
```bash
cd report-card-app
rm -rf node_modules package-lock.json
npm install
npm run dev
```

**Build errors:**
```bash
cd report-card-app
npm run build
# Check for any error messages
```

## 🐛 Common Issues

### Issue: "Module not found" errors

**Symptoms:** Python import errors, missing modules
**Solution:**
```bash
cd backend
source venv/bin/activate  # Make sure virtual environment is active
pip install -r requirements.txt
```

### Issue: "CORS errors" in browser

**Symptoms:** Network errors, blocked requests
**Solution:**
1. Check backend `.env` file has correct `ALLOWED_ORIGINS`
2. Ensure both frontend and backend are running
3. Verify URLs match in frontend `.env`

### Issue: "Database locked" or SQLite errors

**Symptoms:** Database operation failures
**Solution:**
```bash
cd backend
rm report_card.db  # Delete database
python run_migration.py  # Recreate
```

### Issue: "Port already in use"

**Symptoms:** Cannot start services on ports 8000 or 3000/5173
**Solution:**
```bash
# Kill processes using the ports
lsof -ti:8000 | xargs kill -9
lsof -ti:3000 | xargs kill -9
lsof -ti:5173 | xargs kill -9
```

### Issue: "Permission denied" on scripts

**Symptoms:** Cannot execute setup.sh or start scripts
**Solution:**
```bash
chmod +x setup.sh start-dev.sh start.sh
```

### Issue: Frontend shows "Cannot connect to backend"

**Symptoms:** API calls fail, login doesn't work
**Solution:**
1. Check backend is running: `curl http://localhost:8000/health`
2. Check frontend `.env` has correct `VITE_BACKEND_URL`
3. Restart both services

## 🔍 Diagnostic Commands

### Check Backend Status
```bash
cd backend
source venv/bin/activate
python -c "
from app.main import app
from app.config import get_settings
settings = get_settings()
print(f'✅ Backend OK - Environment: {settings.environment}')
print(f'📁 Template: {settings.template_path}')
print(f'🗄️ Database: {settings.database_url}')
"
```

### Check Frontend Status
```bash
cd report-card-app
npm run build
echo "✅ Frontend build successful"
```

### Check Database
```bash
cd backend
source venv/bin/activate
python -c "
import asyncio
from app.database import init_database
asyncio.run(init_database())
print('✅ Database connection OK')
"
```

### Test API Endpoints
```bash
# Health check
curl http://localhost:8000/health

# Login test
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}'
```

## 📋 Environment Verification

### Required Software Versions
- **Python:** 3.9+ (recommended: 3.11+)
- **Node.js:** 18+ (recommended: 20+)
- **npm:** 8+ (recommended: 10+)

### Check Versions
```bash
python3 --version
node --version
npm --version
```

### Environment Files
Ensure these files exist with correct content:

**backend/.env:**
```env
TEMPLATE_PATH=templates/report_template.docx
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
DATABASE_URL=sqlite+aiosqlite:///./report_card.db
JWT_SECRET_KEY=your-secret-key
```

**report-card-app/.env:**
```env
VITE_BACKEND_URL=http://localhost:8000
```

## 🔧 Advanced Troubleshooting

### Enable Debug Mode
1. **Backend:** Set `DEBUG=true` in `backend/.env`
2. **Frontend:** Set `VITE_DEBUG=true` in `report-card-app/.env`

### Check Logs
```bash
# Backend logs (if running in terminal)
cd backend
source venv/bin/activate
uvicorn app.main:app --log-level debug

# Frontend logs (check browser console)
# Open browser dev tools (F12) and check Console tab
```

### Network Issues
```bash
# Check if ports are available
netstat -an | grep :8000
netstat -an | grep :3000
netstat -an | grep :5173

# Test local connectivity
curl -v http://localhost:8000/health
```

### Database Issues
```bash
cd backend
# Check database file exists and is readable
ls -la report_card.db
file report_card.db

# Test database connection
python -c "
import sqlite3
conn = sqlite3.connect('report_card.db')
cursor = conn.cursor()
cursor.execute('SELECT name FROM sqlite_master WHERE type=\"table\";')
tables = cursor.fetchall()
print(f'Tables: {tables}')
conn.close()
"
```

## 🆘 Getting Help

If you're still having issues:

1. **Check the error messages** carefully - they often contain the solution
2. **Search the error message** online for similar issues
3. **Try the complete reset** procedure above
4. **Check system requirements** and versions

### Common Error Patterns

**"ModuleNotFoundError"** → Missing Python dependencies
**"ENOENT"** → Missing files or incorrect paths
**"EADDRINUSE"** → Port already in use
**"CORS"** → Cross-origin request blocked
**"sqlite3.OperationalError"** → Database issues

### System-Specific Issues

**macOS:**
- May need to install Xcode command line tools: `xcode-select --install`
- Python path issues: Use `python3` instead of `python`

**Windows:**
- Use `venv\Scripts\activate` instead of `source venv/bin/activate`
- May need to enable script execution: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

**Linux:**
- May need to install additional packages: `sudo apt-get install python3-venv python3-dev`
- Check firewall settings if services can't be accessed

---

**Still stuck?** The setup script (`./setup.sh`) is designed to handle most common issues automatically. Try running it again!
