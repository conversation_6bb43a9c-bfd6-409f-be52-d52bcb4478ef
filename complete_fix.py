#!/usr/bin/env python3
"""
Complete fix for the version management system.
This script will:
1. Initialize database with sample data
2. Test authentication flow
3. Verify version management endpoints
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_path = Path(__file__).parent / "report-card-app" / "backend"
sys.path.insert(0, str(backend_path))

async def complete_fix():
    """Complete fix for version management system."""
    print("🔧 Complete Version Management System Fix")
    print("=" * 50)
    
    # Step 1: Initialize database
    print("\n📊 Step 1: Initializing database with sample data...")
    try:
        from app.database.connection import init_database, get_database
        from app.database.models import User, ConfigurationVersion
        from app.utils.security import hash_password
        from sqlalchemy import select
        
        await init_database()
        print("✅ Database connection established")
        
        async for session in get_database():
            try:
                # Create admin user
                result = await session.execute(
                    select(User).where(User.username == "admin")
                )
                admin_user = result.scalar_one_or_none()
                
                if not admin_user:
                    admin_user = User(
                        username="admin",
                        email="<EMAIL>",
                        hashed_password=hash_password("admin123"),
                        full_name="System Administrator",
                        role="admin",
                        is_active=True
                    )
                    session.add(admin_user)
                    await session.flush()
                    print(f"✅ Created admin user (ID: {admin_user.id})")
                else:
                    print(f"✅ Admin user exists (ID: {admin_user.id})")
                
                # Create sample versions
                result = await session.execute(select(ConfigurationVersion))
                existing_versions = result.scalars().all()
                
                if len(existing_versions) == 0:
                    versions_data = [
                        {
                            "name": "Initial Configuration",
                            "description": "Initial setup with default assessment criteria",
                            "published": True,
                            "current": True
                        },
                        {
                            "name": "2024-2025 Academic Year",
                            "description": "Updated configuration for the current academic year",
                            "published": True,
                            "current": False
                        },
                        {
                            "name": "Draft - New Grading System",
                            "description": "Draft configuration with competency-based grading",
                            "published": False,
                            "current": False
                        }
                    ]
                    
                    for version_data in versions_data:
                        from datetime import datetime
                        version = ConfigurationVersion(
                            version_name=version_data["name"],
                            description=version_data["description"],
                            is_published=version_data["published"],
                            is_current=version_data["current"],
                            created_by=admin_user.id,
                            created_at=datetime.utcnow(),
                            published_at=datetime.utcnow() if version_data["published"] else None
                        )
                        session.add(version)
                    
                    await session.flush()
                    print(f"✅ Created {len(versions_data)} sample versions")
                else:
                    print(f"✅ Found {len(existing_versions)} existing versions")
                
                await session.commit()
                
            except Exception as e:
                print(f"❌ Database error: {e}")
                await session.rollback()
                return False
            finally:
                break
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Step 2: Test API endpoints
    print("\n🧪 Step 2: Testing API endpoints...")
    
    async with aiohttp.ClientSession() as session:
        # Test health
        try:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    print("✅ Backend health check passed")
                else:
                    print(f"❌ Backend health check failed: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Cannot connect to backend: {e}")
            return False
        
        # Test login
        try:
            login_data = {"username": "admin", "password": "admin123"}
            async with session.post('http://localhost:8000/api/auth/login', 
                                  json=login_data) as response:
                if response.status == 200:
                    data = await response.json()
                    token = data.get('access_token')
                    print("✅ Login successful")
                    
                    # Test versions endpoint with authentication
                    headers = {'Authorization': f'Bearer {token}'}
                    async with session.get('http://localhost:8000/api/versions/', 
                                         headers=headers) as versions_response:
                        if versions_response.status == 200:
                            versions_data = await versions_response.json()
                            print(f"✅ Versions endpoint working - found {len(versions_data)} versions")
                            
                            for version in versions_data:
                                status = "🟢 Current" if version.get('is_current') else ("🔵 Published" if version.get('is_published') else "🟡 Draft")
                                print(f"      - {version.get('version_name', 'Unknown')} ({status})")
                        else:
                            error_text = await versions_response.text()
                            print(f"❌ Versions endpoint failed: {versions_response.status}")
                            print(f"   Error: {error_text}")
                            return False
                else:
                    error_text = await response.text()
                    print(f"❌ Login failed: {response.status}")
                    print(f"   Error: {error_text}")
                    return False
        except Exception as e:
            print(f"❌ API test error: {e}")
            return False
    
    # Step 3: Success summary
    print("\n🎉 Step 3: System Status")
    print("=" * 50)
    print("✅ Database initialized with sample data")
    print("✅ Authentication working correctly")
    print("✅ Version management API functional")
    print("\n🎯 Next Steps:")
    print("   1. Open http://localhost:5173 in your browser")
    print("   2. Login with: admin / admin123")
    print("   3. Navigate to: Publishing & Versions > Version Management")
    print("   4. You should see the version list with sample data")
    print("\n📝 Troubleshooting:")
    print("   - If frontend still shows 'Failed to load versions':")
    print("     * Check browser console for errors")
    print("     * Verify token is stored in localStorage")
    print("     * Try logging out and logging back in")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(complete_fix())
    if success:
        print("\n🚀 Version Management System is now fully working!")
    else:
        print("\n❌ Fix failed - please check the errors above")
        sys.exit(1)
