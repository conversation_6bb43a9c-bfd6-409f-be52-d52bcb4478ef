#!/usr/bin/env python3
"""
Test the API endpoints to verify functionality.
"""

import asyncio
import aiohttp
import json

async def test_api():
    """Test the API endpoints."""
    print("🧪 Testing API endpoints...")
    
    async with aiohttp.ClientSession() as session:
        # Test health endpoint
        try:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    print("✅ Health endpoint working")
                else:
                    print(f"❌ Health endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Health endpoint error: {e}")
        
        # Test login
        try:
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            async with session.post('http://localhost:8000/api/auth/login', 
                                  json=login_data) as response:
                if response.status == 200:
                    data = await response.json()
                    token = data.get('access_token')
                    print("✅ Login successful")
                    
                    # Test versions endpoint with token
                    headers = {'Authorization': f'Bearer {token}'}
                    async with session.get('http://localhost:8000/api/versions/', 
                                         headers=headers) as versions_response:
                        if versions_response.status == 200:
                            versions_data = await versions_response.json()
                            print(f"✅ Versions endpoint working - found {len(versions_data)} versions")
                            for version in versions_data:
                                print(f"   - {version.get('version_name', 'Unknown')} ({'Current' if version.get('is_current') else 'Published' if version.get('is_published') else 'Draft'})")
                        else:
                            print(f"❌ Versions endpoint failed: {versions_response.status}")
                            error_text = await versions_response.text()
                            print(f"   Error: {error_text}")
                else:
                    print(f"❌ Login failed: {response.status}")
                    error_text = await response.text()
                    print(f"   Error: {error_text}")
        except Exception as e:
            print(f"❌ Login/Versions test error: {e}")

if __name__ == "__main__":
    asyncio.run(test_api())
