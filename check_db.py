#!/usr/bin/env python3
"""
Check database state and create initial data if needed.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "report-card-app" / "backend"))

try:
    from app.database.connection import init_database, get_database
    from app.database.models import User, ConfigurationVersion
    from app.utils.security import hash_password
    from sqlalchemy import select
    
    async def check_and_fix_db():
        """Check database state and fix if needed."""
        print("🔍 Checking database state...")
        
        try:
            await init_database()
            print("✅ Database connection successful")
            
            async for session in get_database():
                try:
                    # Check users
                    result = await session.execute(select(User))
                    users = result.scalars().all()
                    print(f"👥 Found {len(users)} users")
                    
                    admin_user = None
                    for user in users:
                        print(f"   - {user.username} ({user.role}) - {'Active' if user.is_active else 'Inactive'}")
                        if user.username == "admin":
                            admin_user = user
                    
                    # Create admin if doesn't exist
                    if not admin_user:
                        print("👤 Creating admin user...")
                        admin_user = User(
                            username="admin",
                            email="<EMAIL>",
                            hashed_password=hash_password("admin123"),
                            full_name="System Administrator",
                            role="admin",
                            is_active=True
                        )
                        session.add(admin_user)
                        await session.flush()
                        print(f"✅ Created admin user with ID: {admin_user.id}")
                    
                    # Check versions
                    result = await session.execute(select(ConfigurationVersion))
                    versions = result.scalars().all()
                    print(f"📋 Found {len(versions)} configuration versions")
                    
                    for version in versions:
                        status = "Current" if version.is_current else ("Published" if version.is_published else "Draft")
                        print(f"   - {version.version_name} ({status})")
                    
                    # Create initial version if none exist
                    if not versions:
                        print("📝 Creating initial configuration version...")
                        version = ConfigurationVersion(
                            version_name="Initial Configuration",
                            description="Initial configuration with default settings",
                            is_published=True,
                            is_current=True,
                            created_by=admin_user.id,
                            created_at=datetime.utcnow(),
                            published_at=datetime.utcnow()
                        )
                        session.add(version)
                        await session.flush()
                        print(f"✅ Created initial version with ID: {version.id}")
                    
                    await session.commit()
                    print("✅ Database state is good!")
                    
                except Exception as e:
                    print(f"❌ Database operation error: {e}")
                    await session.rollback()
                finally:
                    break
                    
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            print("   Make sure the backend is running and database is accessible")

    if __name__ == "__main__":
        asyncio.run(check_and_fix_db())
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("   Make sure you're running this from the correct directory")
    print("   Current working directory should contain 'report-card-app/backend'")
