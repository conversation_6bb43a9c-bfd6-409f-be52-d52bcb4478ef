#!/usr/bin/env python3
"""
Complete fix for version management system.
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "report-card-app" / "backend"))

from app.database.connection import init_database, get_database
from app.database.models import User, ConfigurationVersion
from app.utils.security import hash_password
from sqlalchemy import select

async def fix_version_management():
    """Complete fix for version management system."""
    print("🔧 Fixing Version Management System...")
    
    # Initialize database
    print("📊 Initializing database...")
    await init_database()
    
    async for session in get_database():
        try:
            # 1. Ensure admin user exists
            result = await session.execute(
                select(User).where(User.username == "admin")
            )
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                print("👤 Creating admin user...")
                admin_user = User(
                    username="admin",
                    email="<EMAIL>",
                    hashed_password=hash_password("admin123"),
                    full_name="System Administrator",
                    role="admin",
                    is_active=True
                )
                session.add(admin_user)
                await session.flush()
                print(f"✅ Created admin user with ID: {admin_user.id}")
            else:
                print(f"✅ Admin user exists with ID: {admin_user.id}")
            
            # 2. Check existing versions
            result = await session.execute(select(ConfigurationVersion))
            existing_versions = result.scalars().all()
            print(f"📋 Found {len(existing_versions)} existing versions")
            
            # 3. Create initial version if none exists
            if not existing_versions:
                print("📝 Creating initial configuration version...")
                version = ConfigurationVersion(
                    version_name="Initial Configuration",
                    description="Initial configuration with default settings for the report card system",
                    is_published=True,
                    is_current=True,
                    created_by=admin_user.id,
                    created_at=datetime.utcnow(),
                    published_at=datetime.utcnow()
                )
                session.add(version)
                await session.flush()
                print(f"✅ Created initial version with ID: {version.id}")
            else:
                # Ensure at least one version is current
                current_versions = [v for v in existing_versions if v.is_current]
                if not current_versions:
                    print("🔄 Setting first version as current...")
                    existing_versions[0].is_current = True
                    existing_versions[0].is_published = True
                    if not existing_versions[0].published_at:
                        existing_versions[0].published_at = datetime.utcnow()
            
            # 4. Create a few sample versions for demonstration
            sample_versions = [
                {
                    "name": "2024-2025 Academic Year Setup",
                    "description": "Configuration for the 2024-2025 academic year with updated assessment criteria"
                },
                {
                    "name": "Draft - New Grading System",
                    "description": "Draft configuration implementing the new competency-based grading system",
                    "published": False
                }
            ]
            
            for sample in sample_versions:
                # Check if version already exists
                result = await session.execute(
                    select(ConfigurationVersion).where(
                        ConfigurationVersion.version_name == sample["name"]
                    )
                )
                if not result.scalar_one_or_none():
                    print(f"📝 Creating sample version: {sample['name']}")
                    version = ConfigurationVersion(
                        version_name=sample["name"],
                        description=sample["description"],
                        is_published=sample.get("published", True),
                        is_current=False,
                        created_by=admin_user.id,
                        created_at=datetime.utcnow(),
                        published_at=datetime.utcnow() if sample.get("published", True) else None
                    )
                    session.add(version)
            
            await session.commit()
            
            # 5. Verify the setup
            result = await session.execute(select(ConfigurationVersion))
            all_versions = result.scalars().all()
            
            print("\n📊 Version Management System Status:")
            print(f"   Total Versions: {len(all_versions)}")
            for version in all_versions:
                status = "🟢 Current" if version.is_current else ("🔵 Published" if version.is_published else "🟡 Draft")
                print(f"   - {version.version_name} ({status})")
            
            print("\n✅ Version Management System is now fully working!")
            print("\n🎯 Next Steps:")
            print("   1. Open http://localhost:5173 in your browser")
            print("   2. Login with admin/admin123")
            print("   3. Navigate to Publishing & Versions > Version Management")
            print("   4. You should see all versions listed")
            
        except Exception as e:
            print(f"❌ Error during setup: {e}")
            await session.rollback()
            raise
        finally:
            break

if __name__ == "__main__":
    asyncio.run(fix_version_management())
