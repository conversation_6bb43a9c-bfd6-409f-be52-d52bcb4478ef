# 🚀 Quick Start Guide

Get the Report Card System running in under 5 minutes!

## ⚡ Super Quick Start

```bash
# 1. Navigate to the project directory
cd "Report Card/Website"

# 2. Run the automated setup (one-time only)
./setup.sh

# 3. Start the system
./start-dev.sh
```

**That's it!** 🎉

- **Frontend:** http://localhost:5173
- **Backend:** http://localhost:8000
- **API Docs:** http://localhost:8000/docs

**Login:** `admin` / `admin` or `teacher` / `teacher`

---

## 📋 Prerequisites

- **Python 3.9+** (recommended: 3.11+)
- **Node.js 18+** (recommended: 20+)
- **npm** (comes with Node.js)

### Check if you have them:
```bash
python3 --version  # Should show 3.9+
node --version     # Should show v18+
npm --version      # Should show 8+
```

---

## 🛠️ Detailed Setup

### 1. **First Time Setup**
```bash
# Make setup script executable (if needed)
chmod +x setup.sh

# Run the automated setup
./setup.sh
```

The setup script will:
- ✅ Check prerequisites
- ✅ Create Python virtual environment
- ✅ Install all dependencies
- ✅ Initialize database
- ✅ Configure environment files
- ✅ Build frontend
- ✅ Create start scripts

### 2. **Start the System**

**Development Mode** (with hot reload):
```bash
./start-dev.sh
```

**Production Mode** (optimized):
```bash
./start.sh
```

### 3. **Verify Everything Works**
```bash
./verify-setup.sh
```

---

## 🌐 Access the Application

### URLs
- **Main App:** http://localhost:5173 (dev) or http://localhost:3000 (prod)
- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Health Check:** http://localhost:8000/health

### Default Accounts
| Role | Username | Password |
|------|----------|----------|
| Admin | `admin` | `admin` |
| Teacher | `teacher` | `teacher` |

---

## 🎯 First Steps After Login

### As Admin:
1. **Setup System Configuration:**
   - Navigate to **Grade Management** → Add grades (K, 1, 2, 3, etc.)
   - Go to **Subject Management** → Add subjects for each grade
   - Visit **Category Management** → Create assessment categories
   - Use **Assessment Point Management** → Define evaluation criteria

2. **Publish Configuration:**
   - Go to **Version Management** → Publish your configuration

### As Teacher:
1. **Generate Reports:**
   - Enter student information
   - Complete assessment steps
   - Generate Word document

---

## 🔧 Common Commands

```bash
# Stop all services
pkill -f "uvicorn\|npm\|serve"

# Restart development
./start-dev.sh

# Check system status
./verify-setup.sh

# Clean restart (if issues)
rm -rf backend/venv backend/__pycache__
rm -rf report-card-app/node_modules
./setup.sh
```

---

## 🐛 Troubleshooting

### Quick Fixes:

**Services won't start:**
```bash
# Kill any existing processes
pkill -f "uvicorn\|npm\|serve"
./start-dev.sh
```

**Database errors:**
```bash
cd backend
rm report_card.db
python run_migration.py
cd ..
```

**Frontend issues:**
```bash
cd report-card-app
rm -rf node_modules
npm install
npm run build
cd ..
```

**Complete reset:**
```bash
rm -rf backend/venv backend/__pycache__ backend/report_card.db
rm -rf report-card-app/node_modules report-card-app/dist
./setup.sh
```

### Need More Help?
- 📖 Read the full [README.md](README.md)
- 🔧 Check [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
- 🔍 Run `./verify-setup.sh` for diagnostics

---

## 📱 Mobile Access

The admin interface is fully responsive! Access it from:
- **Desktop:** Full feature set
- **Tablet:** Optimized layout
- **Mobile:** Touch-friendly interface with collapsible navigation

---

## 🎓 What's Next?

1. **Customize Templates:** Edit `backend/templates/report_template.docx`
2. **Configure Settings:** Modify `.env` files for your needs
3. **Add Users:** Use the admin interface to manage accounts
4. **Deploy:** Follow deployment guide in README.md

---

## 💡 Pro Tips

- **Bookmark** http://localhost:8000/docs for API reference
- **Use Ctrl+C** to stop services gracefully
- **Check logs** in terminal for debugging
- **Mobile users:** Tap hamburger menu for navigation
- **Keyboard shortcuts:** Available in admin interface

---

**Happy report card generating! 🎓✨**
