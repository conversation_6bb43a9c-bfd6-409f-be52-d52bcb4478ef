# 📦 Version Control System Guide

## Overview

The Report Card System now includes a comprehensive version control system that allows you to:
- Create and manage configuration versions
- Track changes to subjects, categories, and assessment points
- Publish stable versions for production use
- Maintain drafts for testing changes

## 🎯 How Version Control Works

### 1. **Configuration Versions**
- **Draft Versions**: Work-in-progress configurations that can be edited
- **Published Versions**: Stable configurations available for report generation
- **Current Version**: The active published version used for new reports

### 2. **Change Tracking**
When you make changes to:
- ✅ Subjects (add, edit, delete)
- ✅ Categories (add, edit, delete)  
- ✅ Assessment Points (add, edit, delete)

The system automatically:
- Tracks that you have unpublished changes
- Shows a warning indicator in the interface
- Displays a "📦 Publish Changes" button

## 🚀 Using the Version Control System

### Step 1: Making Changes
1. Navigate to any admin page (Subjects, Categories, Assessment Points)
2. Make your changes (add, edit, or delete items)
3. Notice the **yellow warning banner** appears: "⚠️ You have unpublished changes"
4. See the **pulsing "📦 Publish Changes" button** in the toolbar

### Step 2: Publishing Changes
1. Click the **"📦 Publish Changes"** button (appears when you have changes)
2. This takes you to the **Version Management** page
3. Click **"➕ Create New Version"** to create a new version with your changes
4. Fill in:
   - **Version Name**: e.g., "2024-2025 Academic Year Updates"
   - **Description**: Describe what changes this version includes
5. Click **"Create Version"** - this creates a new draft version

### Step 3: Publishing a Version
1. In the Version Management page, find your new draft version
2. Click the **"🚀 Publish"** button next to the draft
3. This makes the version:
   - **Published**: Available for use in reports
   - **Current**: The active version for new reports

## 📋 Version Management Interface

### Version States
- **🟡 Draft**: Work-in-progress, can be edited
- **🔵 Published**: Stable, cannot be modified
- **🟢 Current**: The active published version

### Available Actions
- **🚀 Publish**: Convert a draft to published and make it current
- **📋 Duplicate**: Create a copy of any version as a new draft
- **➕ Create New Version**: Start a fresh draft version

## 🔄 Recommended Workflow

### For Regular Updates:
1. **Make Changes**: Edit subjects, categories, or assessment points
2. **Test Changes**: Verify everything looks correct
3. **Create Version**: Use "Publish Changes" → "Create New Version"
4. **Publish**: Click "🚀 Publish" to make it live

### For Major Changes:
1. **Create Draft First**: Go to Version Management → "Create New Version"
2. **Make Changes**: Work on your draft configuration
3. **Test Thoroughly**: Ensure all changes work as expected
4. **Publish When Ready**: Click "🚀 Publish" to go live

### For Experimental Changes:
1. **Duplicate Current**: Use "📋 Duplicate" on current version
2. **Experiment**: Make experimental changes to the duplicate
3. **Publish or Discard**: Either publish if successful, or abandon if not

## ⚠️ Important Notes

### What Triggers Change Tracking:
- ✅ Adding new subjects, categories, or assessment points
- ✅ Editing existing items
- ✅ Deleting items
- ✅ Changing sort orders or settings

### What Doesn't Trigger Changes:
- ❌ Just viewing pages
- ❌ Searching or filtering
- ❌ Refreshing data

### Version Limitations:
- 🚫 Published versions cannot be edited
- 🚫 Current version cannot be deleted
- ✅ Draft versions can be edited and deleted

## 🎨 Visual Indicators

### Status Indicators:
- **Blue Banner**: Shows current version name
- **Yellow Banner**: Warns about unpublished changes
- **Pulsing Button**: "📦 Publish Changes" when changes exist

### Version Timeline:
- Versions are displayed in chronological order
- Current version has a **gold left border**
- Each version shows creation date and creator

## 🔧 Troubleshooting

### "Failed to load versions" Error:
1. Check that you're logged in as an admin
2. Refresh the page
3. Check browser console for errors

### Changes Not Being Tracked:
1. Ensure you're making actual changes (not just viewing)
2. Check that the save operation completed successfully
3. Look for error messages in the interface

### Cannot Publish Version:
1. Ensure you have admin privileges
2. Check that the version is in draft state
3. Verify no validation errors exist

## 📞 Support

If you encounter issues with the version control system:
1. Check this guide first
2. Look for error messages in the interface
3. Check browser developer console for technical errors
4. Contact system administrator if problems persist

---

**🎉 Congratulations!** You now have a fully functional version control system that helps you manage configuration changes safely and efficiently.
