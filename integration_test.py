#!/usr/bin/env python3
"""
Integration test script for the Report Card Backend Service.

This script tests the complete flow from JSON input to Word document generation.
"""

import os
import sys
import json
import time
import requests
import subprocess
from pathlib import Path


def check_backend_health(base_url="http://localhost:8000", timeout=30):
    """Check if the backend service is healthy."""
    print("🔍 Checking backend health...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Backend is healthy!")
                print(f"   Status: {health_data.get('status')}")
                print(f"   Template loaded: {health_data.get('template_loaded')}")
                print(f"   Version: {health_data.get('version')}")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print("⏳ Waiting for backend to start...")
        time.sleep(2)
    
    print("❌ Backend health check failed")
    return False


def test_document_generation(base_url="http://localhost:8000"):
    """Test document generation with sample data."""
    print("\n📄 Testing document generation...")
    
    # Sample request data
    sample_data = {
        "assessment": "Integration Test Assessment 2024",
        "student": "Test Student",
        "sections": {
            "Attitude_and_Responsibility": "Shows excellent attitude towards learning and demonstrates responsibility in all tasks.",
            "Class_Room_Behaviour": "Maintains good classroom discipline and participates actively in discussions.",
            "Social_and_Emotional_Behaviour": "Interacts well with peers and shows emotional maturity.",
            "Concept": "Has clear understanding of concepts and can apply them effectively.",
            "Subject_wise_Feedback": {
                "English_Language": "Excellent reading comprehension and writing skills.",
                "Mathematics": "Strong problem-solving abilities and accurate calculations.",
                "Science": "Shows curiosity and good understanding of scientific concepts."
            }
        },
        "teacher": "Integration Test Teacher",
        "grade": "Test Grade",
        "school": "Integration Test School"
    }
    
    try:
        print(f"📤 Sending request to {base_url}/api/generate-doc...")
        response = requests.post(
            f"{base_url}/api/generate-doc",
            json=sample_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Document generation successful!")
            
            # Check response headers
            content_type = response.headers.get('content-type')
            content_disposition = response.headers.get('content-disposition')
            
            print(f"   Content-Type: {content_type}")
            print(f"   Content-Disposition: {content_disposition}")
            print(f"   File size: {len(response.content)} bytes")
            
            # Save the generated document
            timestamp = int(time.time())
            filename = f"integration_test_report_{timestamp}.docx"
            
            with open(filename, "wb") as f:
                f.write(response.content)
            
            print(f"   📁 Document saved as: {filename}")
            
            # Verify it's a valid DOCX file (ZIP format)
            if response.content[:2] == b'PK':
                print("✅ Generated file appears to be a valid DOCX document")
                return True
            else:
                print("❌ Generated file does not appear to be a valid DOCX document")
                return False
                
        else:
            print(f"❌ Document generation failed: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
                if 'details' in error_data:
                    print(f"   Details: {error_data['details']}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False


def test_frontend_backend_integration():
    """Test the complete frontend-backend integration."""
    print("\n🔗 Testing frontend-backend integration...")
    
    # Check if frontend is running
    try:
        response = requests.get("http://localhost:5173", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running on http://localhost:5173")
        else:
            print("⚠️  Frontend may not be running on http://localhost:5173")
    except requests.exceptions.RequestException:
        print("⚠️  Frontend is not accessible on http://localhost:5173")
        print("   To start frontend: cd report-card-app && npm run dev")
    
    # Test CORS
    try:
        response = requests.options(
            "http://localhost:8000/api/generate-doc",
            headers={
                "Origin": "http://localhost:5173",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=5
        )
        
        if response.status_code in [200, 204]:
            print("✅ CORS preflight request successful")
        else:
            print(f"⚠️  CORS preflight failed: HTTP {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ CORS test failed: {e}")


def run_backend_tests():
    """Run the backend test suite."""
    print("\n🧪 Running backend test suite...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    try:
        # Change to backend directory
        original_dir = os.getcwd()
        os.chdir(backend_dir)
        
        # Run pytest
        result = subprocess.run(
            ["python", "-m", "pytest", "-v", "--tb=short"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ All backend tests passed!")
            print(result.stdout)
            return True
        else:
            print("❌ Some backend tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Backend tests timed out")
        return False
    except Exception as e:
        print(f"❌ Error running backend tests: {e}")
        return False
    finally:
        os.chdir(original_dir)


def main():
    """Main integration test function."""
    print("🚀 Report Card Backend Service - Integration Test")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("backend").exists():
        print("❌ Please run this script from the 'Report Card/Website' directory")
        return 1
    
    # Test results
    results = []
    
    # 1. Check backend health
    backend_healthy = check_backend_health()
    results.append(("Backend Health", backend_healthy))
    
    if not backend_healthy:
        print("\n❌ Backend is not running. Please start it with:")
        print("   cd backend")
        print("   uvicorn app.main:app --reload")
        return 1
    
    # 2. Test document generation
    doc_generation_ok = test_document_generation()
    results.append(("Document Generation", doc_generation_ok))
    
    # 3. Test frontend-backend integration
    test_frontend_backend_integration()
    
    # 4. Run backend tests
    tests_passed = run_backend_tests()
    results.append(("Backend Tests", tests_passed))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:.<40} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 ALL TESTS PASSED! The integration is working correctly.")
        print("\n🚀 Next Steps:")
        print("   1. Open http://localhost:5173 in your browser")
        print("   2. Fill out the report card form")
        print("   3. Click 'Generate Word Document' to test the full flow")
        print("   4. Deploy to production when ready")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit(main())
